{
  "parser": "@babel/eslint-parser",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "plugins": [
    "prettier",
    "simple-import-sort",
    "unused-imports"
  ],
  "extends": [
    "react-app",
    "plugin:prettier/recommended"
  ],
  "overrides": [
    // override "simple-import-sort" config
    {
      "files": ["*.js", "*.jsx"],
      "rules": {
        "simple-import-sort/imports": [
          "error",
          {
            "groups": [
              // Packages `react` related packages come first.
              ["^react", "^@?\\w"],
              // Internal packages.
              ["^(@|components|containers|config|apis|utils)(/.*|$)"],
              // Side effect imports.
              ["^\\u0000"],
              // Parent imports. Put `..` last.
              ["^\\.\\.(?!/?$)", "^\\.\\./?$"],
              // Other relative imports. Put same-folder imports and `.` last.
              ["^\\./(?=.*/)(?!/?$)", "^\\.(?!/?$)", "^\\./?$"],
              // Style imports.
              ["^.+\\.?(css)$"]
            ]
          }
        ]
      }
    }
  ],
  "rules": {
    "prettier/prettier": "error",
    "eqeqeq": [
      2,
      "smart"
    ],
    "import/no-webpack-loader-syntax": [
      "off"
    ],
    "react/self-closing-comp": "error",
    "no-restricted-globals": 0,
    "no-unused-vars": [
      "off"
    ],
    "jsx-a11y/href-no-hash": 0,
    "jsx-a11y/alt-text": 0,
    "jsx-a11y/anchor-is-valid": 0,
    "import/no-anonymous-default-export": 0,
    "simple-import-sort/imports": "error",
    "simple-import-sort/exports": "error",
    "unused-imports/no-unused-imports": "error"
  },
  "settings": {
    "react": {
      "version": "15.5.4"
    }
  },
  "globals": {
    "window": true,
    "document": true,
    "lottTranslator": true,
    "FormData": true,
    "FileReader": true,
    "CryptoJS": true,
    "location": true,
    "history": true,
    "reRsa": true,
    "webkit": true,
    "detector": true,
    "ActiveXObject": true
  }
}
