module.exports = {
  types: [
    { value: "feat", name: "feat: 一個新的特性" },
    { value: "fix", name: "fix: 修復一個Bug" },
    { value: "chore", name: "chore: 構建過程或輔助工具的變動" },
    { value: "refactor", name: "refactor: 代碼重構，注意和特性丶修復區分開" },
    { value: "test", name: "test: 添加一個測試, 或測試代碼" },
    { value: "revert", name: "revert: 代碼回退" },
  ],
  // override the messages, defaults are as follows
  messages: {
    type: "選擇一種你的提交類型 Select the type:",
    // used if allowCustomScopes is true
    subject: "標題 Write a SHORT, IMPERATIVE tense description of the change:\n",
    body: '內文，使用"|"換行(可選) LONGER description (optional). Use "|" to break new line：\n',
    confirmCommit: "確定提交說明 Commit above?",
  },
  allowTicketNumber: true,
  isTicketNumberRequired: false,
  ticketNumberPrefix: "TCG-",
  ticketNumberRegExp: "\\d{1,6}",
  allowCustomScopes: true,
  skipQuestions: ["scope", "footer"],
  subjectLimit: 50,
};
