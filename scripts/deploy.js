const fs = require("fs-extra");
const shell = require("shelljs");

// First Copy final build file to phonegap's WWW directory.
// Use the Sync way.
try {
  fs.copySync("./build", "./appshell/www");
  console.log("success copy the build files to phonegap www directory!");
} catch (err) {
  console.error(err);
}

// check PhoneGap is already installed.
if (!shell.which("PhoneGap")) {
  shell.echo(
    'Sorry, this script requires PhoneGap Cli, Install it with "npm install -g phonegap"'
  );
  shell.exit(1);
}

// serve a phonegap project
shell.cd("appshell");
shell.exec("PhoneGap --verbose serve");
