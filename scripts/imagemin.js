const imagemin = require("imagemin");
const imageminJpegtran = require("imagemin-jpegtran");
const imageminPngquant = require("imagemin-pngquant");

function imageMinAction(path) {
  console.log(`[imagemin] ${path} start...`);
  imagemin([`${path}/*.{jpg,png}`], {
    destination: path,
    plugins: [
      imageminJpegtran(),
      imageminPngquant({
        quality: [0.65, 0.9],
        speed: 4,
      }),
    ],
  }).then(() => {
    console.log(`[imagemin] ${path} done`);
  });
}

imageMinAction("build/splash");
imageMinAction("build/offline");
