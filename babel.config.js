module.exports = {
  presets: [
    [
      "@babel/preset-env",
      {
        modules: false,
        useBuiltIns: "usage",
        corejs: {
          version: "3",
          proposals: true,
        },
      },
    ],
    "@babel/preset-react",
  ],
  plugins: [
    [
      "import",
      {
        libraryName: "antd-mobile",
        style: "css",
      },
    ],
    ["@babel/plugin-proposal-decorators", { legacy: true }],
  ],
  env: {
    development: {
      // plugins: ["react-hot-loader/babel"],
    },
  },
  comments: true,
};
