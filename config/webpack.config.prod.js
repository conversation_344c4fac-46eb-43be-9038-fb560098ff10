const UglifyJsPlugin = require("uglifyjs-webpack-plugin");
const autoprefixer = require("autoprefixer");
const path = require("path");
const webpack = require("webpack");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const HtmlWebpackExcludeAssetsPlugin = require("html-webpack-exclude-assets-plugin");
const ExtractTextPlugin = require("extract-text-webpack-plugin");
const ManifestPlugin = require("webpack-manifest-plugin");
const InterpolateHtmlPlugin = require("react-dev-utils/InterpolateHtmlPlugin");
const eslintFormatter = require("react-dev-utils/eslintFormatter");
const ModuleScopePlugin = require("react-dev-utils/ModuleScopePlugin");
const paths = require("./paths");
const getClientEnvironment = require("./env");
const pxtorem = require("postcss-pxtorem");
const InlineManifestWebpackPlugin = require("inline-manifest-webpack-plugin");
const { InjectManifest } = require("workbox-webpack-plugin");
// const HappyPack = require("happypack");

// https://www.npmjs.com/package/webpack-bundle-analyzer
const BundleAnalyzerPlugin = require("webpack-bundle-analyzer").BundleAnalyzerPlugin;

// Webpack uses `publicPath` to determine where the app is being served from.
// It requires a trailing slash, or the file assets will get an incorrect path.
const publicPath = paths.servedPath;
// Some apps do not use client-side routing with pushState.
// For these, "homepage" can be set to "." to enable relative asset paths.
const shouldUseRelativeAssetPaths = publicPath === "./";
// `publicUrl` is just like `publicPath`, but we will provide it to our app
// as %PUBLIC_URL% in `index.html` and `process.env.PUBLIC_URL` in JavaScript.
// Omit trailing slash as %PUBLIC_URL%/xyz looks better than %PUBLIC_URL%xyz.
const publicUrl = publicPath.slice(0, -1);
// Get environment variables to inject into our app.
const env = getClientEnvironment(publicUrl);

// Assert this just to be safe.
// Development builds of React are slow and not intended for production.
if (env.stringified["process.env"].NODE_ENV !== '"production"') {
  throw new Error("Production builds must have NODE_ENV=production.");
}

// Note: defined here because it will be used more than once.
const cssFilename = "[name].[contenthash:8].css";

// ExtractTextPlugin expects the build output to be flat.
// (See https://github.com/webpack-contrib/extract-text-webpack-plugin/issues/27)
// However, our output is structured with css, js and media folders.
// To have this structure working with relative paths, we have to use custom options.
const extractTextPluginOptions = shouldUseRelativeAssetPaths
  ? // Making sure that the publicPath goes back to to build folder.
    { publicPath: Array(cssFilename.split("/").length).join("../") }
  : {};
const runtimeChunk = new webpack.optimize.CommonsChunkPlugin({
  name: "manifest",
  minChunks: Infinity,
});

const chunkCommon = new webpack.optimize.CommonsChunkPlugin({
  name: "chunk-common",
  minChunks: (module, count) => {
    const firebase = module.resource && module.resource.includes("node_modules/@firebase");
    const tcg = module.resource && module.resource.includes("node_modules/tcg");
    return count >= 3 || firebase || tcg;
  },
});

// This is the production configuration.
// It compiles slowly and is focused on producing a fast and minimal bundle.
// The development configuration is different and lives in a separate file.
module.exports = {
  // Don't attempt to continue if there are any errors.
  bail: true,
  // We generate sourcemaps in production. This is slow but gives good results.
  // You can exclude the *.map files from the build during deployment.
  devtool: "none",
  // In production, we only want to load the polyfills and the app code.
  entry: {
    app: [paths.appIndexJs],
    splash: ["./src/stylesheets/splash.css"],
  },
  output: {
    // The build folder.
    path: paths.appBuild,
    // Generated JS file names (with nested folders).
    // There will be one main bundle, and one file per asynchronous chunk.
    // We don't currently advertise code splitting but Webpack supports it.
    filename: "[name].[chunkhash:8].js",
    chunkFilename: "[name].[chunkhash:8].chunk.js",
    // We inferred the "public path" (such as / or /my-project) from homepage.
    publicPath: publicPath,
    // Point sourcemap entries to original disk location
    devtoolModuleFilenameTemplate: (info) => path.relative(paths.appSrc, info.absoluteResourcePath),
  },
  resolve: {
    // This allows you to set a fallback for where Webpack should look for modules.
    // We placed these paths second because we want `node_modules` to "win"
    // if there are any conflicts. This matches Node resolution mechanism.
    // https://github.com/facebookincubator/create-react-app/issues/253
    modules: ["app", path.join(__dirname, "../node_modules"), "node_modules", paths.appNodeModules].concat(
      // It is guaranteed to exist because we tweak it in `env.js`
      process.env.NODE_PATH.split(path.delimiter).filter(Boolean)
    ),
    // These are the reasonable defaults supported by the Node ecosystem.
    // We also include JSX as a common component filename extension to support
    // some tools, although we do not recommend using it, see:
    // https://github.com/facebookincubator/create-react-app/issues/290
    extensions: [".js", ".json", ".jsx", ".web.tsx", ".web.ts", ".web.jsx", ".web.js", ".ts", ".tsx", ".react.js"],
    alias: {
      ...paths.alias,
    },
    plugins: [
      // Prevents users from importing files from outside of src/ (or node_modules/).
      // This often causes confusion because we only process files within src/ with babel.
      // To fix this, we prevent you from importing files out of src/ -- if you'd like to,
      // please link the files into your node_modules/ and let module-resolution kick in.
      // Make sure your source files are compiled, as they will not be processed in any way.
      new ModuleScopePlugin(paths.appSrc),
    ],
    mainFiles: ["index.web", "index"],
    mainFields: ["esm5", "browser", "jsnext:main", "module", "main"],
  },
  module: {
    strictExportPresence: true,
    rules: [
      // TODO: Disable require.ensure as it's not a standard language feature.
      // We are waiting for https://github.com/facebookincubator/create-react-app/issues/2176.
      // { parser: { requireEnsure: false } },

      // First, run the linter.
      // It's important to do this before Babel processes the JS.
      {
        test: /\.(js|jsx)$/,
        enforce: "pre",
        use: [
          {
            options: {
              formatter: eslintFormatter,
            },
            loader: require.resolve("eslint-loader"),
          },
        ],
        include: paths.appSrc,
      },
      {
        test: /\.(js|jsx)$/,
        loader: "string-replace-loader",
        include: paths.appSrc,
        options: {
          search: "!svg-sprite-loader!",
          replace: "!svg-sprite-loader?name=%5Bname%5D_%5Bhash%3A8%5D!",
          flags: "gi",
        },
      },
      // ** ADDING/UPDATING LOADERS **
      // The "file" loader handles all assets unless explicitly excluded.
      // The `exclude` list *must* be updated with every change to loader extensions.
      // When adding a new loader, you must add its `test`
      // as a new entry in the `exclude` list in the "file" loader.

      // "file" loader makes sure those assets end up in the `build` folder.
      // When you `import` an asset, you get its filename.
      {
        exclude: [
          /\.html$/,
          /\.(js|jsx)$/,
          /\.css$/,
          /\.svg$/,
          /\.json$/,
          /\.bmp$/,
          /\.gif$/,
          /\.jpe?g$/,
          /\.png$/,
          /\.scss$/,
        ],
        loader: require.resolve("file-loader"),
        options: {
          name: "[name].[hash:8].[ext]",
        },
      },
      // "url" loader works just like "file" loader but it also embeds
      // assets smaller than specified size as data URLs to avoid requests.
      {
        test: [/\.bmp$/, /\.gif$/, /\.jpe?g$/, /\.png$/, /\.svg$/],
        exclude: [paths.svgIcons, require.resolve("antd-mobile").replace(/warn\.js$/, "")],
        oneOf: [
          {
            resourceQuery: /spec/,
            use: [
              {
                loader: "url-loader",
                options: {
                  name: "[name].[hash:8].[ext]",
                },
              },
            ],
          },
          {
            use: [
              {
                loader: "url-loader",
                options: {
                  limit: 1024 * 2,
                  name: "[name].[hash:8].[ext]",
                },
              },
            ],
          },
        ],
      },
      {
        test: /\.scss$/,
        use: ExtractTextPlugin.extract(
          Object.assign(
            {
              fallback: "style-loader",
              use: [
                {
                  loader: require.resolve("css-loader"),
                  options: {
                    importLoaders: 1,
                    minimize: true,
                    sourceMap: false,
                  },
                },
                {
                  loader: require.resolve("postcss-loader"),
                  options: {
                    ident: "postcss", // https://webpack.js.org/guides/migrating/#complex-options
                    plugins: () => [
                      require("postcss-flexbugs-fixes"),
                      pxtorem({ rootValue: 100, propList: ["*"], minPixelValue: "2", mediaQuery: false }),
                      autoprefixer({
                        flexbox: "no-2009",
                        remove: false,
                      }),
                    ],
                  },
                },
                "sass-loader",
                {
                  loader: "sass-resources-loader",
                  options: {
                    resources: ["src/stylesheets/global/*.scss", "src/stylesheets/mixin/*.scss"],
                  },
                },
              ],
            },
            extractTextPluginOptions
          )
        ),
      },
      // Process JS with Babel.
      {
        test: /\.(js|jsx)$/,
        include: [paths.appSrc, path.join(paths.appNodeModules, "idb")],
        // use: "happypack/loader?id=react"
        use: {
          loader: require.resolve("babel-loader"),
          options: {
            plugins: [
              [
                "import",
                {
                  libraryName: "antd-mobile",
                  style: "css",
                },
              ],
            ],
            // This is a feature of `babel-loader` for webpack (not Babel itself).
            // It enables caching results in ./node_modules/.cache/babel-loader/
            // directory for faster rebuilds.
            cacheDirectory: false,
          },
        },
      },
      // The notation here is somewhat confusing.
      // "postcss" loader applies autoprefixer to our CSS.
      // "css" loader resolves paths in CSS and adds assets as dependencies.
      // "style" loader normally turns CSS into JS modules injecting <style>,
      // but unlike in development configuration, we do something different.
      // `ExtractTextPlugin` first applies the "postcss" and "css" loaders
      // (second argument), then grabs the result CSS and puts it into a
      // separate file in our build process. This way we actually ship
      // a single CSS file in production instead of JS code injecting <style>
      // tags. If you use code splitting, however, any async bundles will still
      // use the "style" loader inside the async code so CSS from them won't be
      // in the main CSS file.
      {
        test: /\.css$/,
        loader: ExtractTextPlugin.extract(
          Object.assign(
            {
              fallback: require.resolve("style-loader"),
              use: [
                {
                  loader: require.resolve("css-loader"),
                  options: {
                    importLoaders: 1,
                    minimize: true,
                    sourceMap: false,
                  },
                },
                {
                  loader: require.resolve("postcss-loader"),
                  options: {
                    ident: "postcss", // https://webpack.js.org/guides/migrating/#complex-options
                    plugins: () => [
                      require("postcss-flexbugs-fixes"),
                      pxtorem({ rootValue: 100, propList: ["*"], minPixelValue: "2", mediaQuery: false }),
                      autoprefixer({
                        flexbox: "no-2009",
                        remove: false,
                      }),
                    ],
                  },
                },
              ],
            },
            extractTextPluginOptions
          )
        ),
        // Note: this won't work without `new ExtractTextPlugin()` in `plugins`.
      },
      {
        test: /\.(svg)$/i,
        loader: "svg-sprite-loader",
        include: [
          require.resolve("antd-mobile").replace(/warn\.js$/, ""), // antd-mobile 内置svg
          paths.svgIcons, // 业务代码本地私有 svg 存放目录
        ],
      },
      // ** STOP ** Are you adding a new loader?
      // Remember to add the new extension(s) to the "file" loader exclusion list.
    ],
  },
  plugins: [
    chunkCommon,
    runtimeChunk,
    new InlineManifestWebpackPlugin(),
    // new BundleAnalyzerPlugin({ analyzerPort: 1234 }),
    // new HappyPack({
    //   id: "react",
    //   threads: 4,
    //   loaders: ["babel-loader?cacheDirectory"]
    // }),
    // Makes some environment variables available in index.html.
    // The public URL is available as %PUBLIC_URL% in index.html, e.g.:
    // <link rel="shortcut icon" href="%PUBLIC_URL%/favicon.ico">
    // In production, it will be an empty string unless you specify "homepage"
    // in `package.json`, in which case it will be the pathname of that URL.
    new InterpolateHtmlPlugin(env.raw),
    // Generates an `index.html` file with the <script> injected.
    new HtmlWebpackPlugin({
      inject: true,
      template: paths.appHtml,
      chunks: ["splash", "vendor", "app", "chunk-common"],
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeRedundantAttributes: true,
        useShortDoctype: true,
        removeEmptyAttributes: true,
        removeStyleLinkTypeAttributes: true,
        keepClosingSlash: true,
        minifyJS: true,
        minifyCSS: true,
        minifyURLs: true,
      },
      excludeAssets: [/splash.*.js/],
    }),
    new HtmlWebpackExcludeAssetsPlugin(),
    new InjectManifest({
      importWorkboxFrom: "local",
      swSrc: path.join(__dirname, "../src/sw.js"),
      swDest: "sw.js",
      modifyURLPrefix: { "": `${publicUrl}/` },
      globDirectory: "build",
      globPatterns: [
        "vendor.encrypt.v2.dll.js",
        "!(lottery|workbox*)/**/*.{html,ico,js,css,png,svg,jpg,jpeg,gif,webp,json,ttf,eot,woff,svg}",
      ],
    }),
    // Makes some environment variables available to the JS code, for example:
    // if (process.env.NODE_ENV === 'production') { ... }. See `./env.js`.
    // It is absolutely essential that NODE_ENV was set to production here.
    // Otherwise React will be compiled in the very slow development mode.
    new webpack.DefinePlugin(env.stringified),
    // Minify the code.
    new UglifyJsPlugin({
      cache: true,
      parallel: true,
      uglifyOptions: {
        output: {
          comments: false,
          beautify: false,
        },
        compress: {
          comparisons: false,
        },
      },
      sourceMap: false,
    }),
    // Note: this won't work without ExtractTextPlugin.extract(..) in `loaders`.
    new ExtractTextPlugin({
      filename: cssFilename,
      allChunks: true,
    }),
    // Generate a manifest file which contains a mapping of all asset filenames
    // to their corresponding output file so that tools can pick it up without
    // having to parse `index.html`.
    new ManifestPlugin({
      fileName: "asset-manifest.json",
    }),
    new webpack.HashedModuleIdsPlugin(),
    new webpack.DllReferencePlugin({
      context: __dirname,
      manifest: require("./vendor-manifest.json"),
    }),
  ],
  // Some libraries import Node modules but don't use them in the browser.
  // Tell Webpack to provide empty mocks for them so importing them works.
  node: {
    fs: "empty",
    net: "empty",
    tls: "empty",
  },
  externals: {
    tcgmodulemc: "tcgmodulemc",
    "tcg-module-mc": "tcgmodulemc",
  },
};
