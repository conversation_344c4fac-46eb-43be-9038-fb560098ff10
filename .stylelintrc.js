module.exports = {
  extends: ["stylelint-config-standard-scss", "stylelint-config-recess-order"],
  plugins: ["stylelint-order"],
  overrides: [
    {
      files: ["**/*.(scss|css|html)"],
      customSyntax: "postcss-scss",
    },
    {
      files: ["**/*.(html)"],
      customSyntax: "postcss-html",
    },
  ],
  rules: {
    // null 禁用部分規則
    "block-no-empty": null, // 禁止空塊
    "selector-class-pattern": null, // 指定類選擇器的模式
    "selector-id-pattern": null,
    "max-nesting-depth": [6, { ignore: ["pseudo-classes"], ignoreRules: ["svg", "img"] }], // 限制嵌套深度
    "selector-max-compound-selectors": [6, { ignoreSelectors: ["svg", "img"] }], // 限制選擇器中復合選擇器的數量
    "function-url-quotes": null, // 要求或不允許網址加引號
    "property-no-unknown": null, // 禁止未知屬性
    "no-empty-source": null, // 禁止空源
    "no-descending-specificity": null, // 禁止較低特異性的選擇器覆蓋較高特異性的選擇器
    "declaration-empty-line-before": null, // 要求或不允許聲明前有空行
    "declaration-property-value-no-unknown": null,
    "scss/dollar-variable-pattern": null, // scss 變數格式，目前無法套用
    "keyframes-name-pattern": null, // keyframe 變數格式，目前無法套用
    "custom-property-pattern": null, // css 變數格式，目前無法套用
    "value-keyword-case": ["lower", { ignoreProperties: ["/^\\$/", "font-family"] }],
    "scss/double-slash-comment-empty-line-before": null,
    "scss/at-if-no-null": null,
    "scss/operator-no-newline-after": null,
    "scss/no-global-function-names": null,
    "media-feature-name-no-unknown": [
      true,
      {
        ignoreMediaFeatureNames: ["device-pixel-ratio"],
      },
    ],
    "media-feature-name-no-vendor-prefix": null,
    "declaration-block-no-duplicate-custom-properties": [
      true,
      {
        ignoreProperties: ["--safe-area-inset-bottom"],
      },
    ],
    // 禁止未知的偽類選擇器
    "selector-pseudo-class-no-unknown": [
      true,
      {
        // 忽略如下偽類
        ignorePseudoClasses: ["deep", "v-deep", "global"],
      },
    ],
  },
};
