import React from "react";
import { with<PERSON>outer } from "react-router";
import { inject, observer } from "mobx-react";
import { withGame } from "tcg-mobile-common";
import { utils } from "tcgmodulemc";

import deploy from "@/config/deploy.config";
import { gamePath } from "@/config/game.config";
import { get } from "@/utils/storage";

const withGameShell = (WrappedComponent) => {
  @withGame
  @inject("gameCenter", "common")
  @withRouter
  @observer
  class WithGame extends React.Component {
    launchGame = (itemData, options = {}) => {
      if (!get("MC_SESSION_INFO")) {
        return this.props.history.push("/m/login");
      }
      const { free } = options;
      const {
        gameType,
        vassalage,
        gameCode = "lobby",
        gameId,
        nodeId,
        supportPlay = false,
        isGameH5 = undefined,
        playType = "trad",
      } = itemData;
      switch (vassalage) {
        case "TCG_LOTTO_VN":
          this.props.launchLott({
            vendor: vassalage,
            gameCode,
            prizeMode: this.props.gameCenter.prizeMode,
          });
          break;
        case "TCG_SEA":
          this.props.launchLott({
            vendor: vassalage,
            gameCode,
            prizeMode: "SEA",
          });
          break;
        case "LOTT":
        case "ELOTT":
          this.props.launchLott({
            vendor: vassalage,
            prizeMode: this.props.gameCenter.prizeMode,
            gameCode,
            playType,
          });
          break;
        default:
          const params = {
            gameCategory: gameType,
            vendor: vassalage,
            gameId,
            nodeId,
            supportFreePlay: supportPlay,
          };
          if (free !== "undefined") {
            params.free = free;
            params.showChoice = false;
          }
          utils.gcUtils.launchGameV3(params);
          break;
      }
    };
    launchGameList = (game, options = {}) => {
      const { gameType, vassalage, gameId, roomId, nodeId, supportPlay, lottoMode = undefined, gameCode } = game;
      const playType = lottoMode && lottoMode.includes("Entertainment");
      this.launchGame(
        {
          gameType,
          vassalage,
          gameId: gameId || roomId,
          nodeId,
          supportPlay: supportPlay === 0,
          gameCode,
          playType: !!playType ? "ent" : "trad",
        },
        options
      );
    };
    launchGameVendor = (game, options = {}) => {
      const { vassalage, gameType, gameId, nodeId, isGameH5, freeplay } = game;
      if (deploy.gameListType.includes(gameType)) {
        return this.props.history.push(`${gamePath[gameType]}?vassalage=${vassalage}`);
      }
      this.launchGame(
        {
          gameType,
          vassalage,
          gameId,
          nodeId,
          isGameH5,
          supportPlay: freeplay === 1,
        },
        options
      );
    };
    launchWinnerBoard = (game) => {
      const { gameId, nodeId, vendor, gameType } = game;
      this.launchGame({
        gameType,
        gameId,
        nodeId,
        vassalage: vendor,
        gameCode: nodeId,
      });
    };
    render() {
      return (
        <WrappedComponent
          launchGameVendor={this.launchGameVendor}
          launchGameList={this.launchGameList}
          launchWinnerBoard={this.launchWinnerBoard}
          {...this.props}
        />
      );
    }
  }

  return WithGame;
};

export default withGameShell;
