import React from "react";
import { withRouter } from "react-router-dom";
import { Modal, Toast } from "antd-mobile";
import { inject, observer } from "mobx-react";
import { utils } from "tcgmodulemc";

import { get } from "@/utils/storage";

const withAuth = (WrappedComponent) => {
  @inject("auth")
  @withRouter
  @observer
  class withAuth extends React.Component {
    logout = ({ confirm = true } = {}) => {
      const { languageShell } = this.props;
      const session = get("MC_SESSION_INFO");
      const alert = Modal.alert;

      const doLogout = () => {
        Toast.loading(languageShell.t("in_please_wait"), 9999);
        return this.props.auth
          .logout(session.token)
          .then((rs) => {
            utils.appStorageUtils.storageSetItem("autoLogin", "0");
            this.props.history.replace("/m/home");
          })
          .finally(() => Toast.hide());
      };

      if (confirm) {
        alert(languageShell.t("in_popup_prompt"), languageShell.t("in_sure_logout"), [
          {
            text: languageShell.t("in_more_cancel"),
            style: "default",
          },
          {
            text: languageShell.t("in_increase_submit"),
            onPress: () => doLogout(),
          },
        ]);
        return Promise.resolve();
      } else {
        return doLogout();
      }
    };
    freeTrial = () => {
      Toast.loading(this.props.languageShell.t("in_please_wait"), 9999);
      this.props.auth.testplays().then(() => {
        Toast.hide();
        if (this.props.auth.trail !== 0) {
          this.props.history.replace("/m/home");
        }
      });
    };
    render() {
      return <WrappedComponent logout={this.logout} freeTrial={this.freeTrial} {...this.props} />;
    }
  }

  return withAuth;
};

export default withAuth;
