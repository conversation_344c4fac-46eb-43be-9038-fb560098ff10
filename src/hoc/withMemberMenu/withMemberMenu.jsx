import React from "react";
import { withRouter } from "react-router";
import { inject, observer } from "mobx-react";
import { withCs, withReferral } from "tcg-mobile-common";

import { handleWindowPage } from "@/utils/openLink";

import { createMenuHelper } from "./creatMenuHelper";

export default function withMemberMenu(WrappedComponent) {
  @inject("auth", "tcgCommon", "personal", "common", "mcMenu")
  @withRouter
  @withCs
  @withReferral
  @observer
  class WithMemberMenu extends React.Component {
    constructor(props) {
      super(props);

      this.menuHelper = createMenuHelper(props, {
        showReferral: props.showReferral,
        openCs: props.openCs,
        openCsByType: props.openCsByType,
        openCsByPos: props.openCsByPos,
        openReferral: props.openReferral,
        openResources: this.openResources,
        handleDownload: this.handleDownload,
        history: props.history,
      });
    }

    handleDownload = () => {
      this.props.common.setDownPopup(true);
    };

    openResources = (type) => {
      const link = this.props.common.downLinkObj[type];
      if (link?.url) {
        handleWindowPage(link?.url);
      }
    };

    render() {
      return <WrappedComponent {...this.props} menuHelper={this.menuHelper} />;
    }
  }

  return WithMemberMenu;
}
