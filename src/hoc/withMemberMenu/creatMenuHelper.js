import { observable } from "mobx";
import { navigatorUtil } from "tcg-mobile-common";

const { isApp, isPWA } = navigatorUtil;

/**
 * {current support type}
 * VIP: /m/member/benefits
 * AGENT: /m/member/home
 * MISSION: /m/leaderBoard
 * SIGNIN: /m/activity/signIn
 * SAVIOR: /m/activity/rescue
 * PROMOCODE: /m/promoCode
 * REFERRAL: /m/inviteFriends
 * APP: download
 * SERVICE: openCs
 */

export function createMenuHelper(stores = {}, handlers = {}) {
  const state = observable({
    get isLogin() {
      return stores.auth?.isLogin ?? false;
    },
    get isAgent() {
      return stores.auth?.isAgent ?? false;
    },
    get whitelabelVal() {
      return stores.mcMenu?.whitelabelVal ?? {};
    },
    get showVipBenefit() {
      return stores.tcgCommon?.showVipBenefit ?? false;
    },
    get showMission() {
      return stores.personal?.availablePromosByKey?.("MISSION")?.enabled ?? false;
    },
    get showSignIn() {
      return stores.personal?.availablePromosByKey?.("LOGIN")?.enabled ?? false;
    },
    get showRescueFund() {
      return stores.personal?.availablePromosByKey?.("SAVIOR")?.enabled ?? false;
    },
    get showPromoCode() {
      return stores.tcgCommon?.promoCodeEnable ?? false;
    },
    get resources() {
      return stores.common?.downLinkObj ?? {};
    },
  });

  function displayMenu(item) {
    const { key, resource, type } = item;
    if (!state.isLogin) return true;
    if (key) return state.whitelabelVal[key];
    if (resource) return !!state.resources[resource];

    switch (type) {
      case "VIP":
        return state.showVipBenefit;
      case "AGENT":
        return state.isAgent;
      case "MISSION":
        return state.showMission;
      case "SIGNIN":
        return state.showSignIn;
      case "SAVIOR":
        return state.showRescueFund;
      case "PROMOCODE":
        return state.showPromoCode;
      case "REFERRAL":
        return handlers.showReferral ?? false;
      case "APP":
        return !isApp && !isPWA;
      default:
        return true;
    }
  }

  function menuClick(item) {
    const { path, resource, type, csType, csPosition } = item;
    if (path) return handlers.history?.push?.(path);
    if (resource) return handlers.openResources?.(resource);
    if (type === "APP") {
      if (path) {
        return handlers.history?.push?.(path);
      }
      return handlers.handleDownload?.();
    }
    if (type === "SERVICE") {
      if (csType && handlers.openCsByType) {
        return handlers.openCsByType?.(csType);
      } else if (csPosition && handlers.openCsByPos) {
        return handlers.openCsByPos?.(csPosition);
      }
      return handlers.openCs?.();
    }
    if (type === "REFERRAL") return handlers.openReferral?.();
  }

  return { displayMenu, menuClick, state };
}
