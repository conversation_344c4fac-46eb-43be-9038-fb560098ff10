import React from "react";
import { inject, observer } from "mobx-react";
import { navigatorUtil, withDownload } from "tcg-mobile-common";

const { isApp, isPWA, isIos, isMobile, isSafari } = navigatorUtil;

const withDownloadShell = (WrappedComponent) => {
  @inject("common")
  @withDownload
  @observer
  class WithDownload extends React.Component {
    handleIOSDownload = () => {
      const IOS = this.props.common.downLinkObj["IOS"];
      const MOBILECONFIG = this.props.common.downLinkObj["MOBILECONFIG"];
      if (IOS?.url) {
        window.location.href = IOS.url;
        return;
      }
      if (!MOBILECONFIG?.url) {
        return null;
      }
      window.location.href = MOBILECONFIG.url;
      this.props.processProfileDownload();
    };
    handleAndroidDownload = () => {
      const android = this.props.common.downLinkObj["Android"];
      if (android?.url) {
        window.location.href = android.url;
      }
    };
    handleDownload = () => {
      if (isIos) {
        this.handleIOSDownload();
      } else {
        this.handleAndroidDownload();
      }
    };
    showA2HS = () => {
      window?.showA2HS();
    };
    render() {
      return (
        <WrappedComponent
          handleDownload={this.handleDownload}
          handleAndroidDownload={this.handleAndroidDownload}
          handleIOSDownload={this.handleIOSDownload}
          showA2HS={this.showA2HS}
          {...this.props}
        />
      );
    }
  }

  return WithDownload;
};

export default withDownloadShell;
