/* eslint-disable no-undef */
// JENKINS DON'T CHANGE DON'T CHANGE
const SW_VERSION = "$BUILD";
const isProd = (location.search || "").includes("production");
// JENKINS DON'T CHANGE DON'T CHANGE

function isJson(str) {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
}

workbox.setConfig({
  debug: !isProd,
});

self.addEventListener("message", (event) => {
  if (!event.data) {
    return;
  }
  switch (event.data.type) {
    case "SKIP_WAITING":
      console.log(`Version=${SW_VERSION} accept SKIP_WAITING`);
      self.skipWaiting();
      event.ports[0].postMessage(true);
      break;
    case "GET_VERSION":
      console.log(`Version=${SW_VERSION}`);
      event.ports[0].postMessage(SW_VERSION);
      break;
    default:
      break;
  }
});

self.addEventListener("install", (e) => {});

self.addEventListener("fetch", (e) => {});

self.addEventListener("push", function (event) {
  const data = event.data.text();
  console.log("Push data: ", data);

  if (isJson(data)) {
    const notification = JSON.parse(data).notification;
    const { title, body, image } = notification;
    return self.registration.showNotification(title, {
      icon: "/m/icons/icon-192x192.png",
      body,
      image,
      tag: "default-group",
    });
  }
});

self.addEventListener("notificationclick", function (event) {
  console.log("On notification click: ", event.notification.tag);
  event.notification.close();

  event.waitUntil(
    clients
      .matchAll({
        type: "window",
      })
      .then(function (clientList) {
        for (var i = 0; i < clientList.length; i++) {
          var client = clientList[i];
          if (client.url && client.url.startsWith(self.registration.scope) && "focus" in client) {
            return client.focus();
          }
        }
        if (clients.openWindow) {
          return clients.openWindow("/m/index.html");
        }
      })
  );
});

workbox.core.skipWaiting();
workbox.core.clientsClaim();
