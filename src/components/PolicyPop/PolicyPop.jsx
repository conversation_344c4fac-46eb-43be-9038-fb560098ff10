import React from "react";
import { withRouter } from "react-router-dom";
import { inject, observer } from "mobx-react";
import { CommonLogo } from "tcg-mobile-common";

import deploy from "@/config/deploy.config";
import ResponsibleGaming from "@/containers/Login/ResponsibleGaming";
import CheckBox from "@/ui/CheckBox/CheckBox";
import CommonPopup from "@/ui/CommonPopup/CommonPopup";

import "./PolicyPop.scss";

const policyPopupStorage = "POLICY_POPUP";

@inject("common", "languageShell")
@withRouter
@observer
class PolicyPop extends React.Component {
  get allChecked() {
    return this.state.ageCheck && this.state.governmentCheck && this.state.gelCheck && this.state.termsCheck;
  }
  componentDidMount() {
    const policyStorage = localStorage.getItem(policyPopupStorage);
    if (!policyStorage) {
      this.props.common.showPolicyPopup(true);
    }
  }
  state = { ageCheck: false, governmentCheck: true, gelCheck: true, termsCheck: true };
  closePopup = () => {
    this.props.common.showPolicyPopup(false);
  };
  handlePolicy = () => {
    this.props.history.push(`/m/help?tag=terms`);
  };
  handleSubmit = () => {
    if (this.allChecked) {
      this.closePopup();
      localStorage.setItem(policyPopupStorage, "1");
    }
  };
  handleExit = () => {
    this.closePopup();
    localStorage.removeItem(policyPopupStorage);
  };
  toggleAll = (value) => {
    this.setState({
      ageCheck: value,
      governmentCheck: value,
      gelCheck: value,
      termsCheck: value,
    });
  };
  handleAge = (value) => {
    this.setState({
      ageCheck: value,
    });
  };
  handleGovernment = (value) => {
    this.setState({
      governmentCheck: value,
    });
  };
  handleGEL = (value) => {
    this.setState({
      gelCheck: value,
    });
  };
  handleTerms = (value) => {
    this.setState({
      termsCheck: value,
    });
  };

  render() {
    return (
      <CommonPopup
        className="policy-popup-container"
        show={this.props.common.policyPopupStatus}
        handleClose={this.closePopup}
      >
        <div className="policy-popup-content">
          <CommonLogo className="popup-logo" src={require("@/assets/images/logo/logo.png")} />
          <div className="policy-popup-title">{this.props.languageShell.t("please_read_guide")}</div>
          <div className="policy-info-wrap">
            <CheckBox checked={this.state.ageCheck} onChange={this.handleAge}>
              <p>{this.props.languageShell.t("over_age").format(deploy.ageLimit)}</p>
            </CheckBox>
            <CheckBox checked={this.state.governmentCheck} onChange={this.handleGovernment}>
              <p>{this.props.languageShell.t("government_check")}</p>
            </CheckBox>
            <CheckBox checked={this.state.gelCheck} onChange={this.handleGEL}>
              <p>{this.props.languageShell.t("gel_check")}</p>
            </CheckBox>
            <CheckBox checked={this.state.termsCheck} onChange={this.handleTerms}>
              <p>
                {this.props.languageShell.t("read_and_agree").format(deploy.brandName)}
                <span className="term-link" onClick={this.handlePolicy}>
                  {this.props.languageShell.t("in_terms_use")}
                </span>
              </p>
            </CheckBox>
          </div>
          <div className="ineligible-play">{this.props.languageShell.t("ineligible_play")}</div>
          <CheckBox className="agree-all" checked={this.allChecked} onChange={this.toggleAll}>
            <p>
              {this.props.languageShell.t("agree_all")}
              <span className="term-link" onClick={this.handlePolicy}>
                {this.props.languageShell.t("in_terms_use")}
              </span>
            </p>
          </CheckBox>
          <div className="popup-btn">
            <button type="button" disabled={!this.allChecked} onClick={this.handleSubmit} className="submit-btn">
              <span>{this.props.languageShell.t("proceed")}</span>
            </button>
            <span className="btn-exit" onClick={this.handleExit}>
              {this.props.languageShell.t("exit")}
            </span>
          </div>
          <ResponsibleGaming />
        </div>
      </CommonPopup>
    );
  }
}

export default PolicyPop;
