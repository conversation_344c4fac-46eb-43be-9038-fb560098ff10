.policy-popup-container {
  width: 625px;

  .policy-popup-content {
    padding: 40px 48px;
  }

  .popup-logo {
    display: block;
    height: 65px;
    margin: 0 auto;
  }

  .policy-popup-title {
    margin-top: 32px;
    font-size: 24px;
    line-height: normal;
    color: #fff;
    text-align: center;
  }

  .policy-info-wrap {
    display: grid;
    gap: 20px;
    padding: 24px;
    margin-top: 32px;
    background: rgb(255 255 255 / 8%);
    border-radius: 12px;
  }

  .sm-checkbox-item {
    font-size: 22px;
    line-height: normal;
    color: #fff;

    .term-link {
      @include border-text-gradient;
      margin-left: 10px;
    }
  }

  .ineligible-play {
    margin-top: 32px;
    font-size: 22px;
    line-height: normal;
    color: #fff;
  }

  .agree-all {
    margin-top: 32px;
  }

  .popup-btn {
    display: grid;
    gap: 20px;
    margin-top: 32px;
    font-size: 26px;
    font-weight: 600;
    line-height: normal;
    text-align: center;

    .submit-btn {
      @include flex-center;
      height: 60px;
      padding: 1px 30px;
      color: #201438;
      background: linear-gradient(97deg, #fff5c6 4.31%, #d1ae4c 85.26%);
      border: 1.5px solid #fff4cd;
      border-radius: 50px;

      &:disabled {
        opacity: 0.5;
      }
    }

    .btn-exit {
      @include flex-center;
      height: 60px;
      padding: 1px 30px;
      color: #9784bf;
      background: #3b2a5e;
      border-radius: 50px;
    }
  }

  .responsible-gaming {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 22px;
    padding: 12px 20px;
    margin-top: 32px;
    background: rgb(255 255 255 / 40%);
    border-radius: 12px;
    backdrop-filter: blur(10px);

    img {
      display: block;
      width: 100%;
      object-fit: contain;
    }
  }
}
