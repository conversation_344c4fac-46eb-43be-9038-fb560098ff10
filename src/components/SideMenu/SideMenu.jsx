import React from "react";
import { withRouter } from "react-router-dom";
import { Icon, Toast } from "antd-mobile";
import cz from "classnames";
import { debounce } from "lodash";
import { inject, observer } from "mobx-react";
import PubSub from "pubsub-js";
import { navigatorUtil } from "tcg-mobile-common";

import deploy from "@/config/deploy.config";
import { navConfig } from "@/config/game.config";
import withAuth from "@/hoc/withAuth";
import withMemberMenu from "@/hoc/withMemberMenu";
import CommonPopup from "@/ui/CommonPopup";
import TouchMoveHandle from "@/ui/TouchMoveHandle";
import { get } from "@/utils/storage";

import LanguageAccordion from "../LanguageSelect/LanguageAccordion";

import "./SideMenu.scss";

const { gameSelectType } = deploy;
const { isApp, isPWA } = navigatorUtil;
const showDownload = !isApp && !isPWA;

@inject("languageShell", "gameCenter", "tcgCommon", "common")
@withMemberMenu
@withAuth
@withRouter
@observer
class SideMenu extends React.Component {
  get menus() {
    return [{ gameCategory: "HOME" }, ...this.sorting];
  }
  get isLogin() {
    return this.props.auth.isLogin;
  }
  get isAgent() {
    return this.props.auth.isAgent;
  }
  get sorting() {
    return this.props.gameCenter.gameVendor.sorting;
  }
  get hasDownloadBar() {
    return this.props.tcgCommon.isShowDownloadBar;
  }
  constructor(props) {
    super(props);
    this.menuHelper = props.menuHelper;
  }
  componentDidMount() {
    this.listenRouterChange();
    this.getAnnounce();
    this.initGame();
    this.getHelp();
    // this.props.tcgCommon.getGameMatches();

    this.changeList = PubSub.subscribe(
      "changeLanguage",
      debounce(() => {
        this.initGame(true);
        this.getHelp();
      }, 250)
    );
  }
  componentWillUnmount() {
    PubSub.unsubscribe(this.changeList);
  }
  listenRouterChange() {
    this.props.history.listen((location, action) => {
      this.hideSideMenu();
    });
  }
  getHelp() {
    this.props.common.getHelpCenter({
      lang: this.props.languageShell.currentLanguage,
    });
  }
  getAnnounce = () => {
    const session = get("MC_SESSION_INFO");
    let data = {
      types: "B,PL,PU,PR,H",
      groupName: "",
      platform: "M",
      merchantCode: deploy.merchant,
      token: session ? session.token : "",
    };
    this.props.common.annount(data);
  };
  initGame = (flush = false) => {
    this.props.tcgCommon.getGameVendor(flush).then((res) => {
      this.props.gameCenter.setGameVendor(res);
    });
    this.getHotGames(flush);
  };
  getHotGames = (refresh) => {
    const data = {
      isPlatform: 2,
      language: this.props.languageShell.currentLanguage,
      platform: "html5",
    };
    return this.props.gameCenter.getGameHot(data);
  };
  hideSideMenu = () => {
    this.props.common.showSideMenu(false);
  };
  handleLogout = (e) => {
    this.hideSideMenu();
    this.props.logout();
  };
  handleLogin = () => {
    this.hideSideMenu();
    this.props.history.push("/m/login");
  };
  handleNavClick = (item) => {
    this.hideSideMenu();
    const { type } = item;
    return this.menuHelper.menuClick(item);
  };
  openDownPopup = () => {
    this.props.common.showLeftSide(false);
    this.props.common.setDownPopup(true);
  };
  handleDownload = () => {
    this.hideSideMenu();
    // this.props.history.push("/m/download");
    this.props.common.setDownPopup(true);
  };
  handleGameNav = async (gameCategory) => {
    this.hideSideMenu();
    await this.props.history.push(`/m/home`);
    await this.props.gameCenter.setCurrentDataType(gameSelectType.ALL);
    await this.props.gameCenter.setCurrentVassalage("");
    await this.props.gameCenter.setCurrentGameCategory(gameCategory);
  };
  handleCopy = (_, result) => {
    if (result) {
      Toast.success(this.props.languageShell.t("copy_success"), 2);
    }
  };
  renderBtn = () => {
    if (this.isLogin) {
      return (
        <div className="btn-logout" onClick={this.handleLogout}>
          <span>{this.props.languageShell.t("in_sign_out")}</span>
        </div>
      );
    }
    return null;
  };

  render() {
    return (
      <CommonPopup
        className="left-side-menu"
        closeOnClickOverlay
        closeable={false}
        position="left"
        zIndex={60}
        show={this.props.common.sideMenuOpen}
        onClose={this.hideSideMenu}
        destroyOnClose={false}
      >
        <TouchMoveHandle handleEvent={this.hideSideMenu} position="left">
          <div
            className={cz("side-menu-layout", {
              // "full-h": !this.hasDownloadBar || !homePath.includes(location.pathname),
            })}
          >
            <div className="side-menu-content">
              <div className="side-menu-header">
                <Icon
                  className="icon-user"
                  type={require("!svg-sprite-loader!@/assets/images/sideMenu/icon-user.svg")}
                />
                {this.props.auth.currentToken ? (
                  <div className="login-register">
                    <div className="login-btn" onClick={() => this.goPage("/m/voucherCenter")}>
                      <span>{this.props.languageShell.t("hd_deposit_button")}</span>
                    </div>
                    <div className="register-btn" onClick={() => this.props.history.push("/m/withdraw")}>
                      <span>{this.props.languageShell.t("hd_withdraw_button")}</span>
                    </div>
                  </div>
                ) : (
                  <div className="login-register">
                    <div className="login-btn" onClick={() => this.props.history.push("/m/login")}>
                      <span>{this.props.languageShell.t("hd_login_button")}</span>
                    </div>
                    <div className="register-btn" onClick={() => this.props.history.push("/m/register")}>
                      <span>{this.props.languageShell.t("hd_reg_button")}</span>
                    </div>
                  </div>
                )}
              </div>
              <div className="side-menu-nav">
                {this.menus.map((item, index) => {
                  return (
                    <div
                      className={cz("side-nav-item", {
                        on: this.props.gameCenter.currentGameCategory === item.gameCategory,
                      })}
                      key={`side_nav_${index}`}
                      onClick={() => this.handleGameNav(item.gameCategory)}
                    >
                      <Icon className="nav-icon" type={navConfig[item.gameCategory]?.icon} />
                      <div className="nav-name">
                        <span>{this.props.languageShell.t(navConfig[item.gameCategory]?.name)}</span>
                      </div>
                    </div>
                  );
                })}
                <div className="side-nav-item" onClick={() => this.props.history.push("/m/activity")}>
                  <Icon
                    className="nav-icon"
                    type={require("!svg-sprite-loader!@/assets/images/common/nav-promo.svg")}
                  />
                  <div className="nav-name">
                    <span>{this.props.languageShell.t("promotion")}</span>
                  </div>
                </div>
                {showDownload && (
                  <div className="side-nav-item" onClick={() => this.props.history.push("/m/download")}>
                    <Icon
                      className="nav-icon"
                      type={require("!svg-sprite-loader!@/assets/images/common/nav-download.svg")}
                    />
                    <div className="nav-name">
                      <span>{this.props.languageShell.t("download_promo")}</span>
                    </div>
                  </div>
                )}
                <LanguageAccordion />
              </div>

              {this.renderBtn()}
            </div>
          </div>
        </TouchMoveHandle>
      </CommonPopup>
    );
  }
}

export default SideMenu;
