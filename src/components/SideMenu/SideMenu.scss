$side-menu-header-height: 222px;

.left-side-menu {
  top: 0;
  width: 560px;
  color: var(--text-color-primary);

  .side-menu-layout {
    width: 100%;
    height: calc(var(--vh, 1vh) * 100);
    overflow: hidden;
    background: var(--bg-color-primary);

    &.full-h {
      height: calc(var(--vh, 1vh) * 100 - var(--header-height));
    }
  }

  .side-menu-header {
    display: flex;
    flex-direction: column;
    gap: 40px;
    align-items: center;

    .icon-user {
      display: block;
      width: 88px;
      height: 88px;
      object-fit: contain;
    }

    .login-register {
      display: flex;
      align-items: center;
    }

    .login-btn {
      @include btn-login;
    }

    .register-btn {
      @include btn-register;
    }
  }

  .side-menu-content {
    width: 100%;
    height: 100%;
    padding: 44px 32px;
    overflow-y: auto;
  }

  .btn-logout {
    @include flex-center;
    width: 100%;
    height: 64px;
    padding: 0 20px;
    margin-top: 24px;
    font-size: 24px;
    font-weight: 500;
    line-height: normal;
    text-align: center;
    text-transform: capitalize;
    background: #f00;
    border-radius: 12px;
  }

  .side-menu-nav {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-top: 40px;
  }

  .side-nav-item {
    position: relative;
    display: flex;
    gap: 16px;
    align-items: center;
    padding: 16px 20px;
    font-size: 24px;
    font-weight: 500;
    line-height: normal;

    text-align: center;
    background: var(--bg-color-surface);
    border-radius: 12px;

    &.on {
      border: 1px solid var(--border-color-accent);
    }

    .nav-icon {
      display: block;
      flex-shrink: 0;
      width: 32px;
      height: 32px;
      object-fit: contain;
      fill: none;
    }

    .arrow-right {
      display: block;
      width: 23px;
      height: 23px;
    }
  }

  .common-accordion {
    .common-accordion-header {
      padding: 0 32px;
    }

    .accordion-nav-item {
      display: flex;
      gap: 30px;
      align-items: center;
      height: 104px;
      padding: 0 64px;
      font-size: 28px;
      line-height: normal;

      .accordion-nav-icon {
        width: 40px;
        height: 40px;
      }
    }
  }

  .app-download {
    display: flex;
    gap: 30px;
    align-items: center;
    width: 100%;
    height: 80px;
    padding: 0 28px;
    margin: 20px 0 0;
    font-size: 32px;
    font-weight: 700;
    line-height: normal;
    color: #fff;
    background: #19191a;
    border: 2.04px solid #505050;
    border-radius: 20.4px;

    .icon-download {
      display: block;
      height: 48px;
      object-fit: contain;
    }
  }

  .invite-friend {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 20px 10px;
    font-size: 24px;
    font-weight: 700;
    line-height: 123%;
    color: #fff;
    letter-spacing: 0.24px;
    background: radial-gradient(58.47% 58.47% at 50% -19.18%, #0b2067 0%, #10225c 100%);
    border-radius: 18px;

    .icon-invite {
      display: block;
      flex-shrink: 0;
      width: 69px;
      height: 69px;
      margin-right: 9px;
    }
  }

  .service-nav-item {
    display: flex;
    gap: 24px;
    align-items: center;
    padding: 12px 30px;
    font-size: 24px;
    line-height: normal;
    color: #fff;

    .service-icon {
      display: block;
      width: 40px;
      height: 40px;
      object-fit: contain;
    }
  }

  .side-logged-wrap {
    padding: 20px;
    margin-top: 24px;
    background: #6756b8;
    border-radius: 24px;
  }

  .service-accordion {
    margin-top: 32px;
  }

  .accordion-active {
    .side-nav-item {
      color: #fffc05;

      .arrow-down {
        transform: rotate(0);
      }
    }
  }
}
