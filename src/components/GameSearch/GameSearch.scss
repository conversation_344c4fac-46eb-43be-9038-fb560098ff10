.game-search-wrap {
  width: 100%;
  min-height: 300px;

  .game-filter {
    display: grid;
    flex-shrink: 0;
    grid-auto-flow: column;
    gap: 10px;
    margin-left: 10px;

    .filter-btn {
      @include flex-center;
      width: 80px;
      height: 80px;
      color: #fff;
      background: #930000;
      border-radius: 24px;

      .am-icon {
        width: 40px;
        height: 40px;
      }
    }
  }

  .game-search-content {
    position: relative;
  }

  .search-header {
    padding: 30px 27px 20px;
    font-size: 40px;
    font-style: normal;
    font-weight: 700;
    color: #fff;
  }

  .search-result-title {
    font-size: 24px;
    line-height: normal;
    color: #a4a3a3;
    text-align: center;
  }

  .search-game-input {
    position: relative;
    display: flex;
    align-items: center;

    .search-input {
      flex: 1;
      height: 50px;
      padding: 0 20px 0 55px;
      font-size: 25px;
      font-weight: 500;
      color: #fff;
      text-shadow: 0 1.389px 5.556px rgb(0 0 0 / 25%);
      background: #2d2d2d;
      border: 1px solid #fee93c;
      border-radius: 8px;
    }

    .search-icon {
      position: absolute;
      top: 10px;
      left: 20px;
      width: 30px;
      height: 30px;
      color: #fff;
    }

    .search-control {
      position: absolute;
      top: 21px;
      right: 16px;
      display: flex;
      align-items: center;
      height: 30px;
      font-size: 24px;
      font-weight: 600;
      line-height: normal;
      color: #531f0e;
    }

    .search-clear {
      width: 24px;
      height: 24px;
      margin-right: 24px;
      fill: none;
    }

    .close-search {
      flex-shrink: 0;
      margin-left: 15px;
      font-size: 30px;
      font-weight: 500;
      line-height: normal;
      color: #fff;
      text-align: center;
      text-transform: uppercase;
      text-shadow: 0 1.389px 5.556px rgb(0 0 0 / 25%);
    }
  }

  .search-result-wrap {
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    margin: 20px 0;
    font-size: 24px;
    font-weight: 600;
    line-height: normal;
    color: #1f2937;

    .total-count {
      margin: 0 12px;
      color: #8a0b0c;
    }
  }

  .search-game-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    border-bottom: 2px solid #a3a3a3;

    .search-label {
      font-size: 30px;
      font-weight: 500;
      line-height: normal;
      text-align: center;
      text-shadow: 0 0 3.7px #ffcd00;
      background: linear-gradient(180deg, #fff 0%, #ffee98 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .result-count {
      font-size: 25px;
      font-weight: 500;
      line-height: normal;
      color: #fff;
      text-align: center;
    }
  }

  .search-game-result {
    padding: 0 27px;

    .result-title {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      font-size: 30px;
      font-weight: 700;
      line-height: normal;
      color: #fff;

      .am-icon {
        margin-right: 10px;
      }
    }
  }

  .search-game-list {
    @include game-list;
    padding: 15px 10px;
    background: #1d1d1d;
  }

  .category-game-list {
    @include game-list;
  }

  .no-result {
    margin-top: 50px;
  }
}
