import React from "react";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import CommonPopup from "@/ui/CommonPopup/CommonPopup";

import GameSearch from "./GameSearch";

@inject("common")
@observer
class GameSearchPop extends React.Component {
  closePopup = () => {
    this.props.common.showSearchPop(false);
  };
  render() {
    return (
      <CommonPopup
        className="search-popup-container"
        show={this.props.common.searchPopStatus}
        handleClose={this.closePopup}
        showClose={false}
      >
        <GameSearch gameType={this.props.gameType} />
      </CommonPopup>
    );
  }
}

GameSearchPop.propTypes = {
  gameType: PropTypes.string,
};

GameSearchPop.defaultProps = {
  gameType: "",
};

export default GameSearchPop;
