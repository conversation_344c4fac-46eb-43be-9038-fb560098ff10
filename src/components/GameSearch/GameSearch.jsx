import React from "react";
import { withRouter } from "react-router";
import { Icon } from "antd-mobile";
import { debounce } from "lodash";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import gameCenterApi from "@/apis/gameCenter";
import GameMoreBtn from "@/components/GameMoreBtn/GameMoreBtn";
import NoData from "@/components/NoData/NoData";
import SpinnerLoading from "@/components/SpinnerLoading";
import deploy from "@/config/deploy.config";

import GameListItem from "../GameListItem/GameListItem";

import "./GameSearch.scss";

const PAGE_SIZE = 24;

@inject("languageShell", "common", "gameCenter")
@withRouter
@observer
class GameSearch extends React.Component {
  get showHot() {
    return this.state.gameName === "";
  }
  get hotList() {
    return this.props.gameCenter.hotGames.slice(0, 8);
  }
  constructor(props) {
    super(props);
    this.state = {
      gameType: "",
      vassalage: "",
      gameName: "",
      gameList: [],
      totalCount: 0,
      hasMore: false,
      noData: false,
      currentPage: 1,
      loading: false,
    };
  }
  componentDidMount() {
    // this.initGame();
  }
  componentDidUpdate(prevProps) {
    if (prevProps.gameType !== this.props.gameType) {
      // this.initGame();
    }
  }
  initGame = () => {
    let gameType = this.props.gameType;
    let vassalage = "";
    this.setState({
      gameType,
      vassalage,
    });
  };
  handleSearchByName = (gameName) => {
    this.setState(
      {
        gameList: [],
        currentPage: 1,
        totalPages: 1,
        gameName,
        hasMore: false,
      },
      () => {
        if (gameName && gameName?.length > 2) {
          this.getGameList(true, true);
        }
        if (gameName === "") {
          this.setState({
            noData: false,
          });
        }
      }
    );
  };
  getGameList(refresh, loading) {
    if (loading) {
      this.setState({
        loading: true,
      });
    }

    gameCenterApi
      .getGameList({
        merchant: deploy.merchant,
        platform: "html5",
        gameName: this.state.gameName,
        gameType: this.state.gameType,
        pageNo: this.state.currentPage,
        pageSize: PAGE_SIZE,
        vassalage: this.state.vassalage,
        gameClassify: "",
        minBet: "",
        minLine: "",
        language: this.props.languageShell.language,
      })
      .then((res) => {
        const result = res?.data?.value;
        if (refresh) {
          this.setState({
            gameList: result.games,
            currentPage: result.pageNum,
          });
        } else {
          this.setState((state) => ({
            gameList: state.gameList.concat(result.games),
            currentPage: result.pageNum,
          }));
        }
        const { totalPages, pageNum, totalCount } = result;
        const hasMore = pageNum < totalPages;
        this.setState({ hasMore, totalCount, noData: result?.games?.length < 1 });
      })
      .finally(() =>
        this.setState({
          loading: false,
        })
      );
  }
  loadMore = () => {
    this.setState(
      {
        currentPage: this.state.currentPage + 1,
      },
      () => {
        this.getGameList(false, true);
      }
    );
  };
  setGameName = (e) => {
    const name = e.target.value;
    this.setState({ gameName: name });
    if (e.key === "Enter") {
      e.target.blur();
      this.setState({ gameName: e.target.value }, () => {
        this.handleSearchByName(this.state.gameName);
      });
    }
  };
  handleInput = (e) => {
    this.setState({ gameName: e.target.value }, () => {
      this.searchGameByName();
    });
  };
  searchGameByName = debounce(() => {
    this.handleSearchByName(this.state.gameName);
  }, 500);

  closeSearch = () => {
    this.handleSearchByName("");
  };
  render() {
    return (
      <div className="game-search-wrap">
        <div className="game-search-content">
          <div className="search-header">
            <div className="search-game-input">
              <Icon
                className="search-icon"
                type={require("!svg-sprite-loader!@/assets/images/common/icon-search.svg")}
              />
              <input
                className="search-input"
                type="text"
                autoComplete="off"
                disabled={this.props.disabled}
                placeholder={this.props.languageShell.t("search")}
                value={this.state.gameName}
                onInput={this.handleInput}
                onKeyUp={this.setGameName}
              />

              <div className="close-search" onClick={this.closeSearch}>
                <span>{this.props.languageShell.t("in_more_cancel")}</span>
              </div>
            </div>
          </div>
          <div className="search-result-content">
            {!this.state.noData ? (
              <div className="search-game-result">
                {this.state.gameList?.length > 0 && (
                  <div className="search-result-box">
                    <div className="search-game-title">
                      <span className="search-label">{this.props.languageShell.t("search_results")}</span>
                      <span className="result-count">
                        {this.props.languageShell.t("about_result").format(this.state.totalCount)}
                      </span>
                    </div>
                    <div className="search-game-list">
                      {this.state.gameList.map((item) => {
                        return <GameListItem key={`result_${item.nodeId}`} item={item} />;
                      })}
                    </div>
                  </div>
                )}

                {this.showHot && (
                  <div className="search-result-box">
                    <div className="search-game-title">
                      <span className="search-label">{this.props.languageShell.t("recommended")}</span>
                    </div>
                    <div className="search-game-list">
                      {this.hotList.map((item) => {
                        return <GameListItem key={`hot_${item.nodeId}`} item={item} />;
                      })}
                    </div>
                  </div>
                )}
                {this.state.hasMore && (
                  <GameMoreBtn onClick={this.loadMore} loading={this.state.loading} hasMore={this.state.hasMore} />
                )}
              </div>
            ) : (
              <div className="no-result">
                <NoData />
              </div>
            )}
          </div>
          {this.state.loading && this.state.gameList?.length < 1 && <SpinnerLoading />}
        </div>
      </div>
    );
  }
}

GameSearch.propTypes = {
  gameType: PropTypes.string,
};

GameSearch.defaultProps = {
  gameType: "",
};

export default GameSearch;
