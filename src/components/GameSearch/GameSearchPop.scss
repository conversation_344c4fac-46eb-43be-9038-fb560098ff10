.search-popup-container {
  .search-popup-wrap {
    width: 100%;
    height: 100vh;
    height: calc(100 * var(--vh, 1vh));
    background: rgba(#2e3158, 0.75);
    box-shadow:
      0 22px 30px -14px rgb(0 0 0 / 20%),
      0 48px 76px 6px rgb(0 0 0 / 14%),
      0 18px 92px 16px rgb(0 0 0 / 12%);
    backdrop-filter: blur(10px);
  }

  .game-filter {
    display: grid;
    flex-shrink: 0;
    grid-auto-flow: column;
    gap: 10px;
    margin-left: 10px;

    .filter-btn {
      @include flex-center;
      width: 80px;
      height: 80px;
      color: #fff;
      background: #930000;
      border-radius: 24px;

      .am-icon {
        width: 40px;
        height: 40px;
      }
    }
  }

  .search-popup-content {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
  }

  .search-header {
    flex-shrink: 0;
    height: 220px;
    padding: 46px 32px 8px;
    font-size: 40px;
    font-style: normal;
    font-weight: 700;
    color: #fff;
  }

  .search-result-title {
    margin-top: 30px;
    font-size: 32px;
    line-height: 48px;
    color: #fff;
  }

  .search-input-wrap {
    position: relative;
    display: flex;
    align-items: center;
    padding-right: 112px;

    .search-input {
      width: 100%;
      height: 80px;
      padding: 0 15px;
      font-size: 32px;
      color: #fff;
      background: transparent;
      border: 2px solid rgb(255 255 255 / 23%);
      border-radius: 16px;

      &::placeholder {
        color: rgb(255 255 255 / 50%);
      }

      &:focus {
        border: 2px solid #00b4d8;
      }
    }

    .search-icon {
      position: absolute;
      top: 25px;
      left: 22px;
      width: 30px;
      height: 30px;
      color: rgb(255 255 255 / 50%);
    }

    .search-control {
      position: absolute;
      top: 21px;
      right: 16px;
      display: flex;
      align-items: center;
      height: 30px;
      font-size: 24px;
      font-weight: 600;
      line-height: normal;
      color: #531f0e;
    }

    .search-clear {
      width: 24px;
      height: 24px;
      margin-right: 24px;
      fill: none;
    }
  }

  .search-result-wrap {
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    margin: 20px 0;
    font-size: 24px;
    font-weight: 600;
    line-height: normal;
    color: #1f2937;

    .total-count {
      margin: 0 12px;
      color: #8a0b0c;
    }
  }

  .search-result-content {
    flex: 1;
    overflow: auto;
  }

  .search-game-result {
    padding: 24px 30px 0 0;

    .result-title {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      font-size: 30px;
      font-weight: 700;
      line-height: normal;
      color: #fff;

      .am-icon {
        margin-right: 10px;
      }
    }
  }

  .game-more {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    .btn-more {
      display: flex;
      align-items: center;
      padding: 13px 25px;
      font-size: 24px;
      font-weight: 600;
      color: #fff;
      text-align: center;
      background: #ca5a00;
      border-radius: 64px;
    }

    .am-icon {
      width: 32px;
      height: 32px;
      margin-left: 8px;
    }
  }

  .category-game-list {
    @include game-list;
  }

  .no-result {
    margin-top: 50px;
  }
}

.popup-slide-enter {
  transform: translateY(110%);
}

.popup-slide-enter-active {
  transform: translateY(0);
  transition: all 250ms ease-out;
}

.popup-slide-exit {
  transform: translateY(0);
}

.popup-slide-exit-active {
  transform: translateY(110%);
  transition: all 250ms ease-out;
}
