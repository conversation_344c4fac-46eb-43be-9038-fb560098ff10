import React, { Component } from "react";
import { withRouter } from "react-router";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { LazyLoadImage } from "tcg-mobile-common";

import withGame from "../../hoc/withGame";

import "./GameSearchItem.scss";

@inject("common", "gameCenter")
@withRouter
@withGame
@observer
class GameSearchItem extends Component {
  get imgUrl() {
    const { showIcon, iconUrl } = this.props.game;
    return showIcon || iconUrl || "";
  }
  gameClick = () => {
    this.props.launchGameList(this.props.game);
  };
  render() {
    const item = this.props.game;
    return (
      <div className="game-search-item" onClick={this.gameClick}>
        <div className="game-background">
          <LazyLoadImage
            className="img-loading"
            src={this.imgUrl}
            errorSrc={this.props.common.defaultImage}
            visibleByDefault={true}
          />
        </div>
        <div className="game-info">
          <div className="game-display-name">
            <span>{item.gameName || item.nodeName}</span>
          </div>
          <div className="game-vendor-name">
            <span>{item.displayName}</span>
          </div>
        </div>
        <div className="btn-arrow">
          <Icon type={require("!svg-sprite-loader!@/assets/images/common/arrow-right.svg")} />
        </div>
      </div>
    );
  }
}

GameSearchItem.propTypes = {
  game: PropTypes.object.isRequired,
};

export default GameSearchItem;
