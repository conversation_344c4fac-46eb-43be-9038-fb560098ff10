import React from "react";
import { Icon } from "antd-mobile";
import cz from "classnames";

import { getTheme, setTheme } from "../../utils/theme";

import "./SwitchTheme.scss";

const themeType = {
  dark: true,
  light: false,
};

class SwitchTheme extends React.Component {
  state = { isChecked: false };
  componentDidMount() {
    this.initSwitch();
  }
  initSwitch() {
    const theme = getTheme();
    this.setState({
      isChecked: themeType[theme],
    });
  }
  changeTheme = (value) => {
    this.setState({
      isChecked: value,
    });
    const type = value ? "dark" : "light";
    setTheme(type);
  };
  render() {
    return (
      <div className="switch-container">
        <div className={cz("switch-theme", { on: !this.state.isChecked })} onClick={() => this.changeTheme(true)}>
          <Icon type={require("!svg-sprite-loader!../../stylesheets/svgs/home/<USER>")} />
        </div>
        <div className={cz("switch-theme", { on: this.state.isChecked })} onClick={() => this.changeTheme(false)}>
          <Icon type={require("!svg-sprite-loader!../../stylesheets/svgs/home/<USER>")} />
        </div>
      </div>
    );
  }
}

export default SwitchTheme;
