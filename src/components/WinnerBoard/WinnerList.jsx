import React, { Component } from "react";
import cz from "classnames";
import { debounce } from "lodash";
import { initial, last, random, take } from "lodash";
import { inject, observer } from "mobx-react";
import PubSub from "pubsub-js";
import { currencyFormat } from "tcg-mobile-common";

import withGame from "@/hoc/withGame";

import "./WinnerList.scss";

@inject("languageShell", "gameCenter", "tcgCommon")
@withGame
@observer
export default class WinnerBoard extends Component {
  get winnerList() {
    return this.props.gameCenter.winnerList;
  }
  state = { gameList: [], visibleData: [] };
  componentDidMount() {
    this.getWinnerBoard();
    this.changeList = PubSub.subscribe(
      "changeLanguage",
      debounce(() => {
        this.getWinnerBoard();
      }, 250)
    );
  }

  componentWillUnmount() {
    PubSub.unsubscribe(this.changeList);
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
  getWinnerBoard() {
    const query = {
      gameCategory: "ALL",
      language: this.props.languageShell.currentLanguage,
      limitNum: 30,
    };
    this.props.gameCenter.getWinnerBoard(query).then(() => {
      if (this.winnerList.length > 0) {
        this.setState(
          {
            gameList: this.winnerList,
            visibleData: this.winnerList.slice(0, 5),
          },
          () => {
            if (this.winnerList?.length > 5) {
              this.initSwiper();
            }
          }
        );
      }
    });
  }
  initSwiper = () => {
    this.timer = setInterval(
      () => {
        const { gameList, visibleData } = this.state;
        const lastItem = last(gameList);
        const updatedData = [lastItem, ...initial(gameList)];
        const updatedVisibleData = take([lastItem, ...visibleData], 5);

        this.setState({
          gameList: updatedData,
          visibleData: updatedVisibleData,
        });
      },
      random(4, 6) * 1000
    );
  };
  launchGame = (game) => {
    this.props.launchWinnerBoard(game);
  };
  render() {
    if (this.winnerList.length < 1) {
      return null;
    }
    return (
      <div className="winner-board-wrap">
        <div className="winner-board-bg">
          {/* <div className="winner-head">
            <p className="head-game">{this.props.languageShell.t("game")}</p>
            <p className="head-player">{this.props.languageShell.t("user")}</p>
            <p className="head-amount">{this.props.languageShell.t("bonus")}</p>
          </div> */}
          <div className="winner-header">
            <div className="winner-title">{this.props.languageShell.t("latest_winners")}</div>
          </div>
          <div className="winner-content">
            <div className="winner-list">
              {this.state.visibleData.map((item, index) => {
                return (
                  <div
                    className={cz("winner-item", { on: index === 0 })}
                    key={`winner-${item.recordId}`}
                    onClick={() => this.launchGame(item)}
                  >
                    {/* <div className="item-game">
                      <div className="item-img" style={{ backgroundImage: `url(${item.iconUrl})` }} />
                    </div> */}
                    <div className="item-user">
                      <span>{item.customerName}</span>
                      <span className="item-game">{item.gameName}</span>
                    </div>
                    <div className="item-amount">
                      {/* <span className="symbol">{this.props.tcgCommon.currencySymbol}</span> */}+
                      <span>{currencyFormat.format({ value: item.winAmount, fixed: 0 })}</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    );
  }
}
