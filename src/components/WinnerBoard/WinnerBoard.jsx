import React, { Component } from "react";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { debounce } from "lodash";
import { chunk } from "lodash";
import { inject, observer } from "mobx-react";
import PubSub from "pubsub-js";
import { currencyFormat } from "tcg-mobile-common";
import MC from "tcgmodulemc";

import withGame from "@/hoc/withGame";
import { remToPx } from "@/utils/dom";

import "./WinnerBoard.scss";

@inject("languageShell", "gameCenter", "tcgCommon")
@withGame
@observer
export default class WinnerBoard extends Component {
  // get winnerList() {
  //   let arr = cloneDeep(this.props.gameCenter.winnerList.slice(3));
  //   if (arr.length % 2 !== 0) {
  //     arr.pop();
  //   }
  //   return arr;
  // }
  get winnerList() {
    return this.props.gameCenter.winnerList || [];
  }
  get winnerGroup() {
    return chunk(this.winnerList, 2);
  }
  get biggestWinner() {
    return this.props.gameCenter.winnerList.slice(0, 10) || [];
  }
  get lastestWinner() {
    return this.props.gameCenter.winnerList.slice(10) || [];
  }
  constructor(props) {
    super(props);
    this.winnerSwiper = null;
  }

  componentDidMount() {
    this.getWinnerBoard();
    this.changeList = PubSub.subscribe(
      "changeLanguage",
      debounce(() => {
        this.getWinnerBoard();
      }, 250)
    );
  }
  componentWillUnmount() {
    PubSub.unsubscribe(this.changeList);
  }
  getWinnerBoard() {
    const query = {
      gameCategory: "ALL",
      language: this.props.languageShell.currentLanguage,
      limitNum: 30,
    };
    this.props.gameCenter.getWinnerBoard(query).then(() => {
      if (this.winnerList?.length > 0) this.initSwiper();
    });
  }
  initSwiper = () => {
    if (this.winnerSwiper) {
      this.winnerSwiper?.destroy(true, true);
    }
    this.winnerSwiper = new MC.Swiper(this.eventSwiper, {
      loop: this.winnerList.length > 7,
      direction: "vertical",
      speed: 1500,
      slidesPerView: "auto",
      spaceBetween: remToPx(0.32),
      freeMode: true,
      autoplay:
        this.winnerList.length > 7
          ? {
              delay: 0,
              disableOnInteraction: false,
            }
          : false,
    });
  };
  launchGame = (game) => {
    this.props.launchWinnerBoard(game);
  };
  render() {
    if (this.winnerList.length < 1) {
      return null;
    }
    return (
      <div className={cz("winner-board-wrap", this.props.className)}>
        <div className="winner-board-bg">
          <div className="winner-content">
            <div className="swiper-container  winner-swiper" ref={(c) => (this.eventSwiper = c)}>
              <div className="swiper-wrapper winner-list">
                {this.winnerList.map((item, index) => {
                  return (
                    <div className={"winner-item swiper-slide "} key={`biggest-winner-${item.recordId}`}>
                      <Icon className="icon-user" type={require("!svg-sprite-loader!./images/icon-user.svg")} />
                      <div className="winner-info">
                        {/* <img className="game-img" src={item.iconUrl} alt="" /> */}
                        <div className="winner-game-info">
                          <div className="winner-name">
                            <span>{item.customerName}</span>
                          </div>
                          <div className="winner-game">
                            <span>won</span>
                            <span>{item.gameName}</span>
                          </div>
                        </div>
                        <div className="winner-amount">
                          {/* <span className="symbol">{this.props.tcgCommon.currencySymbol}</span> */}
                          <span>{currencyFormat.format({ value: item.winAmount, fixed: 0 })}</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
