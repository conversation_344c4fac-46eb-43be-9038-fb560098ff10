.winner-board-wrap {
  position: relative;
  z-index: 1;
  width: 100%;
  padding: 0 32px;
  margin-top: 32px;

  .winner-board-bg {
    position: relative;
    width: 100%;
    height: 892px;
    padding: 156px 42px 0 43px;
    background: url(~@/assets/images/bg/winner-bg.png) no-repeat center/100% 100%;
  }

  .winner-content {
    position: relative;
    width: 100%;
    height: 680px;
    padding: 24px;
    overflow: hidden;

    .winner-wrap {
      width: 100%;
    }

    .winner-swiper {
      width: 100%;
      height: 100%;
      overflow: hidden;

      .swiper-wrapper {
        height: 100%;
        transition-timing-function: linear;
      }
    }

    .winner-item {
      display: flex;
      gap: 12px;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 64px;
      overflow: hidden;
    }

    .winner-info {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: space-between;

      .winner-name {
        font-size: 24px;
        font-weight: 700;
        line-height: normal;
        color: var(--text-color-accent);
      }

      .winner-game {
        @include ellipsis;
        display: flex;
        gap: 4px;
        align-items: center;
        font-size: 20px;
        font-weight: 400;
        line-height: normal;
        color: var(--text-color-secondary);
      }
    }

    .icon-user {
      display: block;
      flex-shrink: 0;
      width: 64px;
      height: 64px;
      object-fit: contain;
    }

    .game-img {
      flex-shrink: 0;
      width: 160px;
      height: 160px;
      margin-right: 16px;
      border-radius: 12px;
    }

    .winner-name {
    }

    .winner-amount {
      display: flex;
      align-items: center;
      font-size: 28px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      color: var(--text-color-highlight);
      -webkit-text-stroke-width: 1px;
      -webkit-text-stroke-color: #071829;
      paint-order: stroke fill;
    }

    .btn-play {
      @include flex-center;
      flex-shrink: 0;
      width: 128px;
      height: 50px;
      font-size: 20px;
      font-weight: 700;
      line-height: normal;
      color: #fff;
      text-align: center;
    }
  }
}
