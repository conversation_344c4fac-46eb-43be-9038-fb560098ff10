// $width-game: 125px;
// $width-user: 220px;
// $width-amount: 240px;

// .winner-board-wrap {
//   position: relative;
//   z-index: 1;
//   width: 100%;
//   padding: 0 20px;
//   margin-top: 66px;

//   .winner-header {
//     @include flex-center;
//     position: relative;
//     width: 100%;
//     height: 96px;
//     background: url("~@/assets/images/home/<USER>") no-repeat center/cover;

//     &::before {
//       position: absolute;
//       bottom: 0;
//       left: 15px;
//       width: 132px;
//       height: 132px;
//       content: "";
//       background: url("~@/assets/images/home/<USER>") no-repeat center/cover;
//     }
//   }

//   .winner-title {
//     font-size: 30px;
//     font-weight: 700;
//     line-height: normal;
//     color: #fff;
//     letter-spacing: 0.216px;
//   }

//   .winner-content {
//     position: relative;
//     width: 100%;
//     overflow-anchor: none;

//     .winner-list {
//       width: 100%;
//       overflow: hidden;
//     }

//     .game-list-item {
//       width: 100px;
//     }

//     .winner-item {
//       display: flex;
//       align-items: center;
//       justify-content: space-between;
//       width: 100%;
//       padding: 20px 25px;
//       margin: 0;
//       background: rgb(255 255 255 / 10%);
//       border: 1px solid rgb(0 0 0 / 20%);
//       backdrop-filter: blur(25px);

//       &.on {
//         animation: item-slide-in 0.3s ease-in-out;
//       }

//       .item-user {
//         display: grid;
//         gap: 12px;
//         font-size: 22px;
//         font-weight: 700;
//         line-height: normal;
//         color: #9784bf;
//         letter-spacing: 0.216px;
//       }

//       .item-game {
//         font-size: 24px;
//         line-height: normal;
//         color: #fff;
//         letter-spacing: 0.216px;
//       }

//       .item-amount {
//         @include text-gradient;
//         font-size: 28px;
//         font-weight: 700;
//         line-height: normal;
//         letter-spacing: 0.216px;
//       }
//     }
//   }

//   .item-placeholder {
//     visibility: hidden;
//     height: 120px;
//     opacity: 0;
//   }
// }

// @keyframes item-slide-in {
//   from {
//     opacity: 0;
//     transform: translateX(100%);
//   }

//   to {
//     opacity: 1;
//     transform: translateX(0);
//   }
// }
