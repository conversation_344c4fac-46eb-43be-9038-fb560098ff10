import React from "react";
import { isEqual } from "lodash";
import { utils } from "tcgmodulemc";

import { get } from "../../utils/storage";

import VNLottItem from "./VNLottItem";

const { appStorageUtils } = utils;

class VNLottList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      lottGames: [],
    };
  }
  componentDidMount() {
    this.loadFav();
  }
  componentDidUpdate(prevProps, prevState) {
    if (!isEqual(prevProps.lottList, this.props.lottList)) {
      this.loadFav();
    }
  }
  initData = async () => {
    const lottFav = await appStorageUtils.storageGetItem("LOTTFAV");
    let allGame = this.props.lottList;
    let favGame = allGame.filter((item) => {
      return lottFav.includes(item.gameId);
    });
    let notFavGame = allGame.filter((item) => {
      return !lottFav.includes(item.gameId);
    });
    favGame.sort((prev, next) => {
      return lottFav.indexOf(next.gameId) - lottFav.indexOf(prev.gameId);
    });
    this.setState({
      lottGames: [...favGame, ...notFavGame],
    });
  };
  loadFav = async () => {
    const lottFav = await appStorageUtils.storageGetItem("LOTTFAV");
    const userInfo = get("MC_SESSION_INFO");
    const lottFavObj = lottFav ? JSON.parse(lottFav) : {};
    if (userInfo && lottFavObj[userInfo.userName]) {
      this.initData();
    } else {
      this.setState({
        lottGames: this.props.lottList,
      });
    }
  };
  sortGame = () => {
    this.loadFav();
  };
  render() {
    return (
      <div className="vnlott-list">
        {this.state.lottGames &&
          this.state.lottGames.map((item) => {
            return (
              <VNLottItem
                dataType={this.props.dataType}
                push={this.props.push}
                sortGame={this.sortGame}
                key={item.code}
                lott={item}
                deleteGame={this.props.deleteGame}
                lotteryTime={this.props.lotteryTime}
                gameClick={this.props.gameClick}
              />
            );
          })}
      </div>
    );
  }
}

export default VNLottList;
