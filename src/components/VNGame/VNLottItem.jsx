import React from "react";
import { Icon, Toast } from "antd-mobile";
import { inject, observer } from "mobx-react";
import { utils } from "tcgmodulemc";

import gameApi from "@/apis/gameCenter";
import { get } from "@/utils/storage";

import LotteryTime from "../Lottery/LotteryTime";

const { appStorageUtils } = utils;

const iconHeart = require("!svg-sprite-loader!@/assets/images/common/icon-heart.svg");
const iconHeartActive = require("!svg-sprite-loader!@/assets/images/common/icon-heart-active.svg");

@inject("languageShell")
@observer
class VNLottItem extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      favList: [],
      isFav: false,
    };
  }
  componentDidMount() {
    this.loadFav();
  }
  loadFav = async () => {
    const lottFav = await appStorageUtils.storageGetItem("LOTTFAV");
    const userInfo = get("MC_SESSION_INFO");
    const lottFavObj = lottFav ? JSON.parse(lottFav) : {};
    if (userInfo && lottFavObj[userInfo.userName]) {
      this.setState({
        favList: lottFavObj[userInfo.userName],
        isFav: lottFavObj[userInfo.userName].includes(this.props.lott.gameId),
      });
    }
  };
  launchGame = () => {
    if (this.props.gameClick) {
      this.props.gameClick(this.props.lott);
      return;
    }
    if (get("MC_SESSION_INFO")) {
      const { gameCode, code, gameType, vassalage, roomId, nodeId } = this.props.lott;
      if (gameType && vassalage) {
        utils.gcUtils.launchGameV3({
          gameCategory: gameType,
          vendor: vassalage,
          gameId: roomId,
          nodeId: nodeId,
          platform: "MOBILE",
        });
      } else {
        this.startGame(gameCode || code);
      }
    } else {
      this.props.push("/m/login");
    }
  };
  startGame = (gameCode) => {
    Toast.loading(this.props.languageShell.resource["in_please_wait"], 3);
    gameApi
      .launchGame({
        lottoArea: "VN",
        launchMode: "LOTT",
        lottoPrizeMode: "Lott",
        lottoGameCode: gameCode,
        lottoView: gameCode ? "betting" : "lobby",
        device: "MOBILE",
        backUrl: window.location.href,
        language: this.props.languageShell.currentLanguage,
      })
      .then((res) => {
        const { gameUrl } = res.data.value;
        if (gameUrl) {
          window.location.href = `/${gameUrl}`;
        }
      })
      .catch(() => {
        Toast.hide();
      });
  };
  toggleFav = (e, gameId) => {
    e.stopPropagation();
    const userInfo = get("MC_SESSION_INFO");
    if (userInfo) {
      this.setState(
        {
          isFav: !this.state.isFav,
        },
        async (state) => {
          const current = await appStorageUtils.storageGetItem("LOTTFAV");
          const lottFav = current ? JSON.parse(current) : {};
          const list = lottFav[userInfo.userName] || [];
          const favSet = new Set(list);
          if (this.state.isFav) {
            favSet.add(gameId);
          } else {
            favSet.delete(gameId);
            if (this.props.dataType === "fav") {
              this.props.deleteGame(gameId);
            }
          }
          lottFav[userInfo.userName] = Array.from(favSet);
          appStorageUtils.storageSetItem("LOTTFAV", JSON.stringify(lottFav));
          let game = await appStorageUtils.storageGetItem("LOTTFAV");
          this.props.sortGame();
        }
      );
    } else {
      this.props.push("/m/login");
    }
  };
  render() {
    const { code, mobileIconUrl, remark, gameId, isSale, remainTime, lockTime, showIcon, gameType } = this.props.lott;
    return (
      <div className="list-item" key={code} onClick={this.launchGame}>
        <div className="lott-img">
          {/* <img className="vnlott-icon" src={mobileIconUrl || showIcon} alt="" /> */}
          <div className="vnlott-icon" style={{ backgroundImage: `url(${mobileIconUrl || showIcon})` }} />
          <div className={`fav-icon ${this.state.isFav}`} onClick={(e) => this.toggleFav(e, gameId)}>
            <Icon type={this.state.isFav ? iconHeartActive : iconHeart} />
          </div>
          {!gameType ? (
            <div className="lottime_box">
              <LotteryTime
                isSale={isSale}
                remainTime={Math.max(remainTime - lockTime * 1000, 0)}
                lotteryTime={this.props.lotteryTime}
              />
            </div>
          ) : (
            ""
          )}
        </div>
        <div className="vnlott-name">{remark}</div>
      </div>
    );
  }
}

export default VNLottItem;
