import React from "react";
import { isEmpty } from "lodash";
import { inject, observer } from "mobx-react";

import gameApi from "../../apis/gameCenter";
import deploy from "../../config/deploy.config";
import Lottery from "../Lottery/Lottery";
import NoData from "../NoData/NoData";

import "./VNGame.scss";

@inject("languageShell", "gameCenter")
@observer
class VNGame extends React.Component {
  state = {
    currentWeek: -1,
    lottList: [],
    lottTag: "",
    gameMenus: [],
    lotteryTime: "",
    activeIndex: 0,
    lottGames: [],
    isGameList: false,
  };
  componentDidMount() {
    this.getLottMenu();
  }
  componentWillUnmount() {
    window.clearTimeout(this.vnTimer);
  }
  getLottMenu = () => {
    const firstTime = new Date().getTime();
    return gameApi
      .getLottMenu({
        prizeMode: this.props.gameCenter.prizeMode,
        device: "MOBILE",
      })
      .then((res) => {
        if (!this.state.gameMenus.length) {
          this.setState(
            {
              gameMenus: this.props.hasGPI ? [...res.data, { labelName: "KENO", tag: "KENO" }] : res.data,
              lottList: res.data || [],
            },
            () => {
              this.memuClick(0);
            }
          );
        }
        if (res.data.length) {
          this.handleLottMenuReturn(firstTime, res);
        }
      });
  };
  handleLottMenuReturn = (firstTime, res) => {
    const { data } = res || {};
    let lastTime = new Date().getTime();
    let counts = { firstTime: firstTime, lastTime: lastTime };
    this.setState({
      lotteryTime: Object.values(counts),
    });
    const menuData = Object.values(data);
    let minRemain;
    if (!isEmpty(menuData)) {
      const vnList = menuData
        .reduce((acc, curr) => [...acc, ...curr.gameMenus], [])
        .filter((game) => !isEmpty(game.numero) && game.remainTime >= 0);
      const validGamesRemainTime = vnList.map(({ remainTime }) => Math.max(remainTime, 0));
      minRemain = Math.min.apply("Math", validGamesRemainTime);
      if (minRemain <= 0) {
        minRemain = 7000;
      } else {
        minRemain = Math.min(30000, minRemain);
      }
    }
    window.clearTimeout(this.vnTimer);
    this.vnTimer = setTimeout(this.getLottMenu, minRemain);
    return menuData;
  };
  memuClick = (index, item) => {
    this.setState(
      {
        activeIndex: index,
        lottGames: [],
      },
      () => {
        if (item && item.tag === "KENO") {
          this.startGpiGame();
          this.setState({
            isGameList: true,
          });
        } else {
          this.setState({
            lottGames: this.state.lottList[index]?.gameMenus || [],
            isGameList: false,
            lottTag: this.state.lottList[index]?.tag,
          });
        }
      }
    );
  };
  startGpiGame = () => {
    this.props.gameCenter
      .getGameList({
        merchant: deploy.merchant,
        platform: "html5",
        gameType: "ELOTT",
        pageNo: 1,
        pageSize: 1000,
        vassalage: "GPI",
        language: this.props.languageShell.currentLanguage,
      })
      .then((res) => {
        this.setState({
          lottGames: res.games || [],
        });
      });
  };
  viewAll = () => {
    this.props.push("/m/lottery");
  };
  filterDataByWeek = (week) => {
    const results = this.state.lottList[this.state.activeIndex]["gameMenus"].filter(({ drawDayOfWeek }) => {
      return drawDayOfWeek.split(",").includes(`${week}`);
    });
    this.setState({
      currentWeek: week,
      lottGames: results,
    });
  };
  render() {
    return (
      <div className="lott-list-wrap hide-scrollbar">
        <div className="lottGame-list">
          {/* {["vietnam_central", "vietnam_southern"].includes(this.state.lottTag) ? (
            <div className="week-menu">
              {weeks.map((item, index) => {
                return (
                  <div
                    key={`lottItem-${index}`}
                    className={cz("lott-item", { on: this.state.currentWeek === index + 1 })}
                    onClick={() => this.filterDataByWeek(item.value)}
                  >
                    <span>{this.props.languageShell.t(item.name)}</span>
                  </div>
                );
              })}
            </div>
          ) : null} */}
          {this.state.lottGames.length ? (
            <div className="lottGame-scroll">
              <Lottery
                key={`lott_${this.state.activeIndex}`}
                push={this.props.push}
                lotteryTime={this.props.lotteryTime}
                lottList={this.state.lottGames}
              />
            </div>
          ) : (
            <NoData />
          )}
        </div>
      </div>
    );
  }
}

export default VNGame;
