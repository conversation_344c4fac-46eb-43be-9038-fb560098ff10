import React from "react";

class LotteryTime extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      times: "--:--:--",
    };
    this.timers = null;
    this.theLast = this.theLast.bind(this);
    this.timeFun = this.timeFun.bind(this);
  }

  componentDidMount() {
    this.timeFun();
  }

  componentDidUpdate(prevProps, prevState) {
    if (
      prevProps.remainTime !== this.props.remainTime ||
      prevProps.lotteryTime !== this.props.lotteryTime ||
      prevProps.isSale !== this.props.isSale
    ) {
      this.timeFun();
    }
  }

  componentWillUnmount() {
    clearInterval(this.timers);
  }

  timeFun() {
    clearInterval(this.timers);
    if (!this.props.isSale) {
      this.setState({
        times: "--:--:--",
      });
      return;
    }

    const { remainTime = 0, lotteryTime = [0, 0] } = this.props;
    const last = Math.max(Math.floor((remainTime - (lotteryTime[1] - lotteryTime[0]) / 2) / 1000), 0);
    const drawTime = new Date().getTime() + last * 1000;
    this.timers = setInterval(() => {
      const diff = drawTime - new Date().getTime();
      this.theLast(Math.ceil(diff / 1000));
    }, 500);
  }

  theLast(time) {
    if (time) {
      let t = "";
      if (time > 0) {
        const hour = Math.floor(time / 3600);
        const min = Math.floor(time / 60) % 60;
        const sec = time % 60;
        if (hour < 10) {
          t = "0" + hour + ":";
        } else {
          t = hour + ":";
        }
        if (min < 10) {
          t += "0";
        }
        t += min;
        let tsc = ":";
        if (sec < 10) {
          tsc += "0";
        }
        tsc += sec.toFixed(0);
        t += tsc;
        this.setState({ times: t });
      } else {
        this.setState({ times: "--:--:--" });
      }
    } else {
      this.setState({ times: "--:--:--" });
    }
  }

  render() {
    return <span className="lottery-time">{this.state.times}</span>;
  }
}

export default LotteryTime;
