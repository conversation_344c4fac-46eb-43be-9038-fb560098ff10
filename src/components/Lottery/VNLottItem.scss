.lottery-item {
  position: relative;
  flex-shrink: 0;
  width: 100%;
  padding: 24px;
  overflow: hidden;
  background-image: linear-gradient(180deg, #39260f 0%, #000 100%), linear-gradient(180deg, #f8cc3f, #974c0e);
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  border: 3px solid transparent;
  border-radius: 20px;
  clip-path: inset(0 round 20px);

  .lottery-title {
    flex: 1;
    line-height: normal;
  }

  .lottery-icon {
    display: block;
    flex-shrink: 0;
    width: 120px;
    height: 120px;
    margin-right: 20px;
    overflow: hidden;
    border-radius: 50%;

    img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .lottery-info {
    display: flex;
    align-items: center;
  }

  .lottery-remark {
    width: 100%;
    font-size: 28px;
    font-weight: 700;
    line-height: normal;
    color: #fff;

    @include ellipsis;
  }

  .lottery-numero {
    margin-top: 8px;
    font-size: 24px;
    color: #d8c98e;
  }

  .lottery-time {
    font-size: 28px;
    color: #fff;
  }

  .draw-time {
    display: flex;
    align-items: center;
    margin-top: 8px;
    font-size: 20px;
    font-weight: 400;
    line-height: normal;
    color: rgb(255 255 255 / 50%);
  }

  .lottery-bet {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    overflow: hidden;
  }

  .lottery-number {
    display: flex;
    flex: 1;
    align-items: center;
    overflow: hidden;
  }

  .btn-bet {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    height: 40px;
    padding: 0 14px;
    margin-left: 10px;
    font-size: 24px;
    font-weight: 700;
    line-height: normal;
    color: #000;
    text-align: center;
    text-transform: uppercase;
    background: linear-gradient(180deg, #f8cc3f 0%, #ffed8e 50%, #974c0e 100%);
    border-radius: 50px;
  }

  .h-line {
    width: 100%;
    height: 1px;
    margin: 12px 0;
    background: rgb(255 255 255 / 30%);
    transform: scaleY(0.5);
  }

  .num-list {
    display: flex;
    align-items: center;
    width: 100%;
    overflow: auto;

    .num-item {
      flex-shrink: 0;
      width: 44px;
      height: 44px;
      font-size: 28px;
      color: #000;
      text-align: center;
      background: linear-gradient(180deg, #fff094 0%, #fef3c8 53%, #c5b472 100%);
      border-radius: 50%;

      @include flex-center;
    }

    .num-item:not(:last-child) {
      margin-right: 10px;
    }
  }

  .fav-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 43px;
    height: 40px;

    svg {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}
