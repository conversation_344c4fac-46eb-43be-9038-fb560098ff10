import React from "react";
import { Icon, Toast } from "antd-mobile";
import cz from "classnames";
import dayjs from "dayjs";
import { inject, observer } from "mobx-react";
import { utils } from "tcgmodulemc";

import gameApi from "@/apis/gameCenter";
import { get } from "@/utils/storage";

import LotteryTime from "./LotteryTime";

import iconStar from "!svg-sprite-loader!@/assets/images/common/icon-star.svg";

const { appStorageUtils } = utils;
const DAYTIME = 24 * 60 * 60 * 1000;

@inject("common", "languageShell")
@observer
class LotteryItem extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      favList: [],
      isFav: false,
    };
  }
  componentDidMount() {
    this.loadFav();
  }
  loadFav = async () => {
    const lottFav = await appStorageUtils.storageGetItem("LOTTFAV");
    const userInfo = get("MC_SESSION_INFO");
    const lottFavObj = lottFav ? JSON.parse(lottFav) : {};
    if (userInfo && lottFavObj[userInfo.userName]) {
      this.setState({
        favList: lottFavObj[userInfo.userName],
        isFav: lottFavObj[userInfo.userName].includes(this.props.lott.gameId),
      });
    }
  };
  toggleFav = (e, gameId) => {
    e.stopPropagation();
    const userInfo = get("MC_SESSION_INFO");
    if (userInfo) {
      this.setState(
        {
          isFav: !this.state.isFav,
        },
        async (state) => {
          const current = await appStorageUtils.storageGetItem("LOTTFAV");
          const lottFav = current ? JSON.parse(current) : {};
          const list = lottFav[userInfo.userName] || [];
          const favSet = new Set(list);
          if (this.state.isFav) {
            favSet.add(gameId);
          } else {
            favSet.delete(gameId);
          }
          lottFav[userInfo.userName] = Array.from(favSet);
          appStorageUtils.storageSetItem("LOTTFAV", JSON.stringify(lottFav));
          let game = await appStorageUtils.storageGetItem("LOTTFAV");
          this.props.sortGame();
        }
      );
    } else {
      this.props.push("/m/login");
    }
  };
  startGame = (gameCode) => {
    Toast.loading(this.props.languageShell.resource["in_please_wait"], 3);
    gameApi
      .launchGame({
        lottoArea: "VN",
        launchMode: "LOTT",
        lottoPrizeMode: "Lott",
        lottoGameCode: gameCode,
        lottoView: gameCode ? "betting" : "lobby",
        device: "MOBILE",
        backUrl: window.location.href,
        language: this.props.languageShell.currentLanguage,
      })
      .then((res) => {
        const { gameUrl } = res.data.value;
        if (gameUrl) {
          window.location.href = `/${gameUrl}`;
        }
      })
      .catch(() => {
        // 只有成功後才關閉, 避免轉換網址時toast太早消失.
        Toast.hide();
      });
  };
  launchGame = (game) => {
    if (get("MC_SESSION_INFO")) {
      const { gameCode, code, gameType, vassalage, roomId, nodeId } = game;
      if (gameType && vassalage) {
        utils.gcUtils.launchGameV3({
          gameCategory: gameType,
          vendor: vassalage,
          gameId: roomId,
          nodeId: nodeId,
          platform: "MOBILE",
        });
      } else {
        this.startGame(gameCode || code);
      }
    } else {
      this.props.push("/m/login");
    }
  };
  formatWinningTime = (time) => {
    return dayjs(time).format("YYYY.MM.DD");
  };
  formatWinningDay = (time) => {
    return this.props.languageShell.t(`week_${dayjs(time).day()}`);
  };
  render() {
    const {
      mobileIconUrl,
      showIcon,
      winningTime,
      remark,
      nodeName,
      numero,
      tag,
      isSale,
      remainTime,
      lockTime,
      gameId,
    } = this.props.lott;
    const countTime = Math.max(remainTime - lockTime * 1000, 0);
    return (
      <li className={`lott-game-item`} onClick={() => this.launchGame(this.props.lott)}>
        <div className={`lott-game-bg`}>
          <div className="lott-game-info">
            <div className="lott-icon shine">
              <img src={mobileIconUrl || showIcon} alt="" />
            </div>
            <div className="lott-name">
              <h6>{remark || nodeName}</h6>
              <LotteryTime
                isSale={isSale}
                remainTime={Math.max(remainTime - lockTime * 1000, 0)}
                lotteryTime={this.props.lotteryTime}
              />
            </div>
          </div>
          <div className="lott-game">
            {winningTime && (
              <div className="win-time">
                <span className="draw-time">{this.props.languageShell.t("draw_time")}</span>
                <span className="winning-time">{this.formatWinningTime(winningTime)}</span>
              </div>
            )}
          </div>
          <div className={cz("fav-icon", { on: this.state.isFav })} onClick={(e) => this.toggleFav(e, gameId)}>
            <Icon type={iconStar} />
          </div>
        </div>
      </li>
    );
  }
}

export default LotteryItem;
