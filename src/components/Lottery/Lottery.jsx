import React from "react";
import { isEqual } from "lodash";
import { inject, observer } from "mobx-react";
import { utils } from "tcgmodulemc";

import { get } from "../../utils/storage";

import LotteryItem from "./LotteryItem";

// Import Common Stylesheets
import "./Lottery.scss";

const { appStorageUtils } = utils;
class Lottery extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      lottGames: [],
      currentWeek: -1,
    };
  }
  componentDidMount() {
    this.loadFav();
  }
  componentDidUpdate(prevProps, prevState) {
    if (!isEqual(prevProps.lottList, this.props.lottList)) {
      this.loadFav();
    }
  }
  initData = async () => {
    const lottFav = await appStorageUtils.storageGetItem("LOTTFAV");
    let allGame = this.props.lottList;
    let favGame = allGame.filter((item) => {
      return lottFav.includes(item.gameId);
    });
    let notFavGame = allGame.filter((item) => {
      return !lottFav.includes(item.gameId);
    });
    favGame.sort((prev, next) => {
      return lottFav.indexOf(next.gameId) - lottFav.indexOf(prev.gameId);
    });
    this.setState({
      lottGames: [...favGame, ...notFavGame],
    });
  };

  loadFav = async () => {
    const lottFav = await appStorageUtils.storageGetItem("LOTTFAV");
    const userInfo = get("MC_SESSION_INFO");
    const lottFavObj = lottFav ? JSON.parse(lottFav) : {};
    if (userInfo && lottFavObj[userInfo.userName]) {
      this.initData();
    } else {
      this.setState({
        lottGames: this.props.lottList,
      });
    }
  };
  sortGame = () => {
    this.loadFav();
  };
  render() {
    return (
      <div className="lottery">
        <ul className={`lott-list`}>
          {this.state.lottGames.map((item) => {
            return (
              <LotteryItem
                lotteryTime={this.props.lotteryTime}
                key={item.gameId}
                sortGame={this.sortGame}
                lott={item}
                push={this.props.push}
              />
            );
          })}
        </ul>
      </div>
    );
  }
}
export default inject("mcCommon", "login", "common", "auth", "languageShell")(observer(Lottery));
