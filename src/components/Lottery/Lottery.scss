.lottery {
  width: 100%;

  img {
    display: block;
    width: 100%;
    height: 100%;
  }

  .lott-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px 33px;
    width: 100%;

    &.game-list {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 30px;

      .lott-game-item {
        width: 100%;
        height: auto;

        .lott-game-bg {
          width: 100%;
          background: none !important;

          .lott-game-icon {
            position: relative;
            width: 100%;
            padding-bottom: 100%;

            img.icon {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }

    .lott-game-item {
      width: 100%;
      height: 160px;
      overflow: hidden;
    }

    .lott-game-bg {
      position: relative;
      height: 100%;
      padding: 20px 14.8px 5px 18px;
      border-radius: 12px;

      .fav-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        color: #595959;

        &.on {
          color: $main-color;
        }

        svg {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
    }

    .lott-game {
      width: 100%;
      margin-top: 10px;
    }

    .lott-name {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: center;
      width: 100%;
      padding-right: 50px;

      h6 {
        font-size: 24px;
        color: #fff;

        @include ellipsis;
      }
    }

    .lott-game-info {
      display: flex;
      align-items: center;

      .lott-icon {
        position: relative;
        flex-shrink: 0;
        width: 60px;
        height: 60px;
        margin-right: 15px;
        overflow: hidden;

        img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
    }

    .draw-time {
      display: flex;
      align-items: flex-end;
      width: 68px;
      height: 56px;
    }

    .win-time {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      font-size: 24px;
    }
  }

  .lottery-time {
    margin-top: 5px;
    font-size: 24px;
    line-height: 1;
    color: $main-color;
  }
}
