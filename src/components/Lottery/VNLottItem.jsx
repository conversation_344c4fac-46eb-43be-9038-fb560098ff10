import React from "react";
import { withRouter } from "react-router";
import cz from "classnames";
import dayjs from "dayjs";
import { inject, observer } from "mobx-react";
import { withGame } from "tcg-mobile-common";
import { utils } from "tcgmodulemc";

import deploy from "@/config/deploy.config";
import { get } from "@/utils/storage";

import LotteryTime from "./LotteryTime";

import "./VNLottItem.scss";

const { appStorageUtils } = utils;

const DAYTIME = 24 * 60 * 60 * 1000;

const iconHeart = require("!svg-sprite-loader!@/assets/images/common/game-fav-normal.svg");
const iconHeartActive = require("!svg-sprite-loader!@/assets/images/common/game-fav-active.svg");

@withGame
@withRouter
@inject("languageShell")
@observer
class VNLotteryItem extends React.Component {
  state = {
    favList: [],
    isFav: false,
  };
  componentDidMount() {
    this.loadFav();
  }
  formatWinningTime = (time) => {
    return time ? dayjs(time).format("YYYY.MM.DD") : "";
  };
  renderNumber = (num) => {
    if (!num) {
      return null;
    }
    let arr = [];
    if (num.indexOf(",") > 0 && num.indexOf("~") > 0) {
      arr = num.split("~")[0].split("");
    } else if (num.indexOf(",") > 0 && num.split(",")[0].split("").length > 3) {
      arr = num.split(",")[0].split("");
    } else {
      arr = num.indexOf(",") > 0 ? num.split(",") : num.split("");
    }
    return (
      <div className="num-list hide-scrollbar">
        {arr.map((item, index) => {
          return (
            <div className="num-item" key={`num_${index}`}>
              {item}
            </div>
          );
        })}
      </div>
    );
  };
  launchGame = () => {
    const { code, gameCode, vendor } = this.props.lott;
    if (this.props.gameClick) {
      return this.props.gameClick(code);
    }
    const mcSessionInfo = get("MC_SESSION_INFO");
    if (mcSessionInfo) {
      this.props.launchLott({
        vendor,
        gameCode: gameCode || code,
        prizeMode: vendor === "TCG_SEA" ? "SEA" : deploy.prizeMode,
      });
    } else {
      this.props.history.push("/m/login");
    }
  };
  loadFav = async () => {
    const lottFav = await appStorageUtils.storageGetItem("LOTTFAV");
    const userInfo = get("MC_SESSION_INFO");
    const lottFavObj = lottFav ? JSON.parse(lottFav) : {};
    if (userInfo && lottFavObj[userInfo.userName]) {
      this.setState({
        favList: lottFavObj[userInfo.userName],
        isFav: lottFavObj[userInfo.userName].includes(this.props.lott.gameId),
      });
    }
  };
  toggleFav = (e, gameId) => {
    e.stopPropagation();
    const userInfo = get("MC_SESSION_INFO");
    if (userInfo) {
      this.setState(
        {
          isFav: !this.state.isFav,
        },
        async (state) => {
          const current = await appStorageUtils.storageGetItem("LOTTFAV");
          const lottFav = current ? JSON.parse(current) : {};
          const list = lottFav[userInfo.userName] || [];
          const favSet = new Set(list);
          if (this.state.isFav) {
            favSet.add(gameId);
          } else {
            favSet.delete(gameId);
            if (this.props.dataType === "fav") {
              this.props.deleteGame(gameId);
            }
          }
          lottFav[userInfo.userName] = Array.from(favSet);
          appStorageUtils.storageSetItem("LOTTFAV", JSON.stringify(lottFav));
          let game = await appStorageUtils.storageGetItem("LOTTFAV");
          this.props.sortGame();
        }
      );
    } else {
      this.props.history.push("/m/login");
    }
  };
  render() {
    const { mobileIconUrl, gameId, previousWinningNumber, numero, remark, isSale, remainTime, lockTime, winningTime } =
      this.props.lott;
    const countTime = Math.max(remainTime - lockTime * 1000, 0);
    return (
      <div
        className={cz("lottery-item", { on: countTime < DAYTIME && countTime > 0, hasFav: this.props.hasFav })}
        onClick={this.launchGame}
      >
        <div className="lottery-info">
          <div className="lottery-icon">
            <img src={mobileIconUrl} alt="" />
          </div>
          <div className="lottery-title">
            <div className="lottery-remark">{remark}</div>
            <div className="lottery-numero">{numero}</div>

            <div className="draw-time">
              <span>{this.props.languageShell.t("draw_time")}:&nbsp;</span>
              <LotteryTime
                isSale={isSale}
                remainTime={Math.max(remainTime - lockTime * 1000, 0)}
                lotteryTime={this.props.lotteryTime}
              />
              {/* <span className="win-time">{this.formatWinningTime(winningTime)}</span> */}
            </div>
          </div>
        </div>
        {/* {this.props.hasFav && (
          <div className={`fav-icon ${this.state.isFav}`} onClick={(e) => this.toggleFav(e, gameId)}>
            <Icon type={this.state.isFav ? iconHeartActive : iconHeart} />
          </div>
        )} */}
        <div className="h-line" />
        <div className="lottery-bet">
          <div className="lottery-number">{this.renderNumber(previousWinningNumber)}</div>
          <span className="btn-bet">{this.props.languageShell.t("in_bet_now")}</span>
        </div>
      </div>
    );
  }
}

VNLotteryItem.defaultProps = {
  hasFav: false,
};

export default VNLotteryItem;
