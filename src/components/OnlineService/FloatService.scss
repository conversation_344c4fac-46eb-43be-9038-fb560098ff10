.float-service {
  position: fixed;
  right: 20px;
  bottom: 30%;
  z-index: $float-service-index;
  transition: all 0.2s;

  .float-service-bg {
    position: relative;
    width: 212px;
    height: 648px;
  }

  .service-list {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .service-item {
      @include flex-center;
      position: relative;
      z-index: 5;
      width: 72px;
      height: 72px;
      border: 1px solid var(--border-color-default);
      border-radius: 100px;

      &.LIVECHAT {
        background: var(--bg-color-gradient-secondary);
      }

      &.TELEGRAM {
        background: var(--bg-color-gradient-primary);
      }

      .service-icon {
        display: block;
        width: 48px;
        height: 48px;
        object-fit: contain;
      }
    }
  }

  .toggle-btn {
    @include flex-center;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 4;
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }

  .sub-service {
    .service-item {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1;
      opacity: 0;
      transition: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &.on {
      .service-item {
        opacity: 1;
      }

      .service-item:nth-child(1) {
        top: calc(100% + 24px);
        right: 10px;
      }

      .service-item:nth-child(2) {
        top: calc(100% - 8px);
        right: calc(100% + 13px);
      }

      .service-item:nth-child(3) {
        top: -8px;
        right: calc(100% + 26px);
      }
    }
  }
}

.demo-floating-box-menu {
  .toggle-service {
    width: 120px;
    height: 120px;

    img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .service-item {
    width: 100px;
    height: 100px;

    .service-icon {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}
