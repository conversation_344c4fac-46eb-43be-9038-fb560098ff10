import React from "react";
import { Icon } from "antd-mobile";
import { withCs } from "tcg-mobile-common";

@withCs
class ServiceIcon extends React.Component {
  render() {
    return (
      <div className="service-btn-wrap">
        <div className="service-btn" onClick={this.props.openCs}>
          <Icon
            className="service-icon"
            type={require("!svg-sprite-loader!@/assets/images/common/customer-service.svg")}
          />
        </div>
      </div>
    );
  }
}

export default ServiceIcon;
