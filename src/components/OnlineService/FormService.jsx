import React from "react";
import { inject, observer } from "mobx-react";
import { withCs } from "tcg-mobile-common";

import "./FormService.scss";

@inject("languageShell")
@withCs
@observer
class FormService extends React.Component {
  render() {
    return (
      <div className="already-account">
        <span>{this.props.languageShell.t("still_issue")}</span>
        <span className="login-now" onClick={this.props.openCs}>
          {this.props.languageShell.t("contact_cs")}
        </span>
      </div>
    );
  }
}

export default FormService;
