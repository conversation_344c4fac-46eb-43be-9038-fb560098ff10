import React from "react";
import { withRouter } from "react-router-dom";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import { navigatorUtil, withCs, withReferral } from "tcg-mobile-common";

import { handleWindowPage } from "@/utils/openLink";

import "./FloatService.scss";

const { isApp, isPWA } = navigatorUtil;
const showDownload = !isApp && !isPWA;

const csPos = ["LIVECHAT-1", "TELEGRAM-1", "TELEGRAM-2"];

const serviceIcon = {
  "LIVECHAT-1": require("!svg-sprite-loader!./images/livechat.svg"),
  "TELEGRAM-1": require("!svg-sprite-loader!./images/telegram1.svg"),
  "TELEGRAM-2": require("!svg-sprite-loader!./images/telegram2.svg"),
};

@inject("common", "tcgCommon", "languageShell", "personal", "auth", "mcMenu")
@withRouter
@withReferral
@withCs
@observer
class FloatService extends React.Component {
  get isLogin() {
    return this.props.auth.isLogin;
  }
  get showReferral() {
    if (!this.isLogin) {
      return true;
    }
    return this.props.showReferral;
  }
  get showMission() {
    if (!this.isLogin) {
      return true;
    }
    return this.props.personal.availablePromosByKey("MISSION").enabled;
  }
  get showVip() {
    if (!this.isLogin) {
      return true;
    }
    return this.props.tcgCommon.showVipBenefit;
  }
  get customerService() {
    return this.props.tcgCommon.customerService.filter((el) => csPos.includes(el.csPosition));
  }
  get resources() {
    return this.props.common.downLinkObj;
  }
  state = { show: false, showService: false };
  componentDidMount() {
    document.addEventListener("click", this.handleClickOutside);
  }
  componentWillUnmount() {
    document.removeEventListener("click", this.handleClickOutside);
  }

  handleClickOutside = (e) => {
    if (this.serviceRef && !this.serviceRef.contains(e.target)) {
      this.setState({ showService: false });
    }
  };

  handleDownload = () => {
    this.props.common.setDownPopup(true);
  };
  toggleService = () => {
    this.setState({
      showService: !this.state.showService,
    });
  };
  openService = () => {
    if (this.customerService?.length > 0) {
      this.setState({
        showService: !this.state.showService,
      });
    }
  };
  navClick = (item) => {
    this.toggleService();
    const { path } = item;
    if (path) {
      return this.props.history.push(path);
    }
  };
  openResources = (type) => {
    const link = this.resources[type];
    if (link?.url) {
      handleWindowPage(link?.url);
    }
  };
  render() {
    return (
      <div className="float-service" ref={(c) => (this.serviceRef = c)}>
        <div className="service-list">
          {this.customerService?.map((item, index) => {
            return (
              <div
                className={cz("service-item", item.csType)}
                key={`float_service_${index}`}
                onClick={() => this.props.openCs(item)}
              >
                <Icon className="service-icon" type={serviceIcon[item.csPosition]} />
              </div>
            );
          })}
        </div>
      </div>
    );
  }
}

export default FloatService;
