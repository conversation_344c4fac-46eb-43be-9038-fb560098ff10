import React from "react";
import { inject, observer } from "mobx-react";

import deploy from "@/config/deploy.config";

import PageBanner from "./PageBanner";

@inject("common")
@observer
class FormBanner extends React.Component {
  get banners() {
    if (this.props.bannerType && deploy[this.props.bannerType]) {
      return this.props.common.banners.filter((item) => item.groupName === this.props.bannerType);
    }
    return [{ url: require("@/assets/images/bg/default-banner.png") }];
  }
  componentDidMount() {}

  render() {
    return <PageBanner className="form-banner" slidesPerView={1} banners={this.banners} />;
  }
}

export default FormBanner;
