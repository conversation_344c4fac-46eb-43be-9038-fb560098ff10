import React from "react";
import { withRouter } from "react-router-dom";
import cz from "classnames";
import { isEqual } from "lodash";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { bannerClick, swiperLoader } from "tcg-mobile-common";

import "./PageBanner.scss";

@inject("languageShell")
@withRouter
@bannerClick
@observer
class Banner extends React.Component {
  constructor(props) {
    super(props);
    this.swiper = null;
    this.bannerSwiperRef = null;
    this.bannerPaginationRef = null;
  }
  componentDidMount() {
    if (this.props.banners?.length > 0) {
      this.initSwiper();
    }
  }
  componentDidUpdate(prevProps, prevState) {
    if (!isEqual(prevProps.banners, this.props.banners)) {
      this.initSwiper();
    }
  }
  componentWillUnmount() {
    this.swiper?.destroy(true, true);
  }
  async initSwiper() {
    await swiperLoader.loadSwiper11();
    if (this.swiper) {
      this.swiper?.destroy(true, true);
    }
    const { slidesPerView } = this.props;
    this.swiper = new window.Swiper(this.bannerSwiperRef, {
      loop: this.props.banners.length > 1,
      observer: true,
      observeParents: true,
      spaceBetween: 10,
      slidesPerView: 1,
      // centeredSlides: this.props.banners.length > 2,
      clickable: true,
      autoplay:
        this.props.banners.length > 1
          ? {
              disableOnInteraction: false,
              delay: 5000,
            }
          : false,
      pagination: {
        el: this.bannerPaginationRef,
      },
    });
  }
  render() {
    if (!this.props.banners.length) {
      return null;
    }
    return (
      <div className={cz("page-banner", this.props.className)}>
        <div className="swiper" ref={(c) => (this.bannerSwiperRef = c)}>
          <div className="swiper-wrapper">
            {this.props.banners.map((val, index) => (
              <div key={`ban${index}`} className="swiper-slide">
                <div className="swiper-inner" onClick={this.props.onBannerClick(val)}>
                  <img
                    key={`ids${index}`}
                    src={val.url}
                    alt={val.title}
                    onError={(e) => {
                      e.target.classList.add("hide");
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
          {this.props.banners.length > 1 && (
            <div className="swiper-pagination" ref={(c) => (this.bannerPaginationRef = c)} />
          )}
        </div>
      </div>
    );
  }
}

Banner.propTypes = {
  className: PropTypes.string,
  banners: PropTypes.array.isRequired,
};

Banner.defaultProps = {
  className: "",
  banners: [],
};
export default Banner;
