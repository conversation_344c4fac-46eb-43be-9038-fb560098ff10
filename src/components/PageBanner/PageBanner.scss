:root {
  --swiper-pagination-bullet-horizontal-gap: 4px;
  --swiper-pagination-bottom: 16px;
}

.page-banner {
  position: relative;
  width: 100%;

  .swiper {
    width: 100%;
  }

  .swiper-inner {
    width: 100%;
    overflow: hidden;
  }

  .swiper-pagination {
    bottom: var(--swiper-pagination-bottom) !important;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .swiper-pagination-bullet {
    flex-shrink: 0;
    width: 12px;
    height: 12px;
    background: #fff;
    border-radius: 12px;
    opacity: 1;
  }

  .swiper-pagination-bullet-active {
    background: url(~@/assets/images/common/swiper-pagination-active.svg) no-repeat center/100% 100%;
  }

  img {
    display: block;
    width: 100%;
    object-fit: cover;
  }
}

@keyframes colorProgress {
  0% {
    width: 0;
  }

  100% {
    width: 50px;
  }
}
