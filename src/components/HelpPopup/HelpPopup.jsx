import React from "react";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { wysiwygUtil } from "tcg-mobile-common";

import CommonPopup from "@/ui/CommonPopup/CommonPopup";

import "./HelpPopup.scss";

@inject("languageShell", "common")
@observer
class HelpPopup extends React.Component {
  state = {
    activeKey: 0,
  };
  componentDidMount() {
    this.setState({
      activeKey: this.props.activeIndex,
    });
  }
  componentDidUpdate(prevProps) {
    if (prevProps.activeIndex !== this.props.activeIndex) {
      this.setState({
        activeKey: this.props.activeIndex,
      });
    }
  }
  closePopup = () => {
    this.props.common.showHelpPopup(false);
  };
  navClick = (item, index) => {
    this.setState({
      activeKey: index,
    });
  };
  render() {
    if (!this.props.common.helpList.length) {
      return null;
    }
    return (
      <CommonPopup
        className="popup-normal"
        closeable={false}
        show={this.props.common.helpPopupStatus}
        onClose={this.closePopup}
      >
        <div className="help-popup-wrap">
          <div className="common-popup-header">
            <p>{this.props.languageShell.t("help_center")}</p>
            <div className="normal-close" onClick={this.closePopup}>
              <Icon type={require("!svg-sprite-loader!@/assets/images/common/popup-close.svg")} />
            </div>
          </div>
          <div className="popup-body hide-scrollbar">
            <div className="popup-nav" />
            <div className="popup-content-inner">
              {this.props.common.helpList.map((i, idx) => {
                return (
                  <div
                    className={cz("popup-slide-item", { on: this.state.activeKey === idx })}
                    key={`help_popup_${idx}`}
                  >
                    <div
                      dangerouslySetInnerHTML={{
                        __html: wysiwygUtil.convertContent(i.content),
                      }}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </CommonPopup>
    );
  }
}

HelpPopup.propTypes = {
  activeIndex: PropTypes.number,
};

HelpPopup.defaultProps = {
  activeIndex: 0,
};

export default HelpPopup;
