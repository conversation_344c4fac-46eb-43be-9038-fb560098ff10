import React from "react";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import deploy from "@/config/deploy.config";
import { navConfig } from "@/config/game.config";

import "./GameTitle.scss";

const { gameSelectType } = deploy;

@inject("languageShell", "gameCenter", "tcgCommon")
@observer
class GameTitle extends React.Component {
  get mapping() {
    return this.props.tcgCommon.gameVendor.mapping;
  }
  get displayName() {
    return this.mapping[this.props.gameType]?.[this.props.vassalage]?.displayName || "";
  }
  render() {
    return (
      <div className="game-title">
        <div className="title-content">
          {navConfig[this.props.gameType]?.img && (
            <img className="title-icon " src={navConfig[this.props.gameType]?.img} />
          )}
          <span className="title-text">
            {this.props.title || this.props.languageShell.t(navConfig[this.props.gameType]?.name)}
          </span>
        </div>
      </div>
    );
  }
}

GameTitle.propTypes = {
  moreGame: PropTypes.func,
  gameType: PropTypes.string,
  count: PropTypes.number,
  title: PropTypes.string,
};

export default GameTitle;
