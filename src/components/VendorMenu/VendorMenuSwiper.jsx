import React from "react";
import { with<PERSON>outer } from "react-router";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { isEqual } from "lodash";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { VendorIcon } from "tcg-mobile-common";
import MC from "tcgmodulemc";

import { remToPx } from "@/utils/dom";

import "./VendorMenuSwiper.scss";

@inject("common", "languageShell", "gameCenter")
@withRouter
@observer
class VendorMenu extends React.Component {
  get newVendors() {
    return [{ vassalage: "", displayName: "in_type_all" }, ...this.props.vendors];
  }
  constructor(props) {
    super(props);
    this.swiper = null;
  }
  componentDidMount() {
    if (this.props.vendors.length) {
      this.initSwiper();
    }
    this.props.setRef && this.props.setRef(this);
  }
  componentDidUpdate(prevProps, prevState) {
    if (!isEqual(prevProps.vendors, this.props.vendors)) {
      this.initSwiper();
    }
    if (prevProps.vassalage !== this.props.vassalage) {
      this.moveToCenter();
    }
  }
  componentWillUnmount() {
    this.swiper?.destroy?.();
  }
  initSwiper = () => {
    if (this.swiper) {
      this.swiper.update();
    } else {
      this.swiper = new MC.Swiper(this.menuSwiper, {
        observer: true,
        observeParents: true,
        spaceBetween: remToPx(0.15),
        freeMode: true,
        freeModeMomentumRatio: 0.5,
        slidesPerView: "auto",
        slidesPerGroup: 3,
        navigation: {
          nextEl: this.nextNav,
          prevEl: this.prevNav,
        },
      });
    }
    this.moveToCenter();
  };
  tabChange = (vassalage) => {
    this.props.handleSelect(vassalage);
  };
  moveToCenter = () => {
    const index = this.newVendors.findIndex((item) => item.vassalage === this.props.vassalage);
    if (this.swiper && index > 0) {
      const swiperWidth = this.swiper.width;
      const maxTranslate = this.swiper.maxTranslate();
      const maxWidth = -maxTranslate + swiperWidth / 2;
      const slide = this.swiper?.slides[index];
      let slideLeft = slide?.offsetLeft;
      let slideWidth = slide?.clientWidth;
      let slideCenter = slideLeft + slideWidth / 2;

      this.swiper.setTransition(200);

      if (slideCenter < swiperWidth / 2) {
        this.swiper.setTranslate(0);
      } else if (slideCenter > maxWidth) {
        this.swiper.setTranslate(maxTranslate);
      } else {
        const nowTlanslate = slideCenter - swiperWidth / 2;
        this.swiper.setTranslate(-nowTlanslate);
      }
    }
  };
  render() {
    return (
      <div className={cz("vendor-menu-swiper-wrap")}>
        <div className="vendor-menu-scroll">
          <div className="menu-nav nav-prev" ref={(e) => (this.prevNav = e)}>
            <Icon type={require("!svg-sprite-loader!@/assets/images/common/arrow-left.svg")} />
          </div>
          <div className="menu-nav nav-next" ref={(e) => (this.nextNav = e)}>
            <Icon type={require("!svg-sprite-loader!@/assets/images/common/arrow-right.svg")} />
          </div>
          <div className="vendor-menu-swiper" ref={(c) => (this.menuSwiper = c)}>
            <div className="swiper-wrapper">
              {this.newVendors.map((item, idx) => {
                return (
                  <div
                    key={`game-menu-${idx}`}
                    className={`vendor-menu-item swiper-slide ${cz({
                      on: this.props.vassalage === item.vassalage,
                    })}`}
                    onClick={() => this.tabChange(item.vassalage)}
                  >
                    {item.vassalage === "" ? (
                      <div className="vendor-menu-all">
                        {/* <Icon
                          className="icon-all"
                          type={require("!svg-sprite-loader!@/assets/images/common/icon-all.svg")}
                        /> */}
                        <span className="vendor-name">{this.props.languageShell.t(item.displayName)}</span>
                      </div>
                    ) : (
                      <div className="vendor-menu-inner">
                        <VendorIcon
                          className="menu-icon"
                          visibleByDefault={true}
                          folder="RNG_LIST_VENDOR"
                          vendor={item.vassalage}
                          color="WHITE"
                        />
                        {/* <span className="vendor-name">{item.displayName}</span> */}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

VendorMenu.propTypes = {
  vendors: PropTypes.array.isRequired,
  vassalage: PropTypes.string.isRequired,
  showNav: PropTypes.bool,
  handleSelect: PropTypes.func.isRequired,
};

VendorMenu.defaultProps = {
  vendors: [],
  vassalage: "",
  showNav: true,
};

export default VendorMenu;
