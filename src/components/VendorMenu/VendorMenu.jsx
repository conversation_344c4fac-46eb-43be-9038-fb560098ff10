import React from "react";
import { with<PERSON>out<PERSON> } from "react-router";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import { scrollLeftTo } from "@/utils/scroll";

import "./VendorMenu.scss";

@inject("common", "languageShell", "gameCenter")
@withRouter
@observer
class VendorMenu extends React.Component {
  get newVendors() {
    return [...this.props.vendors];
  }
  constructor(props) {
    super(props);
    this.menuGame = {};
    this.menuRef = null;
  }
  componentDidMount() {}
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.vassalage !== this.props.vassalage) {
      const index = this.newVendors.findIndex((item) => item.vassalage === this.props.vassalage);
      this.scrollLeftCenter(index);
    }
  }
  tabChange = (vassalage, index) => {
    this.props.handleSelect(vassalage);
    this.scrollLeftCenter(index);
  };
  scrollLeftCenter = (index) => {
    const el = this.menuGame[index];
    if (el) {
      const containerWidth = this.menuRef.offsetWidth;
      const itemOffsetLeft = el.offsetLeft;
      const scrollPosition = itemOffsetLeft - containerWidth / 2 + el.offsetWidth / 2;
      scrollLeftTo(this.menuRef, scrollPosition, 300);
    }
  };
  render() {
    return (
      <div className={cz("vendor-menu-wrap")}>
        <div className="vendor-menu-scroll hide-scrollbar" ref={(c) => (this.menuRef = c)}>
          {this.newVendors.map((item, idx) => {
            return (
              <div
                key={`game-menu-${idx}`}
                className={`vendor-menu-item ${cz({
                  on: this.props.vassalage === item.vassalage,
                })}`}
                onClick={() => this.tabChange(item.vassalage, idx)}
                ref={(e) => (this.menuGame[idx] = e)}
              >
                <span className="vendor-name">{item.displayName}</span>
                {/* {item.vassalage === "" ? (
                  <div className="vendor-menu-all">
                    <Icon
                      className="icon-all"
                      type={require("!svg-sprite-loader!@/assets/images/common/icon-all.svg")}
                    />
                    <span className="vendor-name">{this.props.languageShell.t(item.displayName)}</span>
                  </div>
                ) : (
                  <div className="vendor-menu-inner">
                    <img
                      className="menu-icon"
                      src={`${this.props.common.vendorIconCDN}${item.vassalage}-COLOR.png`}
                      alt=""
                    />
                  </div>
                )} */}
              </div>
            );
          })}
        </div>
      </div>
    );
  }
}

VendorMenu.propTypes = {
  vendors: PropTypes.array.isRequired,
  vassalage: PropTypes.string.isRequired,
  showNav: PropTypes.bool,
  handleSelect: PropTypes.func.isRequired,
};

VendorMenu.defaultProps = {
  vendors: [],
  vassalage: "",
  showNav: true,
};

export default VendorMenu;
