.vendor-menu-wrap {
  position: relative;
  width: 100%;
  margin-top: 20px;

  .vendor-menu-scroll {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    overflow: auto;
    touch-action: pan-x;
  }

  .vendor-menu-item {
    @include flex-center;
    position: relative;
    flex-shrink: 0;
    padding: 5.7px 15.385px 8.44px;
    font-size: 27px;
    font-variant: all-small-caps;
    line-height: 30.76px;
    text-align: center;
    white-space: nowrap;
    border-radius: 26.92px;

    &:not(:last-child) {
      margin-right: 10px;
    }

    &.on {
      color: #fff;
      background: #6297ff;
    }

    .menu-icon {
      display: block;
      height: 42px;
      object-fit: contain;
    }

    .icon-active {
      display: none;
    }
  }

  .icon-all {
    width: 40px;
    height: 40px;
  }

  .vendor-name {
    @include trim(1);
  }

  .menu-nav {
    @include flex-center;
    position: absolute;
    top: 0;
    z-index: 10;
    width: 42px;
    height: 100%;
    color: #fff;

    .am-icon {
      display: block;
      width: 42px;
      height: 42px;
    }
  }

  .nav-prev {
    left: 0;
  }

  .nav-next {
    right: 0;
  }
}
