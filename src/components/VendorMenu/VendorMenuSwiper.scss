.vendor-menu-swiper-wrap {
  position: relative;
  width: 100%;

  .vendor-menu-scroll {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 64px;
  }

  .menu-mask {
    position: absolute;
    right: 0;
    z-index: 10;
    width: 30px;
    height: 90px;
    pointer-events: none;
    background: linear-gradient(90deg, rgb(18 18 18 / 0%) 0%, #121212 74.6%);
  }

  .vendor-menu-swiper {
    width: 100%;
    overflow: hidden;
  }

  .vendor-menu-item {
    @include flex-center;
    position: relative;
    width: fit-content;
    font-size: 24px;
    font-weight: 700;
    line-height: normal;
    color: #fff;
    text-align: center;

    &.on {
      .vendor-menu-inner,
      .vendor-menu-all {
        background: var(--bg-color-menu-active);
      }

      .menu-icon {
        opacity: 1;
      }

      .icon-active {
        display: block;
      }

      .icon-normal {
        display: none;
      }
    }

    .menu-icon {
      display: block;
      width: 100px;
      height: 50px;
      object-fit: contain;
    }

    .icon-active {
      display: none;
    }
  }

  .icon-all {
    width: 40px;
    height: 40px;
  }

  .vendor-menu-inner,
  .vendor-menu-all {
    @include flex-center;
    gap: 15px;
    width: 120px;
    height: 64px;
    background: var(--bg-color-menu-default);
    border-radius: 8px;
  }

  .vendor-name {
    @include trim(1);
  }

  .menu-nav {
    @include flex-center;
    position: absolute;
    top: 50%;
    z-index: 10;
    width: 48px;
    height: 48px;
    background: var(--bg-color-menu-default);
    border-radius: 8px;
    transform: translateY(-50%);

    .am-icon {
      display: block;
      width: 32px;
      height: 32px;
      object-fit: contain;
    }
  }

  .nav-prev {
    left: 0;
  }

  .nav-next {
    right: 0;
  }
}
