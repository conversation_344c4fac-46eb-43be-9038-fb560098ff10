import React from "react";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { withReferral } from "tcg-mobile-common";

import "./InviteEarn.scss";

const INVITEEARN = "INVITEEARN";

@inject("languageShell", "common", "tcgCommon")
@withReferral
@observer
class InviteEarn extends React.Component {
  state = { show: false };
  componentDidMount() {
    const modalStatus = window.sessionStorage.getItem(INVITEEARN);
    if (!modalStatus) {
      this.setState({
        show: true,
      });
    }
  }
  closeModal = () => {
    this.setState({
      show: false,
    });
    window.sessionStorage.setItem(INVITEEARN, "1");
  };
  render() {
    if (!this.props.show) {
      return null;
    }
    return (
      <div className="invite-earn-wrap">
        <div className="invite-earn-content">
          <div className="title">
            {this.props.languageShell.t("invite_to_earn")}{" "}
            <span className="earn-amount">
              {<span className="symbol">{this.props.tcgCommon.currencySymbol}</span>}150
            </span>
          </div>
          <span onClick={this.props.openReferral} className="more-btn">
            {this.props.languageShell.t("in_more_button")}
          </span>
          <Icon
            className="btn-close"
            onClick={this.props.close}
            type={require("!svg-sprite-loader!@/assets/images/common/icon-close.svg")}
          />
        </div>
      </div>
    );
  }
}

InviteEarn.propTypes = {
  show: PropTypes.bool.isRequired,
  close: PropTypes.func.isRequired,
};

InviteEarn.defaultProps = {
  show: false,
};

export default InviteEarn;
