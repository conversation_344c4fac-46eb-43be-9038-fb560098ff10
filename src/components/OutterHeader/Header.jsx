import React from "react";
import { with<PERSON><PERSON>er } from "react-router-dom";
import { Icon, NavBar } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import { isApp } from "@/utils/navigatorUtil";

import "./Header.scss";

@inject("mcMenu", "common", "auth", "languageShell", "login")
@withRouter
@observer
class Header extends React.Component {
  constructor(props) {
    super(props);
    this.titleMap = {
      "/m/activity": "promotion_activity",
      "/m/activityDetail": "promotion_activity",
      "/m/forget": "in_forget_password",
      "/m/home": "hd_home",
      "/m/login": "hd_login_button",
      "/m/register": "hd_reg_button",
      "/m/changePsw": "change_password",
      "/m/notice": "in_increase_announcement",
      "/m/noticeDetail": "promo_detail",
      "/m/gameCenter": "in_game_center",
      "/m/set": "personal_setting",
      "/m/help": "help_center",
      "/m/helpDetail": "help_center_details",
      "/m/hot-games": "in_hot_games",
      "/m/download": "download_promo",
      "/m/provider": "game_providers",
      "/m/search": "search",
      "/m/fav-games": "in_my_favorite",
      "/m/new-games": "new_games",
      "/m/jili": "JILI",
      "/m/slot-games": "RNG",
      "/m/live-games": "LIVE",
      "/m/fish-games": "FISH",
      "/m/pvp-games": "PVP",
      "/m/sport-games": "SPORTS",
      "/m/esport-games": "hd_esport",
      "/m/favorite": "in_favorite",
      "/m/jackpots": "recommended",
      "/m/cockfight": "hd_cockfight",
      "/m/lott": "LOTT",
      "/m/elott": "LOTT",
      "/m/applyReward": "activity_claim",
    };
    this.isAPP = isApp;
  }

  renderLeft() {
    if (!this.props.hasReturn) {
      return null;
    }
    return (
      <div className="shell-return-icon" onClick={this.handleClick}>
        <Icon className="icon-return" type={require("!svg-sprite-loader!@/assets/images/common/icon-return.svg")} />
        {this.props.backHome && <span>{this.props.languageShell.t("back_home")}</span>}
      </div>
    );
  }

  handleClick = () => {
    if (this.props.handleGoBack) {
      return this.props.handleGoBack();
    }
    if (this.props.backHome) return this.props.history.push("/m/home");
    if (
      ["/m/login", "/m/register", "/m/activity", "/m/forget", "/m/download", "/m/help", "/m/notice"].includes(
        location.pathname
      )
    ) {
      this.props.history.push("/m/home");
    } else {
      this.props.history.goBack();
    }
  };

  getTitle() {
    if (this.props.title) {
      return this.props.title;
    }
    return this.titleMap[location.pathname] && this.props.languageShell.t(this.titleMap[location.pathname]);
  }

  renderRight = () => {
    if (this.props.rightContent) {
      return this.props.rightContent;
    }
    return null;
  };

  render() {
    return (
      <div className={cz("shell-header", this.props.size)}>
        <NavBar
          className={`mc-navbar-blue this-mc-header`}
          mode="light"
          iconName={false}
          leftContent={this.renderLeft()}
          rightContent={this.renderRight()}
        >
          {this.getTitle()}
        </NavBar>
      </div>
    );
  }
}

Header.propTypes = {
  hasReturn: PropTypes.bool,
  handleGoBack: PropTypes.func,
  rightContent: PropTypes.node,
  title: PropTypes.string,
};
Header.defaultProps = {
  hasReturn: true,
  title: "",
};

export default Header;
