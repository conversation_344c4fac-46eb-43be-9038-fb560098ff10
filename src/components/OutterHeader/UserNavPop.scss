.user-nav-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  height: calc(100vh);
  height: calc(var(--vh, 1vh) * 100);
}

.user-nav-popup-content {
  position: absolute;
  top: 100%;
  left: 14px;
  z-index: 101;
  width: 362px;

  &::before {
    position: absolute;
    top: -11px;
    left: 22px;
    width: 22px;
    height: 22px;
    content: "";
    background: #fff;
    border-top: 2px solid #e3e3e3;
    border-left: 2px solid #e3e3e3;
    transform: rotate(45deg);
  }

  .nav-popup-bg {
    width: 100%;
    max-height: 800px;
    padding: 22px 12px;
    overflow: hidden auto;
    background: #fff;
    border: 2px solid #e3e3e3;
    border-radius: 14px;
  }

  .user-nav-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px 4px;
    width: 100%;
  }

  .user-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 104px;

    .nav-icon {
      display: block;
      width: 57px;
      height: 50px;
      margin-bottom: 6px;
      object-fit: contain;
    }

    .nav-name {
      @include trim(2);
      height: 48px;
      font-size: 20px;
      line-height: normal;
      color: #666;
      text-align: center;
    }
  }
}
