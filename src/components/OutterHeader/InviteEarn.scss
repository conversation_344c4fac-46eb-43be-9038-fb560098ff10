.invite-earn-wrap {
  position: absolute;
  bottom: -92px;
  left: 0;
  z-index: $header-index;
  width: 100%;
  height: 92px;
  background: rgb(100 32 23 / 60%);
  backdrop-filter: blur(4px);

  .invite-earn-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 72px 0 12px;
    font-size: 24px;
    font-weight: 600;
    line-height: normal;
    color: #fff;
    text-align: center;

    .earn-amount {
      color: #ffff8f;
    }

    .btn-close {
      position: absolute;
      top: 50%;
      right: 12px;
      width: 32px;
      height: 32px;
      transform: translateY(-50%);
    }

    .more-btn {
      @include flex-center;
      min-width: 140px;
      height: 60px;
      padding: 0 10px;
      font-size: 24px;
      font-weight: 600;
      line-height: normal;
      color: #77331c;
      text-align: center;
      background: linear-gradient(180deg, #f5ec4d 0%, #f7d00d 100%);
      border-radius: 8px;
    }
  }
}
