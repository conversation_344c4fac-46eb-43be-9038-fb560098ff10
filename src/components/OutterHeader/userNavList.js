export const userNavList = [
  {
    type: "VIP",
    name: "VIP",
    path: "/m/member/benefits",
    icon: require("@/assets/images/menu/vip.png"),
  },
  {
    type: "CS",
    name: "in_customer_services",
    icon: require("@/assets/images/menu/service.png"),
  },
  {
    key: "WITHDRAW",
    path: "/m/withdraw",
    name: "hd_withdraw_button",
    icon: require("@/assets/images/menu/withdraw.png"),
  },
  {
    path: "/m/activity",
    name: "promotion",
    icon: require("@/assets/images/menu/promotion.png"),
  },
  {
    key: "MANPLAYREB3",
    name: "rebate",
    path: "/m/member/manualRebate",
    icon: require("@/assets/images/menu/rebate.png"),
  },
  {
    key: "FEEDBACK3",
    name: "feedback_suggest",
    path: "/m/feedback",
    icon: require("@/assets/images/menu/feedback.png"),
  },
  {
    key: "SECPRIV",
    name: "security",
    path: "/m/securityCenter",
    icon: require("@/assets/images/menu/security.png"),
  },
  {
    type: "APP",
    name: "app_download_app",
    icon: require("@/assets/images/menu/app.png"),
  },
  {
    key: "MYACC",
    name: "personal_information",
    path: "/m/myAccount/index",
    icon: require("@/assets/images/menu/personal.png"),
  },
  {
    key: "TRANSREC3",
    name: "in_account_record",
    path: "/m/transaction/record",
    icon: require("@/assets/images/menu/account-record.png"),
  },
  {
    key: "TRANSBETREPMEM3",
    name: "in_betting_record",
    path: "/m/gameRecord",
    icon: require("@/assets/images/menu/betting-record.png"),
  },
  {
    key: "PERSREP",
    name: "propfit_and_loss",
    path: "/m/profitandloss",
    icon: require("@/assets/images/menu/profit-loss.png"),
  },
  {
    key: "REWCEN3",
    name: "in_reward_center",
    path: "/m/rewardCenter",
    icon: require("@/assets/images/menu/reward.png"),
  },
  {
    type: "BANK",
    name: "bank_card",
    path: "/m/myBankCards",
    icon: require("@/assets/images/menu/bank.png"),
  },
  {
    type: "MISSION",
    name: "mission_activity",
    path: "/m/leaderBoard",
    icon: require("@/assets/images/menu/mission.png"),
  },
  {
    res: "TELEGRAM",
    name: "Telegram",
    icon: require("@/assets/images/menu/telegram.png"),
  },
  {
    res: "FACEBOOK",
    name: "Facebook ",
    icon: require("@/assets/images/menu/facebook.png"),
  },
  {
    res: "YOUTUBE",
    name: "Youtube",
    icon: require("@/assets/images/menu/youtube.png"),
  },
];
