import React from "react";
import { withRouter } from "react-router-dom";
import { CSSTransition } from "react-transition-group";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { navigatorUtil, withCs } from "tcg-mobile-common";

import withMemberMenu from "@/hoc/withMemberMenu";
import Fragment from "@/ui/Fragment/Fragment";

import { userNavList } from "./userNavList";

import "./UserNavPop.scss";

const { isApp, isPWA } = navigatorUtil;

const showDownload = !isApp && !isPWA;

@inject("languageShell")
@withRouter
@withCs
@withMemberMenu
@observer
class UserNav extends React.Component {
  render() {
    const { menuHelper } = this.props;
    return (
      <Fragment>
        {this.props.show && <div className="user-nav-popup-mask" onClick={this.props.handleClose} />}
        <CSSTransition in={this.props.show} timeout={300} classNames="menu-fade-slide-down" mountOnEnter>
          <div className="user-nav-popup-content">
            <div className="nav-popup-bg">
              <div className="user-nav-list">
                {userNavList.map((item, index) => {
                  return (
                    menuHelper.displayMenu(item) && (
                      <div
                        key={`member-nav-${index}`}
                        className={"user-nav-item"}
                        onClick={() => menuHelper.menuClick(item)}
                      >
                        <img className="nav-icon" src={item.icon} />
                        <div className="nav-name">
                          <span>{this.props.languageShell.t(item.name)}</span>
                        </div>
                      </div>
                    )
                  );
                })}
              </div>
            </div>
          </div>
        </CSSTransition>
      </Fragment>
    );
  }
}

UserNav.propTypes = {
  show: PropTypes.bool,
  handleClose: PropTypes.func,
};

UserNav.defaultProps = {
  show: false,
  handleClose: () => {},
};

export default UserNav;
