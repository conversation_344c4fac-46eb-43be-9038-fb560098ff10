#root {
  // 會員中心header字色
  .shell-header,
  .mc-header-wrap {
    height: var(--mc-header-height);

    .mc-navbar-blue {
      height: var(--mc-header-height);
      background: var(--mc-header-bg);

      .am-navbar-title {
        font-size: 36px;
        font-weight: 700;
        color: var(--mc-header-color);
        white-space: nowrap;
      }

      .am-icon-left {
        display: block;
        width: 40px;
        height: 40px;
        font-size: 32px;
        color: var(--mc-header-color);
      }

      .am-navbar-right {
        color: var(--mc-header-color);
      }

      .shell-return-icon {
        @include flex-center;

        .icon-return {
          width: 40px;
          height: 40px;
        }
      }
    }
  }

  .shell-header {
    .am-navbar-left {
      padding-left: 0;
    }

    .am-navbar-left-content {
      padding-left: 30px;
    }

    .am-navbar-right {
      margin-right: 0 !important;
    }

    .header-right {
      padding-right: 30px;

      .btn-search {
        display: block;
        width: 40px;
        height: 40px;
      }
    }
  }
}
