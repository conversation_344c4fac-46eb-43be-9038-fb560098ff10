.home-header-wrap {
  position: relative;
  flex-shrink: 0;
  width: 100%;
  height: var(--header-height);
  color: var(--text-color-primary);
  transition: all 0.3s;

  .header-logo {
    display: flex;
    align-items: center;
    height: 100%;

    img {
      display: block;
      height: 100%;
    }
  }

  .header-content {
    position: fixed;
    top: 0;
    left: 0;
    z-index: $header-index;
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: var(--header-height);
    padding: 0 32px;
    background: var(--header-bg);
    transition: top 0.3s;

    .header-center {
      @include flex-center;
      position: absolute;
      top: 0;
      left: 50%;
      z-index: 1;
      height: 100%;
      transform: translateX(-50%);
    }

    .header-right {
      position: relative;
      display: flex;
      align-items: center;
      height: 100%;
    }

    .header-left {
      position: relative;
      display: flex;
      align-items: center;
    }

    .header-menu {
      .icon-menu {
        display: block;
        width: 31px;
        height: 26px;
        object-fit: contain;
      }
    }

    .user-icon {
      svg {
        display: block;
        width: 60px;
        height: 60px;
        fill: #aabee2;
      }
    }

    .header-nav-btn {
      display: flex;
      align-items: center;
      height: 100%;
    }

    .login-btn {
      @include btn-login;
    }

    .register-btn {
      @include btn-register;
    }

    .language-select {
      margin-left: 16px;
    }
  }

  .logged-enter {
    display: flex;
    align-items: center;

    .vip-level {
      display: flex;
      align-items: center;
      width: 113px;
      height: 34px;
      background: url(~@/assets/images/home/<USER>/100% 100%;
    }

    .icon-vip {
      flex-shrink: 0;
      width: 45.115px;
      height: 42.906px;
    }

    .vip-name {
      flex: 1;
      font-size: 18px;
      font-style: italic;
      font-weight: 900;
      line-height: normal;
      color: #fff;
      text-align: center;
      text-transform: uppercase;
    }
  }

  .header-username {
    display: flex;
    gap: 10px;
    align-items: center;
    font-size: 27px;
    font-weight: 500;
    line-height: normal;

    .icon-username {
      display: block;
      width: 20px;
      height: 24px;
    }

    .username {
      @include ellipsis;
      max-width: 200px;
    }
  }

  .header-balance {
    display: flex;
    gap: 10px;
    align-items: center;

    .sum-balance {
      font-size: 27px;
      font-weight: 700;
      line-height: normal;
      color: #ff6200;

      .amount {
        max-width: 200px;
      }
    }

    .deposit-btn,
    .withdraw-btn {
      .am-icon {
        display: block;
        width: 40px;
        height: 40px;
        fill: none;
      }
    }

    .header-avatar {
      width: 50px;
      height: 50px;
      border: 2.5px solid #e4eaff;
      border-radius: 125px;
      box-shadow: 0 5px 5px 0 rgb(0 0 0 / 25%);
    }
  }

  .shell-refresh-balance {
    display: none;
  }
}
