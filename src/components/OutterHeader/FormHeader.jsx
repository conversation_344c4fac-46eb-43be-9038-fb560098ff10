import React from "react";
import { withRouter } from "react-router-dom";
import { Icon } from "antd-mobile";
import { CommonLogo } from "tcg-mobile-common";

import "./FormHeader.scss";

@withRouter
class FormHeader extends React.Component {
  render() {
    return (
      <div className="form-header">
        <div className="form-header-content">
          <CommonLogo
            className="form-logo"
            logoClick={() => this.props.history.push("/m/home")}
            src={require("@/assets/images/logo/logo.png")}
          />
          <div className="nav-home" onClick={() => this.props.history.push("/m/home")}>
            <Icon className="icon-home" type={require("!svg-sprite-loader!@/assets/images/common/nav-home.svg")} />
          </div>
        </div>
      </div>
    );
  }
}

export default FormHeader;
