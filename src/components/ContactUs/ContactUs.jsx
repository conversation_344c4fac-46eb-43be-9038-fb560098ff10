import React from "react";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import { handleWindowPage } from "../../utils/openLink";

import "./ContactUs.scss";

const medialist = [
  { name: "Facebook", type: "FACEBOOK", icon: require("./images/facebook.png") },
  { name: "instagram", type: "INSTAGRAM", icon: require("./images/instagram.png") },
  { name: "Twitter", type: "TWITTER", icon: require("./images/twitter.png") },
  { name: "Pinterest", type: "PINTEREST", icon: require("./images/pinterest.png") },
  { name: "Youtube", type: "YOUTUBE", icon: require("./images/youtube.png") },
  { name: "Telegram", type: "TELEGRAM", icon: require("./images/telegram.png") },

  // { name: "Line", type: "LINE", icon: require("@/assets/images/footer/line.png") },
  // { name: "tiktok", type: "TIKTOK", icon: require("./images/tiktok.png") },
  // { name: "kwai", type: "KWAI", icon: require("./images/kwai.png") },
  // { name: "messenger", type: "MESSENGER", icon: require("./images/messenger.png") },
  // { name: "discord", type: "DISCORD", icon: require("./images/discord.png") },
  // { name: "viber", type: "VIBER", icon: require("./images/viber.png") },
  // { name: "whatsapp", type: "WHATSAPP", icon: require("@/assets/images/footer/whatsapp.png") },
];

@inject("common", "languageShell")
@observer
class ContactUs extends React.Component {
  get resources() {
    return this.props.common.downLinkObj;
  }
  get activeMedia() {
    return medialist.filter((item) => !!this.resources[item.type]);
  }
  handleClick = (type) => {
    const link = this.props.common.downLinkObj[type];
    if (link) {
      handleWindowPage(link.url);
    }
  };
  render() {
    if (this.activeMedia?.length < 1) {
      return null;
    }
    return (
      <div className={cz("contact-wrap", this.props.className)}>
        {/* <div className="footer-title">{this.props.languageShell.t("follow_us")}</div> */}
        <div className="contact-list">
          {this.activeMedia.map((item, index) => {
            return (
              <div
                className={`contact-item ${item.type.toLowerCase()}`}
                key={`contact_${index}`}
                onClick={() => this.handleClick(item.type)}
              >
                <img className="contact-icon" src={item.icon} alt={item.name} />
              </div>
            );
          })}
        </div>
      </div>
    );
  }
}

ContactUs.propTypes = {
  className: PropTypes.string,
  hasTitle: PropTypes.bool,
};

ContactUs.defaultProps = {
  className: "",
  hasTitle: true,
};

export default ContactUs;
