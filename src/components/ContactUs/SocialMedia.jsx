import React from "react";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import { handleWindowPage } from "../../utils/openLink";

import "./SocialMedia.scss";

const medialist = [
  { name: "facebook", type: "FACEBOOK", icon: require("./img/icon-facebook.png") },
  { name: "telegram", type: "TELEGRAM", icon: require("./img/icon-telegram.png") },
  { name: "viber", type: "VIBER", icon: require("./img/icon-viber.png") },
];

@inject("common", "languageShell")
@observer
class SocialMedia extends React.Component {
  get resources() {
    return this.props.common.downLinkObj;
  }
  get activeMedia() {
    return medialist.filter((item) => !!this.resources[item.type]);
  }
  handleClick = (type) => {
    const link = this.props.common.downLinkObj[type];
    if (link) {
      handleWindowPage(link.url);
    }
  };
  render() {
    if (this.activeMedia?.length < 1) {
      return null;
    }
    return (
      <div className={cz("social-media-wrap", this.props.className)}>
        <div className="media-title">{this.props.languageShell.t("social_media")}:</div>
        <div className="media-list">
          {this.activeMedia.map((item, index) => {
            return (
              <div
                className={`media-item ${item.name}`}
                key={`contact_${index}`}
                onClick={() => this.handleClick(item.type)}
              >
                <img className="media-icon" src={item.icon} alt={item.name} />
                <span>{this.resources[item.type]?.labelNameEn}</span>
              </div>
            );
          })}
        </div>
      </div>
    );
  }
}

SocialMedia.propTypes = {
  className: PropTypes.string,
};

SocialMedia.defaultProps = {
  className: "",
};

export default SocialMedia;
