export const memberNav = [
  {
    key: "<PERSON>Y<PERSON><PERSON>",
    name: "in_my_account",
    path: "/m/myAccount/index",
    icon: require("!svg-sprite-loader!@/assets/images/home/<USER>"),
  },
  {
    key: "TRANSBETREPMEM3",
    name: "in_betting_record",
    path: "/m/gameRecord",
    icon: require("!svg-sprite-loader!@/assets/images/home/<USER>"),
  },
  {
    key: "TRANSREC3",
    name: "in_account_record",
    path: "/m/transaction/record",
    icon: require("!svg-sprite-loader!@/assets/images/home/<USER>"),
  },
  {
    key: "PERSREP",
    name: "propfit_and_loss",
    path: "/m/profitandloss",
    icon: require("!svg-sprite-loader!@/assets/images/home/<USER>"),
  },

  {
    key: "MAILCEN",
    name: "news",
    path: "/m/webEmail",
    icon: require("!svg-sprite-loader!@/assets/images/home/<USER>"),
  },
  // {
  //   key: "DEPOSIT",
  //   name: "in_increase_deposit",
  //   path: "/m/voucherCenter",
  //   icon: require("!svg-sprite-loader!@/assets/images/common/deposit.svg"),
  // },
  // {
  //   type: "VIP",
  //   name: "level_interests",
  //   path: "/m/member/benefits",
  //   icon: require("!svg-sprite-loader!@/assets/images/common/vip-level.svg"),
  // },
  // {
  //   key: "PTSYS3",
  //   name: "points_mall",
  //   path: "/m/mall/mallHome",
  //   icon: require("!svg-sprite-loader!@/assets/images/common/point-mall.svg"),
  // },
  // {
  //   key: "SIGNIN",
  //   name: "in_sign_in",
  //   path: "/m/activity/signIn",
  //   icon: require("!svg-sprite-loader!@/assets/images/common/sign-in.svg"),
  // },

  // {
  //   type: "INVITE",
  //   name: "invite_friends",
  //   path: "/m/inviteFriends",
  //   icon: require("@/assets/images/common/invite-friend.png"),
  // },

  // {
  //   type: "FAV",
  //   name: "my_games",
  //   path: "/m/fav-games",
  //   icon: require("@/assets/images/common/my-game.png"),
  // },
  // {
  //   type: "MEMBER",
  //   name: "profile",
  //   path: "/m/member/home",
  //   icon: require("@/assets/images/common/personal-center.png"),
  // },

  // {
  //   key: "REWCEN3",
  //   name: "in_reward_center",
  //   path: "/m/rewardCenter",
  //   icon: require("!svg-sprite-loader!@/assets/images/common/reward-center.svg"),
  // },

  // {
  //   key: "SECPRIV",
  //   name: "security_center",
  //   path: "/m/securityCenter",
  //   icon: require("!svg-sprite-loader!@/assets/images/common/security-center.svg"),
  // },

  // {
  //   key: "WITHDRAW",
  //   name: "in_increase_withdrawal",
  //   path: "/m/withdraw",
  //   icon: require("!svg-sprite-loader!@/assets/images/common/withdraw.svg"),
  // },
];
