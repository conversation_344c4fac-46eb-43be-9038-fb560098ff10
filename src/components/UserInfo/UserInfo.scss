.user-info-wrap {
  position: relative;

  .user-info {
    display: flex;
    gap: 15px;
    align-items: center;
  }

  .header-balance {
    @include flex-center;
    height: 50px;
    padding: 0 24px;
    background: linear-gradient(0deg, #2d2d2d 0%, #2d2d2d 100%), linear-gradient(90deg, #fee93c 0%, #faa83c 100%);
    background-clip: padding-box, border-box;
    border: 1px solid transparent;
    border-radius: 28px;
    clip-path: inset(0 round 28px);
  }

  .header-avatar {
    position: relative;
    display: flex;
    flex-shrink: 0;

    &.on {
      .arrow-down {
        transform: rotate(-180deg);
      }
    }

    .arrow-down {
      width: 26px;
      height: 26px;
      margin: 24px 0 0 8px;
      fill: none;
    }
  }

  .default-avatar {
    position: relative;
    display: block;
    width: 60px;
    height: 60px;
  }

  .user-balance {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 69px;
    padding: 0 20px;
    background: #0e131b;
    border-radius: 10px;
  }

  .user-name-box {
    position: relative;
    font-size: 28px;
    font-weight: 700;
    color: #fff;
  }

  .user-popup-avatar {
    position: relative;
    flex-shrink: 0;
    width: 100px;
    height: 100px;
    margin-right: 15px;

    .vip-level {
      right: 12px;
      bottom: -5px;
      width: 63px;
      height: 22px;
      padding-left: 20px;
      font-size: 14px;
    }

    .icon-vip {
      top: -2px;
      left: -12px;
      width: 30px;
      height: 27px;
      object-fit: contain;
    }
  }

  .user-balance-layout {
    display: flex;
    align-items: center;
    height: 100%;
  }

  .member-icon {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 132px;
    height: 94px;
    padding: 10px;
    margin-left: 10px;
    background: #282f3c;
    border-radius: 60px;

    .member-wrap {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 74px;
      height: 74px;

      .wrap-text {
        position: absolute;
        bottom: 0;
        left: 50%;
        min-width: 47px;
        max-width: 100%;
        height: 24px;
        padding: 0 6px;
        font-size: 20px;

        color: #181f2b;
        background: linear-gradient(135deg, #fdeb71 0%, #f8d800 100%);
        border-radius: 50px;
        transform: translateX(-50%);

        @include ellipsis;
      }
    }

    .accordion-arrow {
      width: 30px;
      height: 30px;
    }

    svg {
      display: block;
      width: 29px;
      height: 34px;
    }
  }
}

.user-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  height: calc(100vh);
  height: calc(var(--vh, 1vh) * 100);
}

.user-popup-content {
  position: absolute;
  top: calc(100% + 20px);
  right: 0;
  z-index: 101;
  width: 350px;
  max-height: 800px;
  padding: 28px 15px;
  overflow: hidden auto;
  background: rgb(13 13 13 / 95%);
  border: 1px solid #505050;
  border-radius: 20px;

  .user-nav-list {
    display: grid;
    gap: 10px;
  }

  .user-nav-item {
    display: flex;
    gap: 10px;
    align-items: center;
    height: 50px;
    padding: 0 10px;
    font-size: 24px;
    font-weight: 500;
    line-height: normal;
    color: #fff;
    text-align: center;
    background: linear-gradient(0deg, #2d2d2d 0%, #2d2d2d 100%), linear-gradient(90deg, #fee93c 0%, #faa83c 100%);
    border: 1px solid #505050;
    border-radius: 10px;

    .nav-icon {
      display: block;
      flex-shrink: 0;
      width: 30px;
      height: 30px;
      object-fit: contain;
      fill: none;
    }
  }

  .v-line {
    width: 100%;
    height: 1px;
    margin: 18px 0;
    background: #505050;
  }

  .btn-logout {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    color: #fff;
    text-align: center;

    span {
      @include flex-center;
      width: 220px;
      height: 40px;
      background: radial-gradient(
          226.27% 83.48% at 54.76% -93.51%,
          #a8a8a8 17%,
          #878787 28%,
          #575757 45%,
          #313131 61%,
          #161616 76%,
          #060606 89%,
          #000 100%
        ),
        #ac0e07;
      border: 0.4px solid #ac0e07;
      border-radius: 8px;
      background-blend-mode: color-dodge, normal;
    }
  }
}

.pupup-user-info {
  display: flex;
  align-items: center;

  .user-name {
    margin-bottom: 5px;
    font-size: 24px;
    font-weight: 500;
    line-height: normal;
    color: #fff;
    text-shadow: 0 1.389px 5.556px rgb(0 0 0 / 25%);
  }

  .user-id {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    color: rgb(255 255 255 / 50%);
    text-shadow: 0 1.389px 5.556px rgb(0 0 0 / 25%);

    .id-copy {
      width: 24px;
      height: 24px;
      margin-left: 20px;
      fill: none;
    }
  }
}
