import React from "react";
import { CopyToClipboard } from "react-copy-to-clipboard";
import { withRouter } from "react-router";
import { CSSTransition } from "react-transition-group";
import { Icon, Toast } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";

import withAuth from "@/hoc/withAuth";
import withMemberMenu from "@/hoc/withMemberMenu";

import UserAvatar from "../UserAvatar/UserAvatar";
import UserBalance from "../UserBalance/UserBalance";

import { memberNav } from "./navList";
import VipRankLevel from "./VipRankLevel";

import "./UserInfo.scss";

@inject("languageShell", "common", "tcgCommon", "auth", "mcMenu")
@withRouter
@withAuth
@withMemberMenu
@observer
class UserInfo extends React.Component {
  get vipLabelName() {
    return this.props.tcgCommon.vipLabelName;
  }
  get whitelabelVal() {
    return this.props.mcMenu.whitelabelVal;
  }
  get vipPercent() {
    return `${Math.floor(this.props.tcgCommon.vipProgress * 100)}%`;
  }
  get vipRankLevel() {
    return this.props.tcgCommon.vipRankLevel;
  }
  get isAgent() {
    return this.props.auth.isAgent;
  }

  state = { show: false };
  componentDidMount() {
    this.listenRouterChange();
  }
  getUsername() {
    const { memberInfo } = this.props.common;
    if (!memberInfo) {
      return "";
    }
    const name = memberInfo.nickname ? memberInfo.nickname : memberInfo.account;
    return name;
  }
  handleLogout = (e) => {
    this.hidePopup();
    this.props.logout();
  };
  listenRouterChange() {
    this.props.history.listen((location, action) => {
      this.hidePopup();
    });
  }
  hidePopup = () => {
    this.setState({
      show: false,
    });
  };
  togglePopup = () => {
    this.setState({
      show: !this.state.show,
    });
  };
  handleCopy = (_, result) => {
    if (result) {
      Toast.info(this.props.languageShell.t("copy_success"), 2);
    }
  };
  render() {
    const { menuHelper } = this.props;
    return (
      <div className="user-info-wrap">
        <div className="user-info">
          <div className="header-balance">
            {/* <div className="user-name">{this.getUsername()}</div> */}
            <UserBalance />
          </div>

          <div className={cz("header-avatar", { on: this.state.show })} onClick={this.togglePopup}>
            <div className="default-avatar">
              <UserAvatar />
              <VipRankLevel />
            </div>
            <Icon
              className="arrow-down"
              type={require("!svg-sprite-loader!@/assets/images/common/circle-arrow-down.svg")}
            />
          </div>
        </div>
        {this.state.show && <div className="user-popup-mask" onClick={this.togglePopup} />}

        <CSSTransition in={this.state.show} timeout={300} classNames="menu-fade-slide-down" unmountOnExit={true}>
          <div className="user-popup-content">
            <div className="pupup-user-info">
              <div className="user-popup-avatar">
                <UserAvatar />
                <VipRankLevel />
              </div>
              <div className="user-name-box">
                <div className="user-name">{this.getUsername()}</div>
                <div className="user-id">
                  {`ID:${this.props.common.memberInfo?.id}`}
                  <CopyToClipboard text={this.props.common.memberInfo?.id} onCopy={this.handleCopy}>
                    <Icon
                      className="id-copy"
                      type={require("!svg-sprite-loader!@/assets/images/common/icon-copy.svg")}
                    />
                  </CopyToClipboard>
                </div>
              </div>
            </div>
            <div className="v-line" />
            <div className="user-nav-list">
              {memberNav.map((item, index) => {
                return (
                  menuHelper.displayMenu(item) && (
                    <div
                      key={`member-nav-${index}`}
                      className={"user-nav-item"}
                      onClick={() => menuHelper.menuClick(item)}
                    >
                      <Icon className="nav-icon" type={item.icon} />
                      <div className="nav-name">
                        <span>{this.props.languageShell.t(item.name)}</span>
                      </div>
                    </div>
                  )
                );
              })}
            </div>
            <div className="btn-logout">
              <span onClick={this.handleLogout}>{this.props.languageShell.t("in_sign_out")}</span>
            </div>
          </div>
        </CSSTransition>
      </div>
    );
  }
}

export default UserInfo;
