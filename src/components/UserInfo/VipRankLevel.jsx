import React from "react";
import { inject, observer } from "mobx-react";

import "./VipRankLevel.scss";

@inject("languageShell", "common", "tcgCommon")
@observer
class VipRankLevel extends React.Component {
  get vipRankLevel() {
    return this.props.tcgCommon.vipRankLevel;
  }
  render() {
    return (
      <div className="vip-level">
        <img className="icon-vip" src={require("@/assets/images/home/<USER>")} alt="" />
        LVL
        <span>{this.vipRankLevel}</span>
      </div>
    );
  }
}

export default VipRankLevel;
