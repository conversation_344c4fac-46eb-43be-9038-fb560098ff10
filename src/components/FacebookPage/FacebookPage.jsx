import React from "react";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import Qs from "qs";

import { isoLangMap } from "@/utils/isoLang";

import GameTitle from "../GameTitle/GameTitle";

import "./FacebookPage.scss";

const facebookPlugins = "https://www.facebook.com/plugins/page.php";

const FacebookPage = (props) => {
  const pageConfig = {
    width: 375,
    href: props.src,
    tabs: "timeline",
    small_header: true,
    adapt_container_width: true,
    hide_cover: false,
    show_facepile: true,
    locale: isoLangMap[props.languageShell.currentLanguage]?.langRegionCode,
  };

  const videoSrc = `${facebookPlugins}?${Qs.stringify(pageConfig)}`;

  if (!props.src) {
    return null;
  }

  return (
    <div className={cz("fb-page-wrap", props.className)}>
      <div className="home-list-title">
        <GameTitle gameType="FACEBOOK" />
      </div>

      <div className="fb-page">
        <iframe
          className="js-iframe facebook-page"
          title="facebook page"
          src={videoSrc}
          scrolling="no"
          frameborder="0"
          allowfullscreen="true"
          allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
        />
      </div>
    </div>
  );
};

FacebookPage.propTypes = {
  src: PropTypes.string.isRequired,
  className: PropTypes.string,
};

FacebookPage.defaultProps = {
  className: "",
};

export default inject("languageShell")(observer(FacebookPage));
