import React from "react";
import { with<PERSON>outer } from "react-router";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import { navConfig } from "@/config/game.config";
import { scrollLeftTo, scrollTopTo } from "@/utils/scroll";

import "./GameMenu.scss";

@inject("common", "languageShell", "gameCenter", "tcgCommon")
@withRouter
@observer
class GameMenu extends React.Component {
  get mapping() {
    return this.props.tcgCommon.gameVendor.mapping;
  }
  menuGame = {};
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.gameType !== this.props.gameType) {
      const index = this.props.menus.findIndex((item) => item.gameCategory === this.props.gameType);
      this.moveToCenter(index);
    }
    // if (!isEqual(prevProps.menus !== this.props.menus)) {
    //   this.moveToCenter(this.props.gameType);
    // }
    if (prevProps.activeIndex !== this.props.activeIndex) {
      this.moveToCenter(this.props.activeIndex);
    }
  }
  tabChange = (index) => {
    // const path = gamePath[gameCategory];
    // if (path) {
    //   this.props.history.push(path);
    // }
    this.props.menuClick(index);
  };
  changeGameType = (gameCategory) => {
    this.props.menuClick(gameCategory);
  };
  moveToCenter = (index) => {
    switch (this.props.direction) {
      case "horizontal":
        this.scrollLeftCenter(index);
        break;
      case "vertical":
        this.scrollTopCenter(index);
        break;
      default:
        break;
    }
  };
  scrollTopCenter = (index) => {
    const el = this.menuGame[index];
    if (el) {
      const containerHeight = this.menuRef.offsetHeight;
      const itemOffsetLeft = el.offsetTop;
      const scrollPosition = itemOffsetLeft - containerHeight / 2 + el.offsetHeight / 2;
      scrollTopTo(this.menuRef, scrollPosition, 300);
    }
  };
  scrollLeftCenter = (index) => {
    const el = this.menuGame[index];
    if (el) {
      const containerWidth = this.menuRef.offsetWidth;
      const itemOffsetLeft = el.offsetLeft;
      const scrollPosition = itemOffsetLeft - containerWidth / 2 + el.offsetWidth / 2;
      scrollLeftTo(this.menuRef, scrollPosition, 300);
    }
  };
  render() {
    return (
      <div className={`game-menu-wrap menu-${this.props.direction}`}>
        <div className="game-menu-scroll hide-scrollbar" ref={(c) => (this.menuRef = c)}>
          {this.props.menus.map((item, idx) => {
            return (
              <div
                key={`game-menu-${idx}`}
                className={cz(`game-menu-item`, {
                  on: this.props.gameType === item.gameCategory,
                })}
                onClick={() => this.changeGameType(item.gameCategory)}
                ref={(e) => (this.menuGame[idx] = e)}
              >
                <div className="game-menu-icon">
                  <img src={navConfig[item.gameCategory]?.img} />
                </div>

                <div className="game-menu-name">
                  <span>{this.props.languageShell.t(navConfig[item.gameCategory]?.name)}</span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }
}

GameMenu.propTypes = {
  menus: PropTypes.any.isRequired,
  gameType: PropTypes.string.isRequired,
  menuClick: PropTypes.func,
  gameCount: PropTypes.number,
  activeIndex: PropTypes.number,
  direction: PropTypes.oneOf(["vertical", "horizontal"]),
};

GameMenu.defaultProps = {
  menus: [],
  gameType: "",
  menuClick: () => {},
  gameCount: 0,
  activeIndex: 0,
  direction: "horizontal",
};

export default GameMenu;
