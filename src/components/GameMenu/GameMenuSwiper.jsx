import React from "react";
import { withRouter } from "react-router";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { isEqual } from "lodash";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { AutoIcon } from "tcg-mobile-common";
import <PERSON> from "tcgmodulemc";

import { navConfig } from "@/config/game.config";
import { remToPx } from "@/utils/dom";

import "./GameMenuSwiper.scss";

@inject("common", "languageShell", "gameCenter")
@withRouter
@observer
class GameMenu extends React.Component {
  constructor(props) {
    super(props);
    this.swiper = null;
  }
  componentDidMount() {
    if (this.props.menus.length) {
      this.initSwiper();
    }
    this.props.setRef && this.props.setRef(this);
  }
  componentDidUpdate(prevProps, prevState) {
    if (!isEqual(prevProps.menus, this.props.menus)) {
      this.initSwiper();
    }
    if (prevProps.gameType !== this.props.gameType) {
      const index = this.props.menus.findIndex((item) => item.gameCategory === this.props.gameType);
      if (index > 0) {
        this.moveToCenter(index);
      }
    }
  }
  componentWillUnmount() {
    this.swiper?.destroy?.();
  }
  initSwiper = () => {
    if (this.swiper) {
      this.swiper?.destroy(true, true);
    }
    this.swiper = new MC.Swiper(this.menuSwiper, {
      observer: true,
      observeParents: true,
      freeMode: true,
      freeModeMomentumRatio: 0.5,
      slidesPerView: "auto",
      slidesPerGroup: 4,
      spaceBetween: remToPx(0.04),
      navigation: {
        nextEl: this.nextNav,
        prevEl: this.prevNav,
      },
      scrollbar: {
        el: this.menuScrollBar,
      },
    });
  };
  tabChange = async (gameCategory, index) => {
    // const path = gamePath[gameCategory];
    // if (path) {
    //   this.props.history.push(path);
    // }
    this.props.menuClick(gameCategory);
    // this.moveToCenter(index);
  };
  moveToCenter = (index) => {
    if (this.swiper) {
      const swiperWidth = this.swiper.width;
      const maxTranslate = this.swiper.maxTranslate();
      const maxWidth = -maxTranslate + swiperWidth / 2;
      const slide = this.swiper.slides[index];
      let slideLeft = slide.offsetLeft;
      let slideWidth = slide.clientWidth;
      let slideCenter = slideLeft + slideWidth / 2;

      this.swiper.setTransition(300);

      if (slideCenter < swiperWidth / 2) {
        this.swiper.setTranslate(0);
      } else if (slideCenter > maxWidth) {
        this.swiper.setTranslate(maxTranslate);
      } else {
        const nowTlanslate = slideCenter - swiperWidth / 2;
        this.swiper.setTranslate(-nowTlanslate);
      }
    }
  };
  render() {
    return (
      <div className={cz("game-menu-wrap")}>
        <div className="game-menu-content">
          <div className="menu-nav nav-prev" ref={(e) => (this.prevNav = e)}>
            <Icon type={require("!svg-sprite-loader!@/assets/images/common/arrow-left.svg")} />
          </div>
          <div className="menu-nav nav-next" ref={(e) => (this.nextNav = e)}>
            <Icon type={require("!svg-sprite-loader!@/assets/images/common/arrow-right.svg")} />
          </div>
          <div className="swiper-container  game-menu-swiper" ref={(c) => (this.menuSwiper = c)}>
            <div className="game-menu-list swiper-wrapper">
              {this.props.menus.map(({ gameCategory }, idx) => {
                return (
                  <div
                    key={`game-menu-${idx}`}
                    className={cz(`game-menu-item swiper-slide`, { on: this.props.gameType === gameCategory })}
                    onClick={() => this.tabChange(gameCategory, idx)}
                  >
                    <div className={cz("menu-item-inner", { on: this.props.gameType === gameCategory })}>
                      <div className="game-menu-icon">
                        <AutoIcon icon={navConfig[gameCategory]?.img} />
                      </div>
                      <div className="game-menu-name">
                        <span>{this.props.languageShell.t(navConfig[gameCategory]?.name)}</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          {/* <div className="game-menu-scrollbar swiper-scrollbar" ref={(c) => (this.menuScrollBar = c)} /> */}
        </div>
      </div>
    );
  }
}

GameMenu.propTypes = {
  menus: PropTypes.any.isRequired,
  gameType: PropTypes.string.isRequired,
  menuClick: PropTypes.func,
};

GameMenu.defaultProps = {
  menus: [],
  gameType: "",
  menuClick: () => {},
};

export default GameMenu;
