.game-menu-wrap {
  width: 100%;
  padding: 16px 32px;
}

.game-menu-content {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0 60px;

  .menu-nav {
    @include flex-center;
    position: absolute;
    top: 50%;
    z-index: 10;
    width: 48px;
    height: 48px;
    background: var(--bg-color-menu-active);
    border-radius: 8px;
    transform: translateY(-50%);

    .am-icon {
      display: block;
      width: 32px;
      height: 32px;
      object-fit: contain;
    }

    &.swiper-button-disabled {
      background: var(--bg-color-menu-default);
    }
  }

  .nav-prev {
    left: 0;
  }

  .nav-next {
    right: 0;
  }

  .game-menu-swiper {
    width: 100%;
    overflow: hidden;
  }

  .game-menu-scrollbar {
    width: 48px;
    height: 8px;
    margin: 20px auto 0;
    background-color: rgb(151 132 191 / 50%);
    border-radius: 4px;

    .swiper-scrollbar-drag {
      background: linear-gradient(180deg, #fff5c6 0%, #d1ae4c 100%);
    }
  }

  .game-menu-list {
    width: 100%;

    .game-menu-item {
      position: relative;
      flex-shrink: 0;
      width: 140px;
      font-size: 24px;
      font-weight: 400;
      line-height: normal;
      color: var(--text-color-primary);
      text-align: center;
      letter-spacing: 0.48px;

      &.on {
        color: var(--text-color-accent);
      }

      .menu-item-inner {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 12px 0;

        &.on {
          .game-menu-icon {
            border: 1px solid var(--border-color-accent);
          }
        }
      }

      .game-menu-name {
        @include flex-center;

        span {
          display: block;
          width: 100%;

          @include ellipsis;
        }
      }

      .game-menu-icon {
        display: block;
        padding: 8px;
        background: var(--bg-color-surface);
        border-radius: 8px;

        img,
        .am-icon {
          display: block;
          width: 48px;
          height: 48px;
          object-fit: contain;
        }
      }
    }
  }
}
