import React from "react";
import dayjs from "dayjs";
import { debounce } from "lodash";
import { inject, observer } from "mobx-react";
import PubSub from "pubsub-js";
import { currencyFormat, Marquee, navigatorUtil } from "tcg-mobile-common";

import withGame from "@/hoc/withGame";

// Import Common Stylesheets
import "./WinnerMarquee.scss";

const { isApp, isPWA } = navigatorUtil;

@inject("languageShell", "gameCenter", "tcgCommon")
@withGame
@observer
class WinnerMarquee extends React.Component {
  get winnerList() {
    return this.props.gameCenter.winnerList;
  }
  constructor(props) {
    super(props);
    this.state = {
      items: [],
      isEmpty: true,
    };
  }

  componentDidMount() {
    this.getWinnerBoard();
    this.changeList = PubSub.subscribe(
      "changeLanguage",
      debounce(() => {
        this.getWinnerBoard();
      }, 250)
    );
  }
  componentWillUnmount() {
    PubSub.unsubscribe(this.changeList);
  }
  getWinnerBoard() {
    const query = {
      gameCategory: "ALL",
      language: this.props.languageShell.currentLanguage,
      limitNum: 20,
    };
    this.props.gameCenter.getWinnerBoard(query).then(() => {
      if (this.winnerList.length > 0) this.setItems();
    });
  }

  setItems = () => {
    let items = this.winnerList;
    let isEmpty = false;
    if (!items || items.length === 0) {
      items = [
        {
          id: -1,
          title: this.props.languageShell.resource["no_announcement"],
        },
      ];
      isEmpty = true;
    }
    this.setState({
      items,
      isEmpty,
    });
  };

  launchGame = (game) => {
    this.props.launchWinnerBoard(game);
  };

  getContent() {
    const { items, isEmpty } = this.state;
    return (
      <Marquee isEmpty={isEmpty} fps={30}>
        {items.map((i, idx) => (
          <div className={`content-item ${isEmpty ? "empty" : ""}`} key={`ms-${i.recordId}`}>
            <span>{`${i.customerName} WON:`}</span>
            <div className="win-amount-num">
              <span className="symbol">{this.props.tcgCommon.currencySymbol}</span>
              {currencyFormat.format({ value: i.winAmount, fixed: 2 })}
            </div>
            <span className="win-game" onClick={() => this.launchGame(i)}>
              {i.gameName}
            </span>
          </div>
        ))}
      </Marquee>
    );
  }

  handleDownload = () => {
    this.props.common.setDownPopup(true);
  };

  formatDate = (date) => {
    return dayjs(date).format("YYYY/MM/DD");
  };

  coverContent = (content) => {
    const temp = document.createElement("div");
    temp.style.display = "none";
    temp.innerHTML = content;
    const convert = temp.textContent || temp.innerText;
    temp.remove();
    return convert;
  };

  render() {
    return (
      <div className="winner-marquee-wrap">
        <div className="winner-marquee-bg">
          <div className="winner-marquee-icon">
            <img src={require("@/assets/images/home/<USER>")} />
          </div>
          <div className="marquee">{this.getContent()}</div>
        </div>
      </div>
    );
  }
}
export default WinnerMarquee;
