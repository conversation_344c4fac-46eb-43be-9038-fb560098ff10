import React from "react";
import PropTypes from "prop-types";

import deploy from "@/config/deploy.config";

import "./Skeleton.scss";

class Skeleton extends React.Component {
  render() {
    return (
      <ul className="skeleton-wrap">
        {Array.from(new Array(this.props.size).keys()).map((index) => {
          return <li key={`skeleton-${index}`} className={deploy.gameImgSize} />;
        })}
      </ul>
    );
  }
}

Skeleton.propTypes = {
  className: PropTypes.string,
  size: PropTypes.number,
};

Skeleton.defaultProps = {
  className: "",
  size: 6,
};

export default Skeleton;
