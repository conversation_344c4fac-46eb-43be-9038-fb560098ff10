.skeleton-wrap {
  @include game-list;
  overflow: hidden;

  li {
    position: relative;
    width: 100%;
    aspect-ratio: 1/1;
    overflow: hidden;
    background: var(--game-list-skeleton-bg);
    border-radius: 12px;

    &.rx2 {
      aspect-ratio: 400/540;
    }

    &::after {
      position: absolute;
      inset: 0;
      content: "";
      background-image: linear-gradient(
        90deg,
        rgb(255 255 255 / 0%) 0,
        rgb(255 255 255 / 20%) 20%,
        rgb(255 255 255 / 40%) 60%,
        rgb(255 255 255 / 0%)
      );
      transform: translateX(-100%);
      animation: shimmer 3s infinite;
    }
  }
}
