import React from "react";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { debounce } from "lodash";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import "./SearchInput.scss";

@inject("languageShell")
@observer
class SearchInput extends React.Component {
  inputRef = null;
  state = { gameName: "" };
  componentDidMount() {
    this.props.setRef && this.props.setRef(this);
    if (this.props.gameName) {
      this.setState({
        gameName: this.props.gameName,
      });
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.gameName !== this.props.gameName) {
      this.setState({
        gameName: this.props.gameName,
      });
    }
  }

  setGameName = (e) => {
    const name = e.target.value;
    this.setState({ gameName: name });
    if (e.key === "Enter") {
      e.target.blur();
      this.setState({ gameName: e.target.value }, () => {
        this.props.searchGame(this.state.gameName.trim());
      });
    }
  };
  handleInput = (e) => {
    this.setState({ gameName: e.target.value }, () => {
      this.searchGameByName();
    });
  };
  searchGameByName = debounce(() => {
    this.props.searchGame(this.state.gameName.trim());
  }, 500);
  handleSubmit = () => {
    if (this.props.disabled) {
      this.props.handleClick();
    } else {
      this.searchGameByName();
    }
  };
  clear = () => {
    this.setState({ gameName: "" });
  };
  focus = () => {
    this.inputRef?.focus();
  };
  handleBlur = () => {
    if (!this.state.gameName) {
      this.props.close();
    }
  };
  render() {
    return (
      <div className={cz("search-input-wrap", this.props.className, { disabled: this.props.disabled })}>
        <div className="search-input-content">
          <input
            className="search-input"
            name="search-input"
            type="text"
            autoComplete="off"
            disabled={this.props.disabled}
            placeholder={this.props.languageShell.t("search")}
            value={this.state.gameName}
            onInput={this.handleInput}
            onKeyUp={this.setGameName}
            ref={(e) => (this.inputRef = e)}
          />
          <div className="search-btn" onClick={this.handleSubmit}>
            <Icon className="search-icon" type={require("!svg-sprite-loader!@/assets/images/common/icon-search.svg")} />
          </div>
        </div>
      </div>
    );
  }
}

SearchInput.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  handleClick: PropTypes.func,
  gameName: PropTypes.string,
  close: PropTypes.func,
};

SearchInput.defaultProps = {
  className: "",
  disabled: false,
  gameName: "",
  handleClick: () => {},
  close: () => {},
};

export default SearchInput;
