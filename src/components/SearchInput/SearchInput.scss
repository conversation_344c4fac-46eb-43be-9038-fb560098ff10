.search-input-wrap {
  position: relative;
  flex-shrink: 0;
  width: 100%;
  min-width: 0;

  &.disabled::after {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    content: "";
  }

  .search-input-content {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;

    .search-input {
      flex: 1;
      width: 100%;
      height: 64px;
      padding: 0 16px 0 60px;
      font-size: 24px;
      font-weight: 400;
      color: var(--text-color-primary);
      background: var(--bg-color-surface);
      border: 1px solid var(--border-color-surface);
      border-radius: 100px;

      &::placeholder {
        color: var(--text-color-primary);
      }

      &:focus {
        & + .search-btn {
          .search-icon {
            color: #fff;
          }
        }
      }
    }

    .search-btn {
      @include flex-center;
      position: absolute;
      top: 16px;
      left: 16px;
      flex-shrink: 0;
    }

    .search-icon {
      display: block;
      width: 32px;
      height: 32px;
      fill: none;
    }
  }
}
