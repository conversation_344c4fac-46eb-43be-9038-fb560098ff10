.provider-popup-container {
  .provider-popup-bg {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: $provider-popup-index;
    width: 100%;
  }

  .popup-close {
    position: absolute;
    top: -70px;
    right: 20px;
    width: 54px;
    height: 54px;

    svg {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .provider-popup-content {
    max-height: 80vh;
    padding: 50px 24px 70px;
    overflow-y: auto;
    background: rgb(243 246 249 / 90%);
    border-top: 5px solid #80b4fe;
    border-radius: 40px 40px 0 0;
    box-shadow: 0 -6px 30px 0 rgb(0 0 0 / 25%);
    backdrop-filter: blur(2px);
  }

  .provider-title {
    margin-bottom: 30px;
    font-size: 40px;
    font-weight: 700;
    line-height: normal;
    color: #4c5d6f;
    text-align: center;
  }

  .provider-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 26px 20px;

    .provider-item {
      @include flex-center;
      min-width: 0;
      height: 70px;
      font-size: 23px;
      font-weight: 600;
      color: #76797d;
      text-align: center;
      background: #fff;
      border: 2px solid #d9e1ec;
      border-radius: 70px;

      &.on {
        color: #fff;
        background: linear-gradient(90deg, #97d2fe 0%, #2a87f1 100%);

        .icon-active {
          display: block;
        }

        .icon-normal {
          display: none;
        }
      }
    }

    .provider-all {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 0 18px;

      .icon-all {
        width: 34px;
        height: 34px;
        margin-right: 8px;
      }

      span {
        flex: 1;
      }
    }

    .provider-icon {
      display: block;
      width: 100px;
      height: 50px;
      object-fit: cover;
    }

    .icon-normal {
      opacity: 0.5;
    }

    .icon-active {
      display: none;
    }
  }

  .provider-btns {
    margin-top: 80px;
    font-size: 32px;
    font-weight: 700;
    color: #fff;
    text-align: center;

    .btn-reset {
      @include flex-center;
      height: 60px;
      background: rgb(0 180 216 / 0%);
      border: 2px solid #fff;
      border-radius: 8px;
    }

    .btn-confirm {
      @include flex-center;
      height: 72px;
      background: linear-gradient(180deg, #80b4fe 0%, #74adfe 50%, #599eff 100%);
      border-radius: 50px;
      box-shadow: 0 4px 0 -1px rgb(255 255 255 / 20%) inset;
    }
  }
}
