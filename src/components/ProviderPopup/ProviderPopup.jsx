import React from "react";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import CommonPopup from "@/ui/CommonPopup";

import "./ProviderPopup.scss";

@inject("languageShell", "common")
@observer
class ProviderPopup extends React.Component {
  get allProviders() {
    return [{ vassalage: "", displayName: "in_type_all" }, ...this.props.vendors];
  }
  constructor(props) {
    super(props);
    this.state = {
      vassalage: "",
    };
  }
  componentDidMount() {
    if (this.props.vassalage) {
      this.setState({
        vassalage: this.props.vassalage,
      });
    }
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.vassalage !== this.props.vassalage) {
      this.setState({
        vassalage: this.props.vassalage,
      });
    }
  }
  closePopup = () => {
    this.props.common.showProvider(false);
  };
  handleSelect = (vassalage) => {
    this.setState({ vassalage });
  };
  handleReset = () => {
    this.setState({ vassalage: "" });
  };
  handleConfirm = () => {
    this.closePopup();
    this.props.handleSelect(this.state.vassalage);
  };
  render() {
    return (
      <CommonPopup
        className="provider-popup-container"
        closeable={false}
        position="bottom"
        show={this.props.common.providerStatus}
        onClose={this.closePopup}
        closeOnClickOverlay
      >
        <div className="provider-popup-bg">
          <div className="popup-close" onClick={this.closePopup}>
            <Icon type={require("!svg-sprite-loader!@/assets/images/common/popup-close.svg")} />
          </div>
          <div className="provider-popup-content">
            <div className="provider-title">{this.props.languageShell.t("game_providers")}</div>
            <div className="provider-list">
              {this.allProviders.map((item, index) => {
                return (
                  <div
                    key={`provider_item_${index}`}
                    className={cz("provider-item", { on: item.vassalage === this.state.vassalage })}
                    onClick={() => this.handleSelect(item.vassalage)}
                  >
                    {item.vassalage === "" ? (
                      <div className="provider-all">
                        <Icon
                          className="icon-all"
                          type={require("!svg-sprite-loader!@/assets/images/common/icon-provider.svg")}
                        />
                        <span>{this.props.languageShell.t(item.displayName)}</span>
                      </div>
                    ) : (
                      [
                        <img
                          className="provider-icon icon-normal"
                          src={`${this.props.common.vendorIconCDN}${item.vassalage}-BLACK.png`}
                          alt=""
                        />,
                        <img
                          className="provider-icon icon-active"
                          src={`${this.props.common.vendorIconCDN}${item.vassalage}-WHITE.png`}
                          alt=""
                        />,
                      ]
                    )}
                  </div>
                );
              })}
            </div>
            <div className="provider-btns">
              {/* <div className="btn-reset" onClick={this.handleReset}>
                  {this.props.languageShell.t("btn_reset")}
                </div> */}
              <div className="btn-confirm" onClick={this.handleConfirm}>
                {this.props.languageShell.t("in_increase_submit")}
              </div>
            </div>
          </div>
        </div>
      </CommonPopup>
    );
  }
}

ProviderPopup.propTypes = {
  vendors: PropTypes.array,
  vassalage: PropTypes.string,
  handleSelect: PropTypes.func.isRequired,
};

ProviderPopup.defaultProps = {
  vendors: [],
  vassalage: "",
};

export default ProviderPopup;
