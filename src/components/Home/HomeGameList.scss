.home-game-list-wrap {
  position: relative;
  width: 100%;
  overflow: hidden;

  .lottery-list-wrap {
    width: 100%;
    margin-top: 16px;
    overflow: auto;

    .lottery-list {
      display: grid;
      grid-template-rows: repeat(2, 1fr);
      grid-auto-columns: 550px;
      grid-auto-flow: column;
      gap: 16px 20px;
    }
  }

  .vendor-list {
    display: flex;
    padding-top: 0;
    overflow: auto;

    li {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 200px;
      height: 80px;
      padding: 0 20px;
      margin-right: 10px;

      font-size: 24px;
      color: #fff;
      background: #050505;
      border: 2px solid #1a5f2e;
      border-radius: 16px;

      &.on {
        background: #049e07;
        border: none;
      }

      img {
        height: 100%;
      }
    }
  }

  .home-game-list-content {
    width: 100%;
  }

  .home-game-list {
    display: grid;
    grid-template-rows: repeat(1, 1fr);
    grid-auto-flow: column;
    gap: 16px;
    padding: 0 20px;
    overflow: auto hidden;

    .game-list-item {
      width: 200px;
    }
  }

  .home-game-slide {
    display: grid;
    gap: 20px;
    width: 100%;
  }

  .home-vendor-list {
    display: flex;
    flex-wrap: nowrap;
    overflow: auto;

    .vendor-game-item {
      width: 220px;

      &:not(:last-child) {
        margin-right: 20px;
      }
    }
  }

  .swiper-btn-group {
    display: grid;
    grid-auto-flow: column;
    gap: 10px;
    margin-left: 10px;

    .swiper-btn {
      width: 60px;
      height: 60px;
    }

    .am-icon {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .skeleton-wrap {
    display: grid;
    grid-template-rows: repeat(2, 1fr);
    grid-template-columns: none;
    grid-auto-columns: 220px;
    grid-auto-flow: column;
    gap: 20px 25px;
  }
}

.home-vendor-list-wrap {
  .scroll-game-container {
    .scroll-game-list {
      --grid-col-width: 240px;
    }
  }
}
