import React, { Component } from "react";
import { withRouter } from "react-router-dom";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { navigatorUtil, withCs, withReferral } from "tcg-mobile-common";

import HomeFooter from "@/components/Footer/HomeFooter";
import SearchInput from "@/components/SearchInput/SearchInput";
import deploy from "@/config/deploy.config";
import { navConfig } from "@/config/game.config";
import { homeScrollTop } from "@/utils/dom";

import JackPot from "../Jackpot/Jackpot";
import ProviderGameList from "../ProviderGameList/ProviderGameList";
import WinnerBoard from "../WinnerBoard/WinnerBoard";

import HomeGameList from "./HomeGameList";
import HomeVendorList from "./HomeVendorList";

import "./GameLobby.scss";

const { isApp, isPWA } = navigatorUtil;
const showDownload = !isApp && !isPWA;

const PAGE_SIZE = 16;
const { gameSelectType } = deploy;

@withReferral
@withCs
@inject("languageShell", "gameCenter", "tcgCommon", "mcMenu", "auth", "common")
@withRouter
@observer
class GameLobby extends Component {
  get sorting() {
    return this.props.gameCenter.gameVendor.sorting;
  }
  get mapping() {
    return this.props.gameCenter.gameVendor.mapping;
  }
  get helpList() {
    return this.props.common.helpList;
  }
  get hotList() {
    return this.props.gameCenter.hotGames?.slice(0, 20);
  }
  get gameContent() {
    return this.props.gameCenter.gameVendor.sorting;
  }
  get resources() {
    return this.props.common.downLinkObj;
  }
  get facebookPage() {
    return this.resources["FACEBOOK"]?.url;
  }
  get youtubeLink() {
    return this.props.common.banners.find((item) => item.groupName === "m_video")?.linkage;
  }
  get isLogin() {
    return this.props.auth.isLogin;
  }
  get matchs() {
    return this.props.tcgCommon.gameMatches;
  }
  state = { favGames: [], gameName: "" };
  componentDidMount() {
    // this.getFavGameList();
  }
  getGameList = (item) => {
    const { gameType = "", gameClassify = "", isNew = undefined, vassalage = "" } = item;
    return this.props.gameCenter.getGameList({
      merchant: deploy.merchant,
      platform: "html5",
      gameName: "",
      gameType: gameType,
      pageNo: 1,
      pageSize: PAGE_SIZE,
      vassalage: vassalage,
      gameClassify: gameClassify,
      isNew: isNew,
      minBet: "",
      minLine: "",
      language: this.props.languageShell.language,
    });
  };

  getFavGameList = (refresh) => {
    if (this.props.auth.currentToken) {
      this.props.gameCenter
        .getFavGameList(
          {
            pageNo: 1,
            gameType: "",
            vendor: "",
            pageSize: PAGE_SIZE,
            language: this.props.languageShell.currentLanguage,
          },
          !refresh
        )
        .then((res) => {
          this.setState({
            favGames: res?.content || [],
          });
        });
    }
  };

  renderContent = (gameCategory) => {
    if (deploy.gameListType.includes(gameCategory)) {
      return (
        <HomeGameList
          key={`home_lobby_${gameCategory}`}
          gameType={gameCategory}
          row={1}
          moreGame={() => this.moreGame(gameCategory)}
          title={this.props.languageShell.t(navConfig[gameCategory]?.name)}
        />
      );
    } else {
      return (
        <HomeVendorList
          key={`home_lobby_${gameCategory}`}
          gameType={gameCategory}
          row={1}
          moreGame={() => this.moreGame(gameCategory)}
          title={this.props.languageShell.t(navConfig[gameCategory]?.name)}
        />
      );
    }
  };

  moreGame = async (gameType) => {
    // const path = gamePath[gameType];
    // if (path) {
    //   this.props.history.push(path);
    // }
    await this.props.gameCenter.setCurrentGameCategory(gameType);
    homeScrollTop();
  };

  handleProvider = (vassalage) => {
    this.props.gameCenter.setCurrentVassalage(vassalage);
    this.props.gameCenter.setCurrentGameCategory("RNG");
    homeScrollTop();
  };

  handleDataType = (dataType) => {
    this.props.gameCenter.setCurrentDataType(dataType);
    this.props.gameCenter.setCurrentGameCategory("RNG");
    homeScrollTop();
  };

  handleSearchByName = (gameName) => {
    this.setState({
      gameName: gameName,
    });
  };

  render() {
    return (
      <div className="home-game-lobby">
        <div className="game-search-section">
          <SearchInput searchGame={this.handleSearchByName} gameName={this.state.gameName} />
        </div>
        {this.state.gameName !== "" ? (
          <ProviderGameList dataType={gameSelectType.SEARCH} gameName={this.state.gameName} />
        ) : (
          <div className="home-lobby-content">
            <HomeGameList
              gameType="HOT"
              games={this.hotList}
              title={this.props.languageShell.t("most_popular")}
              row={1}
            />
            <HomeGameList gameType="NEW" title={this.props.languageShell.t("new_games")} row={1} />
            {this.sorting?.map((item) => {
              return this.renderContent(item.gameCategory);
            })}
            <div className="jackpot-winner">
              <JackPot />
              <WinnerBoard />
            </div>
            <HomeFooter />
          </div>
        )}
      </div>
    );
  }
}

GameLobby.propTypes = {
  gameType: PropTypes.string,
  menus: PropTypes.array.isRequired,
};

GameLobby.defaultProps = {
  menus: [],
};

export default GameLobby;
