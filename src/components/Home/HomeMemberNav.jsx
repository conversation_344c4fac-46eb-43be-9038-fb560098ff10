import React from "react";
import { inject, observer } from "mobx-react";

import withMemberMenu from "@/hoc/withMemberMenu";

import { homeMenuList } from "./homeMemuList";

import "./HomeMemberNav.scss";

@inject("languageShell")
@withMemberMenu
@observer
class HomeMemberNav extends React.Component {
  render() {
    const { menuHelper } = this.props;
    return (
      <div className="home-member-nav">
        <div className="home-member-nav-list">
          {homeMenuList?.map((item, index) => {
            return item.type === "SEPARATE" ? (
              <div className="nav-line" key={`menu_line_${index}`} />
            ) : (
              menuHelper.displayMenu(item) && (
                <div
                  className="home-member-nav-item"
                  key={`home_member_nav_${index}`}
                  onClick={() => menuHelper.menuClick(item)}
                >
                  <img className="nav-icon" src={item.icon} alt="" />
                  {item.resource ? (
                    <span>
                      {menuHelper.state?.resources?.[item.resource]?.labelNameEn ||
                        this.props.languageShell.t(item.name)}
                    </span>
                  ) : (
                    <span>{this.props.languageShell.t(item.name)}</span>
                  )}
                </div>
              )
            );
          })}
        </div>
      </div>
    );
  }
}

export default HomeMemberNav;
