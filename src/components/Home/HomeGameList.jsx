import React from "react";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import GameListItem from "@/components/GameListItem/GameListItem";
import ScrollGameList from "@/components/ScrollGameList/ScrollGameList";
import deploy from "@/config/deploy.config";

import "./HomeGameList.scss";

const PAGE_SIZE = 20;
const { gameSelectType } = deploy;

@inject("languageShell", "gameCenter", "common", "tcgCommon")
@observer
class HomeGameList extends React.Component {
  get gameList() {
    if (this.props.games?.length > 0) {
      return this.props.games;
    }
    return this.state.gameList;
  }

  get gameCount() {
    if (this.props.games?.length > 0) {
      return this.props.games?.length;
    }
    return this.state.totalCount;
  }

  constructor(props) {
    super(props);
    this.state = {
      gameList: [],
      gameType: props.gameType,
      vassalage: props.vassalage,
      gameClassify: "",
      minBet: "",
      gameName: "",
      dataType: props.dataType,
      currentPage: 1,
      totalPages: 1,
      hasMore: false,
      totalCount: 0,
      skeleton: false,
    };
  }

  componentDidMount() {
    this.initGame();
  }

  initGame = () => {
    if (this.props.gameType === "HOT") {
      return;
    }
    switch (this.props.gameType) {
      case "BINGO":
        return this.filterClick({
          gameType: "RNG",
          gameClassify: "BG",
        });
      case "JACKPOT":
        return this.filterClick({
          gameType: "RNG",
          gameClassify: "JP",
        });
      case "FEATURE":
        return this.filterClick({
          gameType: "RNG",
          gameClassify: "FeatureBuy",
        });
      case "PG":
      case "JDB":
        return this.filterClick({
          gameType: "RNG",
          vassalage: this.props.gameType,
        });
      case "JL":
        return this.filterClick({
          gameType: "RNG",
          vassalage: "JL",
        });
      case "INHOUSE":
        return this.filterClick({
          gameType: "RNG",
          vassalage: deploy.inHouse.join(","),
        });
      case "NEW":
        return this.filterClick({
          gameType: "",
          gameClassify: gameSelectType.NEW,
        });
      default:
        return this.filterClick({
          gameType: this.props.gameType,
          vassalage: this.props.vassalage,
        });
    }
  };

  handleSelectVassalage = (vassalage) => {
    this.filterClick({ vassalage, dataType: gameSelectType.ALL });
  };

  handleChangeClassity = (item) => {
    const { gameClassify } = item;
    this.filterClick({ gameClassify, dataType: gameSelectType.ALL });
  };

  filterClick = (item = {}) => {
    const {
      vassalage = this.state.vassalage,
      gameType = this.state.gameType,
      dataType = this.state.dataType,
      gameClassify = this.state.gameClassify,
      gameName = "",
    } = item;
    let type = dataType;
    if ([gameSelectType.HOT, gameSelectType.FAV, gameSelectType.RECENT].includes(gameClassify)) {
      type = gameClassify;
    }
    this.setState(
      {
        gameType,
        vassalage,
        gameClassify,
        gameList: [],
        dataType: type,
        currentPage: 1,
        totalPages: 1,
        minBet: "",
        gameName,
        hasMore: false,
        skeleton: false,
      },
      () => {
        this.getGameList(true);
      }
    );
  };

  getGameList = (refresh) => {
    this.setState({
      skeleton: true,
    });
    this.props.gameCenter
      .getGameList({
        merchant: deploy.merchant,
        platform: "html5",
        gameName: this.state.gameName,
        gameType: this.state.gameType,
        pageNo: this.state.currentPage,
        pageSize: PAGE_SIZE,
        vassalage: this.state.vassalage,
        gameClassify: this.state.gameClassify === gameSelectType.NEW ? "" : this.state.gameClassify,
        minBet: this.state.minBet,
        minLine: "",
        isNew: this.state.gameClassify === gameSelectType.NEW ? "1" : "",
        language: this.props.languageShell.language,
      })
      .then((res) => {
        if (refresh) {
          this.setState({
            gameList: res.games,
            currentPage: res.pageNum,
          });
        } else {
          this.setState((state) => ({
            gameList: state.gameList.concat(res.games),
            currentPage: res.pageNum,
          }));
        }
        const { totalPages, pageNum, totalCount } = res;
        const hasMore = pageNum < totalPages;
        this.setState({ hasMore, totalCount: totalCount, totalPages });
      })
      .finally(() => {
        this.setState({
          skeleton: false,
        });
      });
  };

  getItemSize = (index) => {
    if (this.props.gameType === "HOT") {
      return index % 7 === 0;
    } else {
      return (index - 6) % 7 === 0;
    }
  };

  render() {
    if (this.gameList?.length < 1) {
      return null;
    }
    return (
      <div className={cz("home-game-list-wrap", this.props.className)}>
        <div className="home-game-list-content">
          <ScrollGameList
            gameType={this.props.gameType}
            title={this.props.title}
            count={this.gameCount}
            rows={this.props.row}
            scrollCount={3}
          >
            {this.gameList.map((item) => (
              <GameListItem item={item} key={`${this.props.gameType}-${item.nodeId}`} />
            ))}
          </ScrollGameList>
          {/* <div className="home-game-list hide-scrollbar">
            {this.gameList.map((item, index) => (
              <GameListItem item={item} visibleByDefault={true} key={`${this.props.gameType}-${item.nodeId}`} />
            ))}
          </div> */}
        </div>
      </div>
    );
  }
}

HomeGameList.propTypes = {
  className: PropTypes.string,
  games: PropTypes.array.isRequired,
  gameType: PropTypes.string.isRequired,
  vassalage: PropTypes.string,
  moreGame: PropTypes.func,
  row: PropTypes.number,
  title: PropTypes.string,
};

HomeGameList.defaultProps = {
  className: "",
  vassalage: "",
  games: [],
  row: 1,
  title: "",
  moreGame: null,
};

export default HomeGameList;
