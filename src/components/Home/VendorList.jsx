import React, { Component } from "react";
import { with<PERSON>outer } from "react-router-dom";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import VendorGameItem from "@/components/VendorGameItem/VendorGameItem";
import deploy from "@/config/deploy.config";
import withGame from "@/hoc/withGame";

import "./VendorList.scss";

const { gameSelectType } = deploy;

@inject("common", "languageShell", "gameCenter")
@withGame
@withRouter
@observer
class VendorList extends Component {
  render() {
    const { className, gameType } = this.props;
    return (
      <div className={cz("vendor-list-wrap", className)}>
        <div className="home-vendor-list">
          {this.props.vendors.map((item, index) => {
            return <VendorGameItem key={`home_vendor_${item.id}_${index}`} item={item} gameType={gameType} />;
          })}
        </div>
      </div>
    );
  }
}

VendorList.propTypes = {
  className: PropTypes.string,
  gameType: PropTypes.string,
  vendors: PropTypes.array,
};

VendorList.defaultProps = {
  gameType: "",
  className: "",
  vendors: [],
};

export default VendorList;
