import React from "react";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import GameTitle from "@/components/GameTitle/GameTitle";
import ScrollGameList from "@/components/ScrollGameList/ScrollGameList";
import VendorGameItem from "@/components/VendorGameItem/VendorGameItem";

import "./HomeGameList.scss";

@inject("languageShell", "gameCenter", "common", "tcgCommon")
@observer
class HomeVendorList extends React.Component {
  get currentVendors() {
    return this.vendors.slice(0, this.state.currentPage * this.props.pageSize);
  }
  get vendors() {
    return this.props.gameCenter.gameVendor.mapping[this.props.gameType] || [];
  }
  get hasMore() {
    return this.state.currentPage < this.totalPages;
  }

  get totalPages() {
    return Math.ceil(this.vendors?.length / this.props.pageSize);
  }
  get showNav() {
    return this.vendors?.length > 3;
  }
  get matchs() {
    return this.props.tcgCommon.gameMatches;
  }

  state = {
    currentPage: 1,
  };

  loadMore = () => {
    this.setState({
      currentPage: this.state.currentPage + 1,
    });
  };

  render() {
    if (!this.vendors?.length) {
      return null;
    }
    return (
      <div className={`home-game-list-wrap home-vendor-list-wrap ${this.props.className}`}>
        {this.props.hasTitle && (
          <div className="home-game-title">
            <GameTitle gameType={this.props.gameType} />
            <div className="more-game" onClick={this.props.moreGame}>
              <span>{this.props.languageShell.t("see_all")}</span>
            </div>
          </div>
        )}
        <div className="home-game-list-content">
          <ScrollGameList
            gameType={this.props.gameType}
            count={this.vendors?.length}
            title={this.props.title}
            showNav={this.vendors?.length > 3}
          >
            {this.vendors.map((item, index) => {
              return <VendorGameItem key={`${this.props.gameType}_${index}`} item={item} />;
            })}
          </ScrollGameList>
          {/* <div className="home-vendor-list hide-scrollbar">
            {this.vendors.map((item, index) => {
              return <VendorGameItem key={`${this.props.gameType}_${index}`} item={item} />;
            })}
          </div> */}

          {/* {this.hasMore && <GameMoreBtn onClick={this.loadMore} />} */}
        </div>
      </div>
    );
  }
}

HomeVendorList.propTypes = {
  className: PropTypes.string,
  gameType: PropTypes.string.isRequired,
  pageSize: PropTypes.number,
  hasTitle: PropTypes.bool,
};

HomeVendorList.defaultProps = {
  className: "",
  gameType: "",
  pageSize: 9,
  hasTitle: false,
};

export default HomeVendorList;
