import React from "react";
import { withRouter } from "react-router";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import "./GiftPopup.scss";

@inject("languageShell", "common", "personal")
@withRouter
@observer
class GiftPopup extends React.Component {
  get promoCount() {
    return this.props.personal.totalUnclaimedPromosCount;
  }
  get resource() {
    return this.props.common.downLinkObj;
  }
  state = {
    show: false,
  };

  componentDidMount() {
    if (window.sessionStorage.getItem("LOGIN_AFTER_GIFT") !== "1") {
      this.setState({ show: true });
    }
  }
  handleClaim = () => {
    this.closeModal();
    this.props.history.push("/m/rewardCenter");
  };
  setCache = () => {
    window.sessionStorage.setItem("LOGIN_AFTER_GIFT", "1");
  };
  closeModal = () => {
    this.setState({
      show: false,
    });
    this.setCache();
  };
  render() {
    if (!this.state.show || this.promoCount < 1) {
      return null;
    }
    return (
      <div className={cz("register-success-modal", { on: this.state.show })}>
        <div className="shell-modal-mask register-success" />
        <div className="register-success-content">
          <img className="gift-title" src={require("@/assets/images/common/login-gift.png")} alt="" />
          <h2 className="modal-title">{this.props.languageShell.t("gift_tip")}</h2>
          <div className="btn_claim" onClick={this.handleClaim}>
            {this.props.languageShell.t("claim_now")}
          </div>
          <div className="close-btn" onClick={this.closeModal}>
            <Icon type={require("!svg-sprite-loader!@/assets/images/common/icon-close.svg")} />
          </div>
        </div>
      </div>
    );
  }
}

GiftPopup.propTypes = {
  show: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
  token: PropTypes.string.isRequired,
};

export default GiftPopup;
