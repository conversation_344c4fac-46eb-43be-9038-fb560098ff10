.register-success-modal {
  .shell-modal-mask.register-success {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10001;
    width: 100%;
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    overscroll-behavior-y: none;
    background: rgb(0 0 0 / 50%);
    backdrop-filter: blur(5px);
  }

  .register-success-content {
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 10002;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    width: 600px;
    transform: translate(-50%, -50%);

    .modal-title {
      font-size: 38px;
      font-weight: 700;
      line-height: normal;
      color: #fff;
      text-align: center;
    }

    .gift-title {
      display: block;
      width: 600px;
    }

    .contact_promo {
      font-size: 32px;
      color: #000;
      text-align: center;
    }

    .contact_acc {
      font-weight: bold;
    }

    .btn_claim {
      width: 370px;
      height: 60px;
      margin-top: 30px;
      font-size: 24px;
      font-weight: 500;
      line-height: normal;
      color: #fff;
      background: $common-gradient;
      border: 2px solid #ff8541;
      border-radius: 20px;

      @include flex-center;
    }
  }

  .close-btn {
    position: absolute;
    top: 50px;
    right: 50px;
    width: 30px;
    height: 30px;

    .am-icon {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}
