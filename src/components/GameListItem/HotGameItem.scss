.hot-game-item {
  position: relative;
  flex-shrink: 0;
  width: 100%;
  min-width: 0;

  .game-background {
    position: relative;
    display: flex;
    justify-content: center;
    width: 100%;
    overflow: hidden;

    .hot-game-bg {
      display: block;
      width: 100%;
    }

    .vendor-small-icon {
      position: absolute;
      top: 5px;
      right: 5px;
      z-index: 1;
      width: 60px;
      height: 30px;
    }
  }

  .game-item-type {
    position: absolute;
    top: 10px;
    left: 12px;
    z-index: 1;
    font-size: 17.391px;
    font-weight: 900;
    line-height: normal;
  }

  .game-item-name {
    @include flex-center;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 35px;
    font-size: 17px;
    font-weight: 700;
    line-height: normal;
    color: #fff;
    text-align: center;
    text-transform: uppercase;
    background: url(~@/assets/images/bg/hot-item-bg.png) no-repeat center/100% 100%;
    border-radius: 0 0 17px 17px;

    span {
      @include trim(1);
    }
  }
}
