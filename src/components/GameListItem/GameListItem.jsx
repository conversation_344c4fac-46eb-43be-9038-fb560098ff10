import React, { Component } from "react";
import { withRouter } from "react-router";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { LazyLoadImage } from "tcg-mobile-common";

import deploy from "@/config/deploy.config";
import withGame from "@/hoc/withGame";
import { getToken } from "@/utils/storage";

import "./GameListItem.scss";

const iconHeart = require("!svg-sprite-loader!@/assets/images/common/game-fav-normal.svg");
const iconHeartActive = require("!svg-sprite-loader!@/assets/images/common/game-fav-active.svg");

const randomColor = {
  1: "theme-1",
  2: "theme-2",
  3: "theme-3",
  4: "theme-4",
  5: "theme-5",
  6: "theme-6",
  7: "theme-7",
  8: "theme-8",
};

@withGame
@inject("common", "languageShell", "gameCenter", "auth")
@withRouter
@observer
class GameListItem extends Component {
  get isLogin() {
    return this.props.auth.isLogin;
  }
  get myFav() {
    const { favGames } = this.props.gameCenter;
    const { nodeId } = this.props.item;
    if (!this.isLogin) {
      return false;
    }
    return favGames.includes(nodeId.toString());
  }
  get imgUrl() {
    const { showIcon, iconUrl } = this.props.item;
    return showIcon || iconUrl || "";
  }
  get isHot() {
    const { nodeId } = this.props.item;
    return this.props.gameCenter.hotGames.some((item) => item.nodeId === nodeId);
  }
  state = { loaded: false, showPortal: false, showPopup: false };
  gameClick = (free) => {
    this.props.launchGameList(this.props.item, free);
  };
  toggleIcon = (toggle, game, e) => {
    e.stopPropagation();
    if (getToken()) {
      if (toggle) {
        this.removeFav(game);
      } else {
        this.addFav(game);
      }
    } else {
      this.props.history.push("/m/login");
    }
  };
  removeFav = (game) => {
    const { nodeId, vassalage } = game;
    this.props.gameCenter.removeFavGame({
      nodeId: nodeId,
      token: getToken(),
      vendor: vassalage,
    });
  };
  addFav = (game) => {
    const { nodeId, vassalage } = game;
    this.props.gameCenter.addFavGame({
      nodeId: nodeId,
      token: getToken(),
      vendor: vassalage,
    });
  };

  getThemeClass = () => {
    return "";
    // return randomColor[random(1, 8)];
  };
  handleOnLoad = (e) => {
    this.setState({
      loaded: true,
    });
  };
  showPopup = () => {
    if (!this.isLogin) {
      return this.props.history.push("/m/login");
    }
    this.setState({
      showPortal: true,
    });
    setTimeout(() => {
      this.setState({
        showPopup: true,
      });
    }, 0);
  };
  closePopup = () => {
    this.setState({
      showPopup: false,
    });
  };
  render() {
    const item = this.props.item;
    return (
      <div className={cz("game-list-item", this.props.className, this.getThemeClass())} onClick={this.gameClick}>
        {/* {!this.state.loaded && <ImgPlaceholder />} */}

        <div className={cz("game-background", deploy.gameImgSize)}>
          <LazyLoadImage
            className="img-loading"
            src={this.imgUrl}
            errorSrc={this.props.common.defaultImage}
            visibleByDefault={this.props.visibleByDefault}
            afterLoad={this.handleOnLoad}
          />
          {!["LOTT", "ELOTT"].includes(item.gameType) && (
            <div
              className="game-fav"
              onClick={(e) => {
                this.toggleIcon(this.myFav, item, e);
              }}
            >
              <Icon
                type={this.myFav ? iconHeartActive : iconHeart}
                className={cz("favorite_icon", { on: this.myFav })}
              />
            </div>
          )}
          {this.isHot && (
            <div className="game-tag hot">
              <img className="tag-icon" src={require("@/assets/images/common/nav-hot.png")} alt="" />
              <span>{this.props.languageShell.t("hot")}</span>
            </div>
          )}
          {item.isNew === 1 && (
            <div className="game-tag new">
              <img className="tag-icon" src={require("@/assets/images/common/nav-new.png")} alt="" />
              <span>{this.props.languageShell.t("game_new")}</span>
            </div>
          )}
        </div>

        {/* {this.props.showVendorName && (
          <div className="game-vendor-name">
            <img
              className="vendor-icon"
              src={`${this.props.common.vendorIconCDN}/${item.vassalage}-WHITE.png`}
              alt=""
            />
          </div>
        )} */}
        <div className="game-item-info">
          {this.props.showVendorName && (
            <div className="game-vendor-name">
              <span>{item.displayName}</span>
            </div>
          )}
          {this.props.showGameName && (
            <div className="game-item-name">
              <span>{item.gameName || item.nodeName}</span>
            </div>
          )}
        </div>
      </div>
    );
  }
}

GameListItem.propTypes = {
  className: PropTypes.string,
  showVendorName: PropTypes.bool,
  hasFav: PropTypes.bool,
  showGameName: PropTypes.bool,
};

GameListItem.defaultProps = {
  className: "",
  visibleByDefault: false,
  hasFav: true,
  showVendorName: false,
  showGameName: true,
};

export default GameListItem;
