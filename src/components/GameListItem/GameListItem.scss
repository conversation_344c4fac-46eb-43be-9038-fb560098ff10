.game-list-item {
  position: relative;
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-width: 0;
  font-size: 24px;
  font-weight: 400;
  line-height: normal;
  color: var(--text-color-primary);
  border-radius: 12px;

  &.theme-1 {
    .game-item-info {
      background: linear-gradient(180deg, rgb(3 64 249 / 0%) 0%, #0340f9 60%);
    }
  }

  &.theme-2 {
    .game-item-info {
      background: linear-gradient(180deg, rgb(1 168 74 / 0%) 0%, #01a84a 60%);
    }
  }

  &.theme-3 {
    .game-item-info {
      background: linear-gradient(180deg, rgb(246 41 71 / 0%) 0%, #f62947 60%);
    }
  }

  &.theme-4 {
    .game-item-info {
      background: linear-gradient(180deg, rgb(147 40 244 / 0%) 0%, #9328f4 60%);
    }
  }

  &.theme-5 {
    .game-item-info {
      background: linear-gradient(180deg, rgb(178 34 1 / 0%) 0%, #b22201 60%);
    }
  }

  &.theme-6 {
    .game-item-info {
      background: linear-gradient(180deg, rgb(2 118 237 / 0%) 0%, #0276ed 60%);
    }
  }

  &.theme-7 {
    .game-item-info {
      background: linear-gradient(180deg, rgb(238 101 28 / 0%) 0%, #ee651c 60%);
    }
  }

  &.theme-8 {
    .game-item-info {
      background: linear-gradient(180deg, rgb(3 166 144 / 0%) 0%, #03a690 60%);
    }
  }

  .vendor-name {
    width: 90px;
    height: 34px;
    background: #d9d9d9;
    border-radius: 15px 0;

    @include flex-center;
  }

  .game-background {
    position: relative;
    width: 100%;
    aspect-ratio: 1/1;
    overflow: hidden;
    border-radius: 12px 12px 0 0;

    &.rx2 {
      aspect-ratio: 400/540;
    }

    .lazy-load-image-background {
      height: 100%;
    }

    .img-loading {
      display: block;
      width: 100%;
      height: 100%;
      margin: 0 auto;
    }
  }

  .game-item-info {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 16px;
    background: var(--bg-color-surface);
    border-radius: 0 0 12px 12px;
  }

  .vendor-icon {
    display: block;
    width: 100px;
    height: 50px;
    object-fit: contain;
  }

  .game-item-name {
    @include trim(1);
  }

  .choose-play {
    @include flex-center;

    .btn-play {
      padding: 16px 24px;
      font-size: 22px;
      font-weight: 700;
      line-height: normal;
      color: #fff;
      background: rgb(255 255 255 / 10%);
      border-radius: 100px;
    }

    .btn-free {
      margin-left: 16px;
      font-size: 20px;
      font-weight: 700;
      line-height: normal;
      color: #fff;
      text-decoration: underline;
      opacity: 0.7;
    }
  }

  .game-vendor-name {
    @include ellipsis;
    width: 100%;
    font-size: 20px;
    color: rgb(255 255 255 / 50%);
    text-align: center;
  }

  .game-fav {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 8px;
    background: rgb(7 24 41 / 40%);
    border-radius: 100px;

    &.on {
      color: #cf1436;
    }
  }

  .favorite_icon {
    display: block;
    width: 24px;
    height: 24px;
    fill: none;
  }

  .game-tag {
    position: absolute;
    top: 8px;
    left: 0;
    display: flex;
    align-items: center;
    padding: 4px 8px;
    font-size: 24px;
    font-weight: 500;
    line-height: normal;
    color: var(--text-color-primary);
    border-radius: 0 100px 100px 0;

    &.hot {
      background: var(--bg-color-error);
    }

    &.new {
      color: var(--text-color-secondary);
      background: var(--bg-color-highlight);
    }

    .tag-icon {
      width: 32px;
      height: 32px;
      margin-right: 5px;
    }
  }
}

.launch-game-popup {
  .game-popup-layout {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 642px;
  }

  .game-img {
    width: 312px;
    height: 312px;
    overflow: hidden;
    border-radius: 11px;

    img {
      display: block;
      width: 312px;
      height: 312px;
    }
  }

  .game-name {
    margin-top: 30px;
    font-size: 48px;
    font-weight: 500;
    line-height: normal;
    color: #fff;
  }

  .choose-play {
    @include flex-center;
    gap: 30px;
    width: 100%;
    margin-top: 50px;
    font-size: 30px;
    font-weight: 500;
    color: #fff;
    text-align: center;
    text-transform: capitalize;
    text-shadow: 0 2px 10px rgb(0 0 0 / 25%);

    .btn-play,
    .btn-free {
      @include flex-center;
      width: 300px;
      height: 86px;
      border-radius: 50px;
    }

    .btn-free {
      background: linear-gradient(0deg, #2d2d2d 0%, #2d2d2d 100%), linear-gradient(90deg, #fee93c 0%, #faa83c 100%);
      border: 2px solid #505050;
    }
  }
}
