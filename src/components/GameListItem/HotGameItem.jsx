import React, { Component } from "react";
import { withRouter } from "react-router";
import cz from "classnames";
import { random } from "lodash";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { currencyFormat, LazyLoadImage } from "tcg-mobile-common";

import withGame from "@/hoc/withGame";

import "./HotGameItem.scss";

@withGame
@inject("common", "languageShell", "gameCenter", "auth", "tcgCommon")
@withRouter
@observer
class HotGameItem extends Component {
  get imgUrl() {
    const { showIcon, iconUrl } = this.props.item;
    return showIcon || iconUrl || "";
  }
  state = { loaded: false, amount: random(this.props.minNum, this.props.maxNum, true) };
  gameClick = (free) => {
    this.props.launchGameList(this.props.item, free);
  };
  getAmount = () => {
    return this.formatToK(this.state.amount);
  };
  formatToK = (amount) => {
    if (amount >= 1000) {
      return currencyFormat.format({ value: amount / 1000, fixed: this.props.decimal }) + "k";
    }
    return currencyFormat.format({ value: amount, fixed: this.props.decimal });
  };

  handleOnLoad = (e) => {
    this.setState({
      loaded: true,
    });
  };
  render() {
    const { gameName, vassalage, gameType } = this.props.item;
    return (
      <div className={cz("hot-game-item", this.props.className)} onClick={this.gameClick}>
        <div className="game-background">
          <LazyLoadImage
            wrapperClassName="hot-game-bg"
            className="img-loading"
            src={this.imgUrl}
            errorSrc={this.props.common.defaultImage}
            visibleByDefault={this.props.visibleByDefault}
            afterLoad={this.handleOnLoad}
          />

          {/* <VendorIcon
            className="vendor-small-icon"
            visibleByDefault={true}
            folder="RNG_LIST_VENDOR"
            vendor={vassalage}
            color="COLOR2"
          /> */}
        </div>

        {/* <div className="game-item-type">{this.props.languageShell.t(navConfig[gameType]?.name)}</div>
        <div className="game-item-name">
          <span>{gameName}</span>
        </div> */}
        {/* <div className="game-item-amount">
          <span className="symbol">{this.props.tcgCommon.currencySymbol}</span>
          <span>{this.getAmount()}</span>
        </div> */}
      </div>
    );
  }
}

HotGameItem.propTypes = {
  className: PropTypes.string,
  visibleByDefault: PropTypes.bool,
  maxNum: PropTypes.number,
  minNum: PropTypes.number,
  decimal: PropTypes.number,
};

HotGameItem.defaultProps = {
  className: "",
  visibleByDefault: true,
  maxNum: 1500000,
  minNum: 400000,
  decimal: 2,
};

export default HotGameItem;
