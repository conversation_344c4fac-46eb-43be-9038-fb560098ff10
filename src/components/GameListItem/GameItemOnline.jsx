import React from "react";
import { clamp, random } from "lodash";
import PropTypes from "prop-types";

import "./GameItemOnline.scss";

class GameItemOnline extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      onlineUsers: this.getInitialOnlineUsers(props.gameType),
    };

    this.timer = null;
  }
  componentDidMount() {
    this.startUpdating();
  }
  componentWillUnmount() {
    this.clearUpdating();
  }
  getInitialOnlineUsers = (gameType) => {
    const range = this.getRange(gameType);
    return random(range[0], range[1]);
  };
  getRange = (gameType) => {
    return gameType === "RNG" ? [100, 1000] : [35, 250];
  };
  startUpdating = () => {
    const interval = random(5, 10) * 60 * 1000;
    this.timer = setInterval(() => this.updateOnlineUsers(), interval);
  };
  updateOnlineUsers() {
    const { gameType } = this.props;
    const range = this.getRange(gameType);

    this.setState((prevState) => {
      const change = random(-5, 5);
      const newUsers = clamp(prevState.onlineUsers + change, range[0], range[1]);
      return { onlineUsers: newUsers };
    });
  }
  clearUpdating() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }
  render() {
    return (
      <div className="game-item-online">
        <img className="icon-user" src={require("@/assets/images/common/icon-user.png")} />
        <span>{this.state.onlineUsers}</span>
      </div>
    );
  }
}

GameItemOnline.propTypes = {
  gameType: PropTypes.string,
};

GameItemOnline.defaultProps = {
  gameType: "",
};

export default GameItemOnline;
