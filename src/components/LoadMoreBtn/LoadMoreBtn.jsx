import React from "react";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import GameProgress from "../GameProgress/GameProgress";

import "./LoadMoreBtn.scss";

const gameName = {
  HOT: "in_hot_games",
  FAV: "in_favorite",
  RECENT: "Recent",
  LIVE: "hd_live",
  RNG: "hd_rng",
  PVP: "hd_pvp",
  SPORTS: "hd_sport",
  COCKFIGHT: "hd_cockfight",
  LOTT: "hd_lott",
  ELOTT: "hd_lott",
};

@inject("languageShell")
@observer
class LoadMoreBtn extends React.Component {
  getTitle = () => {
    if (this.props.hasMore) {
      return this.props.languageShell
        .t("display_game")
        .format(this.props.count, this.props.totalCount, this.props.languageShell.t(gameName[this.props.gameType]));
    }
    return this.props.languageShell
      .t("display_game")
      .format(this.props.totalCount, this.props.totalCount, this.props.languageShell.t(gameName[this.props.gameType]));
  };
  render() {
    if (!this.props.hasMore) {
      return null;
    }
    return (
      <div className="load-more-wrap">
        <div className={cz("load-more-btn", { on: this.props.hasMore })} onClick={this.props.loadMore}>
          <span>{this.props.languageShell.t("load_more")}</span>
        </div>
        <GameProgress count={this.props.count} totalCount={this.props.totalCount} />
        <div className="game-count">
          <span>{this.getTitle()}</span>
        </div>
      </div>
    );
  }
}

LoadMoreBtn.propTypes = {
  gameType: PropTypes.string,
  totalCount: PropTypes.number.isRequired,
  count: PropTypes.number,
  hasMore: PropTypes.bool.isRequired,
  loadMore: PropTypes.func.isRequired,
};

LoadMoreBtn.defaultProps = {
  count: 3,
  hasMore: false,
};

export default LoadMoreBtn;
