.message-nav-wrap {
  flex-shrink: 0;
  width: 100%;
  height: 90px;
  padding: 0 60px;
  background: var(--bg-color-primary);
  border-bottom: 2px solid #e3e3e3;

  .nav-links {
    display: flex;
    height: 100%;
  }

  .nav-link {
    position: relative;
    display: flex;
    align-items: center;
    height: 100%;
    font-size: 24px;
    font-weight: 400;
    color: #666;

    &:not(:last-child) {
      margin-right: 60px;
    }

    &.on {
      color: $main-color;

      &::after {
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 58px;
        height: 4px;
        content: "";
        background: $main-color;
        transform: translateX(-50%);
      }
    }
  }
}
