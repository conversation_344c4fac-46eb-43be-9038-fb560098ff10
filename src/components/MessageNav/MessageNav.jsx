import React from "react";
import { <PERSON> } from "react-router-dom";
import cz from "classnames";
import { inject, observer } from "mobx-react";

import "./MessageNav.scss";

@inject("languageShell")
@observer
class MessageNav extends React.Component {
  render() {
    return (
      <div className="message-nav-wrap">
        <div className="nav-links">
          <Link to="/m/help" className={cz("nav-link", { on: location.pathname === "/m/help" })}>
            {this.props.languageShell.t("support")}
          </Link>
          <Link to="/m/notice" className={cz("nav-link", { on: location.pathname === "/m/notice" })}>
            {this.props.languageShell.t("messages")}
          </Link>
        </div>
      </div>
    );
  }
}

export default MessageNav;
