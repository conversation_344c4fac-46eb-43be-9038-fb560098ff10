.home-promo-wrap {
  flex-shrink: 0;
  width: 100%;
  margin-top: 16px;
  overflow: hidden;

  .home-game-title {
    padding: 0 30px;
    margin-bottom: 15px;

    .game-title-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 5px;
      border-bottom: 1px solid #a3a3a3;
    }

    .title-content {
      display: flex;
      align-items: center;
      font-size: 32px;
      font-weight: 500;
      line-height: normal;
      text-align: center;
      text-shadow: 0 0 3.69px #ffcd00;
      background: linear-gradient(180deg, #fff 0%, #ffee98 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .title-icon {
      display: block;
      width: 40px;
      height: 40px;
      margin-right: 10px;
    }

    .see-all {
      font-size: 30px;
      font-weight: 500;
      line-height: normal;
      color: #fff;
      text-align: center;
    }
  }

  .promo-swiper {
    position: relative;

    .swiper-slide {
      width: 536px;
      height: 244px;

      .promo-item {
        transform: scale(0.92);
      }
    }

    .swiper-slide-active {
      .promo-item {
        transform: scale(1);
      }
    }
  }

  .promo-swiper-list {
    position: relative;
    width: 100%;

    .promo-item {
      position: relative;
      width: 100%;
      height: 100%;
      padding: 20px 16px;
      background: linear-gradient(0deg, rgb(0 0 0 / 20%) 0%, rgb(0 0 0 / 20%) 100%),
        linear-gradient(258deg, #818c92 7.64%, #343941 37.78%, #272429 66.93%);
      border: 3px solid #39ff07;
      border-radius: 32px;
      box-shadow: 0 1px 25px 4px rgb(30 255 0 / 25%) inset;
      transition: all 0.2s;

      .item-title {
        @include trim(1);
        width: 100%;
        font-size: 29px;
        font-weight: 500;
        line-height: normal;
        color: #fff;
      }

      .item-content {
        @include trim(1);
        width: 100%;
        font-size: 40px;
        font-weight: 700;
        line-height: normal;
        color: #f09d00;
      }
    }

    .promo-item-bg {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 32px;
    }

    .promo-links {
      display: flex;
      gap: 25px;
      align-items: center;
      margin-top: 20px;
    }

    .btn-join {
      @include flex-center;
      width: 106px;
      height: 37px;
      font-size: 16.154px;
      font-weight: 500;
      line-height: normal;
      color: #fff;
      text-align: center;
      text-shadow: 0 2.244px 8.974px rgb(0 0 0 / 25%);
      background: radial-gradient(
          226.27% 83.48% at 54.76% -93.51%,
          #a8a8a8 17%,
          #878787 28%,
          #575757 45%,
          #313131 61%,
          #161616 76%,
          #060606 89%,
          #000 100%
        ),
        #f09d00;
      border: 0.808px solid #f09d00;
      border-radius: 8.077px;
      background-blend-mode: color-dodge, normal;
    }

    .btn-more {
      display: flex;
      gap: 3px;
      align-items: center;
      font-size: 16.154px;
      font-weight: 500;
      line-height: normal;
      color: #fff;
      text-align: center;

      .icon-more {
        display: block;
        width: 16px;
        height: 16px;
      }
    }
  }

  .more-promo {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    span {
      @include flex-center;
      min-width: 140px;
      height: 50px;
      padding: 0 10px;
      font-size: 24px;
      font-weight: 700;
      line-height: normal;
      color: #fff;
      text-align: center;
      background: linear-gradient(0deg, #305cff 0%, #a62cff 100%);
      border-radius: 16px;
      box-shadow:
        0 0 15px 0 #702cff,
        0 0 15px 0 #702cff;
    }
  }

  .promo-nav {
    position: absolute;
    top: 50%;
    z-index: 2;
    transform: translateY(-50%);

    .am-icon {
      width: 50px;
      height: 50px;
      fill: none;
    }
  }

  .nav-prev {
    left: 100px;
  }

  .nav-next {
    right: 100px;
  }
}
