import React from "react";
import { withRouter } from "react-router-dom";
import cz from "classnames";
import { inject, observer } from "mobx-react";

import GameTitle from "../GameTitle/GameTitle";

import "./HomePromo.scss";

@inject("common", "tcgCommon", "languageShell")
@withRouter
@observer
class HomePromo extends React.Component {
  get promoList() {
    return this.props.common.activity;
  }
  getImage = (item) => {
    const { announcementImages } = item;
    if (!announcementImages) {
      return null;
    }
    return (announcementImages.find(({ sequence }) => sequence === 1) || {}).url;
  };
  morePromo = (item) => {
    const { id } = item;
    if (id) {
      this.props.history.push(`/m/activity?id=${id}`);
    }
  };
  render() {
    if (this.promoList?.length < 1) {
      return null;
    }
    return (
      <div className={cz("home-promo-list-wrap", this.props.className)}>
        <div className="home-game-title">
          <GameTitle title={this.props.languageShell.t("promotion")} />
          <div className="more-game" onClick={() => this.props.history.push("/m/activity")}>
            <span>{this.props.languageShell.t("see_all")}</span>
          </div>
        </div>
        <div className="home-promo-list hide-scrollbar">
          {this.promoList.map((item, index) => (
            <div className="promo-item" key={`promo_${index}`} onClick={() => this.morePromo(item)}>
              <img className="promo-image" src={this.getImage(item)} alt="" />
            </div>
          ))}
        </div>
      </div>
    );
  }
}

export default HomePromo;
