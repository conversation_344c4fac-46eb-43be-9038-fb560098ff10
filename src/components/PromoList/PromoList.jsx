import React from "react";
import { with<PERSON>outer } from "react-router";
import { Icon } from "antd-mobile";
import { isEqual } from "lodash";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import MC from "tcgmodulemc";

import PromoCountdown from "../PromoCountdown/PromoCountdown";

import "./PromoList.scss";

@inject("common", "languageShell")
@withRouter
@observer
class PromoList extends React.Component {
  componentDidMount() {
    if (this.props.promoList?.length > 0) {
      this.initSwiper();
    }
  }
  componentDidUpdate(prevProps, prevState) {
    if (!isEqual(prevProps.promoList, this.props.promoList)) {
      this.initSwiper();
    }
  }
  componentWillUnmount() {
    this.swiper?.destroy?.();
  }
  initSwiper = () => {
    if (this.swiper) {
      this.swiper?.destroy(true, true);
    }
    this.swiper = new MC.Swiper(this.promoSwiper, {
      observer: true,
      observeParents: true,
      slidesPerView: "auto",
      centeredSlides: true,
      // spaceBetween: remToPx(0.2),
      initialSlide: 1,
    });
  };
  getImage = (item) => {
    const { announcementImages } = item;
    if (!announcementImages) {
      return "";
    }
    return (announcementImages.find(({ sequence }) => sequence === 1) || {}).url;
  };
  morePromo = (item) => {
    this.props.history.push("/m/leaderBoard");
  };
  getContent = (content) => {
    const temp = document.createElement("div");
    temp.style.display = "none";
    temp.innerHTML = content;
    const convert = temp.textContent || temp.innerText;
    temp.remove();
    return convert;
  };
  render() {
    if (this.props.promoList.length < 1) {
      return null;
    }
    return (
      <div className="home-promo-wrap">
        <div className="home-game-title">
          <div className="game-title-content">
            <div className="title-content">
              <img className="title-icon" src={require("@/assets/images/home/<USER>")} alt="" />
              <span>{this.props.languageShell.t("tournament")}</span>
            </div>
            <span className="see-all" onClick={this.morePromo}>
              {this.props.languageShell.t("see_all")}
            </span>
          </div>
        </div>
        <div className="swiper-container  promo-swiper" ref={(c) => (this.promoSwiper = c)}>
          {/* <div className="promo-nav nav-prev" ref={(e) => (this.prevNav = e)}>
            <Icon type={require("!svg-sprite-loader!@/assets/images/common/nav-left.svg")} />
          </div>
          <div className="promo-nav nav-next" ref={(e) => (this.nextNav = e)}>
            <Icon type={require("!svg-sprite-loader!@/assets/images/common/nav-right.svg")} />
          </div> */}
          <div className="promo-swiper-list swiper-wrapper">
            {this.props.promoList.map((item, index) => {
              return (
                <div className="swiper-slide" key={`home_promo_${index}`}>
                  <div className="promo-item">
                    <img className="promo-item-bg" src={this.getImage(item)} alt="" />
                    <div className="item-title">{item.title}</div>
                    <div className="item-content">{this.getContent(item.content)}</div>
                    {item.endDate && <PromoCountdown item={item} />}
                    <div className="promo-links">
                      <div className="btn-join" onClick={this.morePromo}>
                        {this.props.languageShell.t("participate")}
                      </div>
                      <div className="btn-more" onClick={this.morePromo}>
                        <span>{this.props.languageShell.t("in_more_button")}</span>
                        <Icon
                          className="icon-more"
                          type={require("!svg-sprite-loader!@/assets/images/common/circle-more.svg")}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  }
}

PromoList.propTypes = {
  promoList: PropTypes.array.isRequired,
};

PromoList.defaultProps = {
  promoList: [],
};

export default PromoList;
