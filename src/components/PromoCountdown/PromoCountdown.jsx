import React from "react";
import { inject, observer } from "mobx-react";
import propTypes from "prop-types";

import "./PromoCountdown.scss";

const SECOND = 1000;
const MINUTE = SECOND * 60;
const HOUR = MINUTE * 60;
const DAY = HOUR * 24;

@inject("languageShell")
@observer
class PromoCountdown extends React.Component {
  state = {
    day: "00",
    hour: "00",
    minute: "00",
    second: "00",
  };
  componentDidMount() {
    this.startCountDown();
  }
  componentWillUnmount() {
    clearInterval(this.timer);
  }
  startCountDown = () => {
    const { noExpiry } = this.props.item;
    if (noExpiry === 1) {
      return;
    }
    this.timeCountDown();
    this.timer = setInterval(this.timeCountDown, 500);
  };
  leftFillNum = (num) => {
    return String(num).padStart(2, "0");
  };

  timeCountDown = () => {
    const currentTime = new Date().getTime();
    const { endDate } = this.props.item;
    const diffTime = endDate - currentTime;
    this.setState({
      day: Math.floor(diffTime / DAY),
      hour: this.leftFillNum(Math.floor((diffTime % DAY) / HOUR)),
      minute: this.leftFillNum(Math.floor((diffTime % HOUR) / MINUTE)),
      second: this.leftFillNum(Math.floor((diffTime % MINUTE) / SECOND)),
    });
  };
  render() {
    return (
      <div className="promo-count-down">
        <div className="section-count day">
          <div className="count-num">
            <span>{this.state.day}</span>
          </div>
          <div className="unit">
            <span>Days</span>
          </div>
        </div>
        <div className="section-count">
          <div className="count-num">
            <span>{this.state.hour}</span>
          </div>
          <div className="unit">
            <span>Hours</span>
          </div>
        </div>
        <div className="section-count">
          <div className="count-num">
            <span>{this.state.minute}</span>
          </div>
          <div className="unit">
            <span>Minutes</span>
          </div>
        </div>
        <div className="section-count">
          <div className="count-num">
            <span>{this.state.second}</span>
          </div>
          <div className="unit">
            <span>Seconds</span>
          </div>
        </div>
      </div>
    );
  }
}

PromoCountdown.propTypes = {
  endDate: propTypes.number.isRequired,
  item: propTypes.object,
};

export default PromoCountdown;
