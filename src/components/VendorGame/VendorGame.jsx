import React, { Component } from "react";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import GameListBox from "@/components/GameListBox/GameListBox";
import VendorGameItem from "@/components/VendorGameItem/VendorGameItem";
import deploy from "@/config/deploy.config";

import "./VendorGame.scss";

const { gameSelectType } = deploy;

@inject("common", "languageShell", "gameCenter")
@observer
class VendorGame extends Component {
  get vendors() {
    return this.props.gameCenter.gameVendor.mapping[this.props.gameType] || [];
  }
  state = { gameName: "", dataType: gameSelectType.ALL };
  handleChangeType = (dataType) => {
    this.setState({
      dataType,
      gameName: "",
    });
  };
  handleSearchByName = (gameName) => {
    this.setState({
      gameName: gameName?.toLowerCase(),
      dataType: gameSelectType.ALL,
    });
  };
  renderContent = (dataType) => {
    if (dataType === gameSelectType.ALL) {
      return (
        this.state.dataType === gameSelectType.ALL && (
          <div className="vendor-game-scroll">
            <div className={`vendor-game-list`}>
              {this.vendors.map((item, index) => {
                return <VendorGameItem key={`${item.gameType}_${index}`} item={item} />;
              })}
            </div>
          </div>
        )
      );
    }
    return this.state.dataType === dataType && <GameListBox dataType={dataType} gameType={this.props.gameType} />;
  };
  render() {
    return (
      <div className={cz("vendor-game-wrap", this.props.className)}>
        <div className="vendor-game-content">
          <div className={`vendor-game-list ${this.props.gameType}`}>
            {this.vendors.map((item, index) => {
              return <VendorGameItem key={`${item.gameType}_${index}`} item={item} />;
            })}
          </div>
        </div>
      </div>
    );
  }
}

VendorGame.propTypes = {
  className: PropTypes.string,
  gameType: PropTypes.string,
};

VendorGame.defaultProps = {
  gameType: "",
  className: "",
};

export default VendorGame;
