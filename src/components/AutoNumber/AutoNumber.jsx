import React from "react";
import { Icon } from "antd-mobile";
import { floor, random } from "lodash";
import { inject } from "mobx-react";
import { currencyFormat } from "tcg-mobile-common";

import "./AutoNumber.scss";

@inject("languageShell", "tcgCommon")
class AutoNumber extends React.Component {
  get formatAmount() {
    return currencyFormat.format({ value: Math.min(this.state.amount, 80000) });
  }
  get formatNumber() {
    return currencyFormat.format({ value: Math.min(this.state.number, 800000) });
  }
  constructor(props) {
    super(props);
    this.state = {
      amount: floor(random(this.props.minNum, this.props.maxNum, true), this.props.decimal),
      number: floor(random(this.props.minAmount, this.props.maxAmount, true), this.props.decimal),
    };
    this.count = 0;
    this.timeArr1 = [3000, 4000, 5000];
    this.timeArr2 = [3000, 6000, 8000];
  }
  componentDidMount() {
    this.incrementNum();
    this.incrementAmount();
  }
  componentWillUnmount() {
    clearTimeout(this.timer1);
    clearTimeout(this.timer2);
  }
  incrementNum = () => {
    this.timer1 = setTimeout(
      () => {
        this.count = floor(random(5, 20, true), this.props.decimal);
        this.setState((state) => {
          return { amount: state.amount + this.count };
        });
        clearTimeout(this.timer1);
        this.incrementNum();
      },
      this.timeArr1[Math.floor(Math.random() * this.timeArr1.length)]
    );
  };
  incrementAmount = () => {
    this.timer2 = setTimeout(
      () => {
        this.count = floor(random(100, 200, true), this.props.decimal);
        this.setState((state) => {
          return { number: state.number + this.count };
        });
        clearTimeout(this.timer2);
        this.incrementAmount();
      },
      this.timeArr2[Math.floor(Math.random() * this.timeArr2.length)]
    );
  };
  render() {
    return (
      <div className="auto-num-list">
        <div className="num-item">
          <div className="count">
            <span>{this.formatNumber}</span>
          </div>
          <div className="title">
            <Icon type={require("!svg-sprite-loader!@/assets/images/home/<USER>")} />
            <span>{this.props.languageShell.t("winner")}</span>
          </div>
        </div>
        <div className="num-item">
          <div className="count">
            <span>{this.formatAmount}</span>
          </div>
          <div className="title">
            <Icon type={require("!svg-sprite-loader!@/assets/images/home/<USER>")} />
            <span>{this.props.languageShell.t("player")}</span>
          </div>
        </div>
      </div>
    );
  }
}

AutoNumber.defaultProps = {
  maxNum: 35000,
  minNum: 35000,
  maxAmount: 500000,
  minAmount: 500000,
  decimal: 0,
};

export default AutoNumber;
