import React from "react";
import cz from "classnames";
import { isEqual } from "lodash";
import { swiperLoader, VendorIcon } from "tcg-mobile-common";

import { remToPx } from "@/utils/dom";

class VendorIconSwiper extends React.Component {
  constructor(props) {
    super(props);
    this.swiper = null;
    this.swiperRef = null;
    this.paginationRef = null;
  }
  componentDidMount() {
    if (this.props.vendors?.length > 0) {
      this.initSwiper();
    }
  }
  componentDidUpdate(prevProps, prevState) {
    if (!isEqual(prevProps.vendors, this.props.vendors)) {
      this.initSwiper();
    }
  }
  componentWillUnmount() {
    this.swiper?.destroy(true, true);
  }
  async initSwiper() {
    await swiperLoader.loadSwiper11();
    if (this.swiper) {
      this.swiper?.destroy(true, true);
    }
    this.swiper = new window.Swiper(this.swiperRef, {
      observer: true,
      observeParents: true,
      spaceBetween: remToPx(0.25),
      slidesPerView: "auto",
      slidesPerGroup: 3,
      freeMode: true,
      freeModeMomentumRatio: 0.5,
      navigation: {
        nextEl: this.nextNavRef,
        prevEl: this.prevNavRef,
      },
    });
  }
  render() {
    if (this.props.vendors?.length < 1) {
      return null;
    }
    return (
      <div className={cz("vendor-icon-swiper", this.props.className)}>
        <div className="vendor-nav nav-prev" ref={(c) => (this.prevNavRef = c)}>
          <img className="nav-icon" src={require("@/assets/images/common/prev-slide.png")} />
        </div>
        <div className="vendor-nav nav-next" ref={(c) => (this.nextNavRef = c)}>
          <img className="nav-icon" src={require("@/assets/images/common/next-slide.png")} />
        </div>
        <div className="swiper" ref={(c) => (this.swiperRef = c)}>
          <div className="swiper-wrapper">
            {this.props.vendors.map((item, index) => (
              <div key={`vendor_icon_${index}`} className="swiper-slide">
                <VendorIcon className="vendor-icon" folder="RNG_LIST_VENDOR" vendor={item} color="COLOR" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
}

VendorIconSwiper.defaultProps = {
  vendors: [],
};

export default VendorIconSwiper;
