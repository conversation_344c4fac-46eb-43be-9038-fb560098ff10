import React from "react";
import cz from "classnames";
import { inject, observer } from "mobx-react";

import { coverVassalage } from "@/config/game.config";

import VendorIconSwiper from "./VendorIconSwiper";

import "./VendorIconList.scss";

@inject("gameCenter", "common", "languageShell")
@observer
class VendorIconList extends React.Component {
  get allGameVendor() {
    const games = this.props.gameCenter.gameVendor.sorting.reduce((prev, next) => {
      const vendors = next?.vendorNames?.map((item) => coverVassalage(item.vassalage)) || [];
      return [...prev, ...vendors];
    }, []);
    return Array.from(new Set(games));
  }
  get splitVendor() {
    const mid = Math.ceil(this.allGameVendor?.length / 2);
    const vendorsLeft = this.allGameVendor?.slice(0, mid);
    const vendorsRight = this.allGameVendor?.slice(mid);
    return [vendorsLeft, vendorsRight];
  }
  render() {
    const [vendorsLeft, vendorsRight] = this.splitVendor;
    return (
      <div className={cz("vendor-icon-wrap", this.props.className)}>
        <div className="vendor-icon-list">
          <VendorIconSwiper vendors={vendorsLeft} />
          <VendorIconSwiper vendors={vendorsRight} />
        </div>
      </div>
    );
  }
}

VendorIconList.defaultProps = {
  className: "",
};

export default VendorIconList;
