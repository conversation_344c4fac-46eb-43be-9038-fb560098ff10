.vendor-icon-wrap {
  width: 100%;
  padding: 20px;
  background: #6b76a0;

  .vendor-icon-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .vendor-icon-swiper {
    position: relative;
    padding: 0 60px;

    .swiper-slide {
      width: 100px;
    }

    .vendor-icon {
      display: block;
      width: 100px;
      height: 50px;

      .common-vendor-icon {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    .vendor-nav {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    .nav-icon {
      display: block;
      width: 60px;
    }

    .nav-prev {
      left: 0;
    }

    .nav-next {
      right: 0;
    }

    .swiper-button-disabled {
      opacity: 0.5;
    }
  }
}
