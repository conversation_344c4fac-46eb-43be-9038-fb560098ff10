import React from "react";
import { withRouter } from "react-router-dom";
import cz from "classnames";
import { isEqual } from "lodash";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import MC from "tcgmodulemc";

import HotGameItem from "@/components/GameListItem/HotGameItem";
import SearchInput from "@/components/SearchInput/SearchInput";
import { gamePath, navConfig } from "@/config/game.config";
import { remToPx } from "@/utils/dom";

import "./GameMarquee.scss";

@inject("languageShell", "gameCenter", "common")
@withRouter
@observer
class GameMarquee extends React.Component {
  get gameList() {
    return this.props.gameCenter.hotGames.filter((item) => item.gameType === this.props.gameType).slice(0, 5);
  }
  componentDidMount() {
    if (this.props.gameList?.length > 0) {
      this.initSwiper();
    }
  }
  componentDidUpdate(prevProps, prevState) {
    if (!isEqual(prevProps.gameList, this.props.gameList)) {
      this.initSwiper();
    }
  }
  initSwiper = () => {
    if (this.swiper) {
      this.swiper?.destroy(true, true);
    }
    this.swiper = new MC.Swiper(this.gameSwiper, {
      loop: true,
      speed: 2000,
      slidesPerView: "auto",
      spaceBetween: remToPx(0.1),
      freeMode: true,
      autoplay:
        this.props.gameList.length > 5
          ? {
              delay: 0,
              disableOnInteraction: false,
            }
          : false,
    });
  };

  handleSearch = () => {
    this.props.history.push(gamePath.SEARCH);
  };

  render() {
    if (this.props.gameList?.length < 1) {
      return null;
    }
    return (
      <div className={cz("game-marquee-wrap", this.props.className)}>
        <div className="game-marquee-title">
          <div className="title-text">
            <span>{this.props.languageShell.t(navConfig[this.props.gameType]?.name)}</span>
          </div>
          <div className="disable-search" onClick={this.handleSearch}>
            <SearchInput disabled />
          </div>
        </div>

        <div className="game-marquee-content">
          <div className="swiper-container  game-swiper" ref={(c) => (this.gameSwiper = c)}>
            <div className="swiper-wrapper game-marquee-list">
              {this.props.gameList.map((item, index) => {
                return <HotGameItem className="swiper-slide" item={item} key={`game-marquee-${item.nodeId}`} />;
              })}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

GameMarquee.propTypes = {
  className: PropTypes.string,
  gameType: PropTypes.string.isRequired,
  gameList: PropTypes.array.isRequired,
};

GameMarquee.defaultProps = {
  className: "",
  gameType: "",
  gameList: [],
};

export default GameMarquee;
