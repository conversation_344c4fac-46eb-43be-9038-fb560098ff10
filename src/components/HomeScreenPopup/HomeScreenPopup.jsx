import React from "react";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";

import * as navigatorUtil from "@/utils/navigatorUtil";
import * as openWinUtils from "@/utils/openLink";

import add from "./images/add.png";
import click from "./images/click.png";

import "./HomeScreenPopup.scss";

const SHOW_TYPE_APP = 0;
const SHOW_TYPE_PROFILE = 1;
const SHOW_TYPE_PWA = 2;

const keyHideDownloadBar = "hideDownloadBar";

@inject("tcgCommon", "mcLanguage", "common", "languageShell")
@observer
class HomeScreenPopup extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      show: false,
      iosShowType: SHOW_TYPE_APP,
      tipType: SHOW_TYPE_PWA,
    };
    this.isIos = navigatorUtil.isIos();
    const [major, minor] = navigatorUtil.iOSVersion();
    this.showIOSTips = this.isIos && ((major === 12 && minor < 2) || major <= 11);
  }
  componentDidMount() {
    this.setState({ show: this.props.show });
    this.setupLinks();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.downLink !== this.props.downLink && this.props.downLink) {
      this.setupLinks();
    }
    if (prevProps.show !== this.props.show) {
      this.setState({ show: this.props.show }, () => {
        if (this.state.show) {
          if (window.localStorage.getItem(keyHideDownloadBar) === "1") {
            this.props.tcgCommon.showAppWin(false);
          }
        }
      });
    }
  }
  setupLinks() {
    const links = this.props.common.downLink.reduce((acc, curr) => {
      return {
        ...acc,
        [curr.resourceType]: curr.url,
      };
    }, {});
    let hasProfile = !!links.MOBILECONFIG;
    let iosShowType = SHOW_TYPE_APP;
    if (hasProfile) {
      iosShowType = SHOW_TYPE_PROFILE;
      links.MOBILECONFIG = `${location.origin}${links.MOBILECONFIG}`;
    }
    this.setState({ links, iosShowType });
  }
  goDownProfile = () => {
    this.handleAddHomeShow(false)();
    if (this.props.downLink) {
      const findOne = this.state.links["MOBILECONFIG"];
      if (findOne) {
        openWinUtils.handleWindowPage(findOne);
      }
    }
  };
  renderTips() {
    switch (this.state.tipType) {
      case SHOW_TYPE_PWA:
        let icon = `${process.env.PUBLIC_URL}/icons/icon-96x96.png`;
        let obj = localStorage.getItem("templates") || {};
        if (obj) {
          try {
            obj = JSON.parse(obj);
            icon = JSON.parse(obj.parameters.pwa).icon["96x96"];
          } catch (e) {}
        }
        return (
          <div className="home-screen-tips-second">
            {/* <img alt="icon" className="app-icon" src={icon} /> */}
            <div className="home-screen-tips">
              {`${this.props.mcLanguage.t("app_win_click")}`}
              <img className="icon_click" src={click} alt="" />
              {this.props.languageShell.t("app_win_addhome")}
              <img className="icon_add" src={add} alt="" />
            </div>
          </div>
        );
      case SHOW_TYPE_PROFILE:
        return (
          <div className="home-screen-tips-profile">
            <input
              type="button"
              className="btn-primary"
              value={this.props.mcLanguage.t("app_win_download")}
              onClick={this.goDownProfile}
            />
          </div>
        );
      default:
        return null;
    }
  }
  handleAddHomeShow = (show) => () => {
    this.setState(
      {
        show,
        tipType: SHOW_TYPE_PWA,
      },
      () => {
        if (window.localStorage.getItem(keyHideDownloadBar) === "1") {
          this.props.tcgCommon.showAppWin(!this.state.show);
        }
      }
    );
    if (!show) {
      this.props.onHide();
    }
  };
  render() {
    const classnames = cz("footer-home-screen-shell", {
      "footer-home-screen-hide": !this.state.show,
    });
    return (
      <footer className={cz(classnames, this.props.className)}>
        <div className="model-mask" onClick={this.handleAddHomeShow(false)} />
        <div className="add-home-screen" ref={(c) => (this.guideDom = c)}>
          {this.showIOSTips && (
            <div className="home-screen-tips-first">
              <span className="title">{this.props.mcLanguage.t("app_win_tips")}</span>
              <span className="content">{this.props.mcLanguage.t("app_win_legacy_ios")}</span>
            </div>
          )}
          {this.renderTips()}
          <Icon
            className="icon_close"
            type={require("!svg-sprite-loader!@/assets/images/common/btn-close.svg")}
            onClick={this.handleAddHomeShow(false)}
            alt=""
          />
        </div>
      </footer>
    );
  }
}

export default HomeScreenPopup;
