.footer-home-screen-shell {
  position: fixed;
  bottom: 35px !important;
  left: 0;
  z-index: 99999;
  width: 100%;
  padding: 0;
  text-align: center;
  background-color: transparent;
  transition: all 0.3s;

  &.footer-home-screen-hide {
    bottom: -400px !important;

    .model-mask {
      display: none;
    }
  }

  .model-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgb(0 0 0 / 30%);
  }

  .icon {
    width: 44px;
    height: 44px;
    fill: white;

    &__share {
      width: 60px;
      height: 60px;
      margin: 0;
      transform: translateY(10%);
    }

    &__close-button {
      position: absolute;
      top: 0.45em;
      right: 0.45em;
      width: 0.875em;
      height: 0.875em;
    }
  }

  &.agent {
    .add-home-screen {
      background: #7bc8f1;
    }

    .add-home-screen::after {
      border-top: 20px solid #7bc8f1;
    }
  }

  .add-home-screen {
    position: relative;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 595px;
    height: 90px;
    margin: 0 auto;
    background: $main-color;
    border-radius: 15px;

    &::after {
      position: absolute;
      bottom: -18px;
      left: 50%;
      width: 0;
      height: 0;
      content: "";
      border-top: 20px solid $main-color;
      border-right: 20px solid transparent;
      border-left: 20px solid transparent;
      transform: translateX(-50%);
    }

    .icon_close {
      position: absolute;
      top: -15px;
      right: -15px;
      width: 45px;
      height: 45px;
    }
  }

  .app-icon {
    flex: 0 0 128px;
    width: 128px;
    height: 128px;
    vertical-align: middle;
    border-radius: 0.8em;
  }

  .home-screen-tips {
    display: flex;
    align-items: center;
    padding-left: 1em;
    font-size: 24px;
    color: white;
    text-align: left;

    .icon_click {
      display: inline-block;
      width: 28px;
      height: 37px;
      margin: 0 15px;
    }

    .icon_add {
      display: inline-block;
      width: 33px;
      height: 33px;
      margin: 0 15px;
    }

    .label {
      display: inline-block;
      line-height: normal;
      transform: translateY(8%);
    }
  }

  .add-app-icon {
    display: inline-block;
    width: 10px;
    height: 10px;
  }

  .home-screen-tips-second {
    display: flex;
    align-items: center;
  }

  .home-screen-tips-first {
    .title {
      display: block;
      padding-bottom: 20px;
      font-size: 36px;
      color: #fff;

      text-align: left;
    }

    .content {
      display: block;
      padding-bottom: 1em;
      font-size: 24px;
      line-height: 1.5;
      color: #fff;
      text-align: left;
    }

    h4 {
      line-height: 1;
    }
  }

  span {
    line-height: 1;
  }

  .btn-primary {
    height: auto;
    padding: 14px;
    margin: 0 auto;
    font-size: 30px;
  }

  .home-screen-tips-profile {
    width: 100%;
  }
}
