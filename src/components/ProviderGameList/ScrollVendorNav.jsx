import React from "react";
import cz from "classnames";
import { isEqual } from "lodash";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { VendorIcon } from "tcg-mobile-common";

import { scrollTopTo } from "@/utils/scroll";

import "./ScrollVendorNav.scss";

@inject("gameCenter", "languageShell", "common", "auth", "tcgCommon")
@observer
class ScrollVendorNav extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showArrowDown: false,
      showArrowUp: false,
    };
    this.menuGame = {};
  }
  componentDidMount() {
    if (this.props.vendors?.length > 0) {
      this.initVedorScroll();
    }
  }
  componentDidUpdate(prevProps, prevState) {
    if (!isEqual(prevProps.vendors, this.props.vendors)) {
      this.initVedorScroll();
    }
  }
  componentWillUnmount() {
    this.vendorScroll.removeEventListener("scroll", this.vendorScrollEvent);
  }
  initVedorScroll = () => {
    if (this.vendorScroll) {
      this.vendorScroll.addEventListener("scroll", this.vendorScrollEvent);
      let canScroll = this.vendorScroll.scrollHeight > this.vendorScroll.clientHeight;
      this.setState({
        showArrowDown: canScroll,
      });
    }
  };
  vendorScrollEvent = (e) => {
    let canScroll = this.vendorScroll.scrollHeight > this.vendorScroll.clientHeight;
    let isAtTop = this.vendorScroll.scrollTop <= 1;
    let isAtBottom = this.vendorScroll.scrollTop + this.vendorScroll.clientHeight >= this.vendorScroll.scrollHeight - 1;
    this.setState({
      showArrowUp: canScroll && !isAtTop,
      showArrowDown: canScroll && !isAtBottom,
    });
  };
  scrollTopCenter = (index) => {
    const el = this.menuGame[index];
    if (el) {
      const containerHeight = this.vendorScroll.offsetHeight;

      const itemOffsetTop = el.offsetTop;

      const scrollPosition = itemOffsetTop - containerHeight / 2 + el.offsetHeight / 2;

      scrollTopTo(this.vendorScroll, scrollPosition, 300);
    }
  };
  handleNavClick = (vassalage, index) => {
    this.props.onChangeVendor(vassalage);
    this.scrollTopCenter(index);
  };
  render() {
    if (this.props.vendors.length < 1) return null;
    return (
      <div className="vendor-nav-wrap">
        <div className="vendor-nav-scroll hide-scrollbar" ref={(c) => (this.vendorScroll = c)}>
          {this.props.vendors.map((item, index) => {
            return (
              <div
                className={cz("vendor-nav-item", { on: this.props.vassalage === item.vassalage })}
                onClick={() => {
                  this.handleNavClick(item.vassalage, index);
                }}
                ref={(e) => (this.menuGame[index] = e)}
              >
                <div className="vendor-icon-bg">
                  {this.props.vassalage === item.vassalage ? (
                    <VendorIcon className="vendor-nav" folder="RNG_LIST_VENDOR" vendor={item.vassalage} color="WHITE" />
                  ) : (
                    <VendorIcon className="vendor-nav" folder="RNG_LIST_VENDOR" vendor={item.vassalage} color="GRAY" />
                  )}
                </div>
                {/* <div className="vendor-nav-name">
                  <span>{this.props.languageShell.t(item.displayName)}</span>
                </div> */}
              </div>
            );
          })}
        </div>
        {/* {this.state.showArrowDown && (
          <div className="arrow-down">
            <Icon type={require("!svg-sprite-loader!@/assets/images/common/double-arrow-down.svg")} />
          </div>
        )} */}
      </div>
    );
  }
}

ScrollVendorNav.propTypes = {
  vendors: PropTypes.array,
  vassalage: PropTypes.string,
  onChangeVendor: PropTypes.func.isRequired,
};

ScrollVendorNav.defaultProps = {
  vendors: [],
};

export default ScrollVendorNav;
