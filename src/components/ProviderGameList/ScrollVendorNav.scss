.vendor-nav-wrap {
  position: sticky;
  top: var(--header-height);
  flex-shrink: 0;
  width: 136px;
  height: calc(100vh - var(--header-height) - var(--footer-height));
  height: calc(100 * var(--vh, 1vh) - var(--header-height) - var(--footer-height));

  .vendor-nav-scroll {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 0 14px 0 15px;
    overflow-y: scroll;
    overscroll-behavior: none;
  }

  .arrow-up,
  .arrow-down {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    color: #f9fd4e;

    .am-icon {
      width: 24px;
      height: 24px;
    }
  }

  .arrow-up {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
  }

  .arrow-down {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 10;
  }

  .vendor-nav-item {
    @include flex-center;
    position: relative;
    flex-direction: column;
    width: 100%;
    min-width: 0;
    height: 76px;
    font-size: 20px;
    line-height: normal;
    color: #88f0bf;
    text-align: center;
    border-radius: 15px;

    &::before {
      position: absolute;
      top: -3.84px;
      left: 15.381px;
      width: 76.9px;
      height: 2px;
      content: "";
      background: linear-gradient(
        90deg,
        rgb(212 228 255 / 0%) 0%,
        #d4e4ff 30.18%,
        #d4e4ff 70.34%,
        rgb(212 228 255 / 0%) 100%
      );
    }

    &.on {
      background: #6297ff;
    }

    &:not(:last-child) {
      margin-bottom: 8px;
    }
  }

  .vendor-icon-bg {
    position: relative;

    .common-vendor-icon {
      display: block;
      width: 80px;
      height: 40px;
    }
  }

  .vendor-icon-inner {
    @include flex-center;
    position: relative;
    z-index: 2;
  }

  .vendor-nav-icon {
    display: block;
    width: 50px;
    height: 50px;
    object-fit: contain;
  }

  .vendor-nav-name {
    @include flex-center;
    margin-top: 8px;
    word-break: break-all;
  }
}
