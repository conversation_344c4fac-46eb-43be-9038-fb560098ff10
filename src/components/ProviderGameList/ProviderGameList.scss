// .download-bar.show-bar + .home-container {
//   .vendor-nav-wrap {
//     top: calc(345px + var(--download-bar-height));
//     .game-menu-scroll {
//       max-height: calc(100vh - var(--header-height) - var(--footer-height) - var(--download-bar-height) - 222px);
//       max-height: calc(
//         100 * var(--vh, 1vh) - var(--header-height) - var(--footer-height) - var(--download-bar-height) - 222px
//       );
//     }
//   }
// }

.provider-game-list-wrap {
  width: 100%;

  .provider-game-list-content {
    display: flex;
    align-items: flex-start;

    &.vertical {
      padding: 32px;
    }

    &.horizontal {
      padding: 10px 19px;
    }
  }

  .search-game-wrap {
    display: flex;
    align-items: center;
  }

  .current-filter {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;

    .filter-item {
      display: flex;
      gap: 10px;
      align-items: center;
      height: 60px;
      padding: 0 16px;
      font-size: 28px;
      line-height: normal;
      color: #8d9aa5;
      background: #222424;
      border-radius: 6px;
    }

    .btn-close {
      display: block;
      width: 20px;
      height: 20px;
    }
  }

  .game-filter-wrap {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
    padding: 0 32px;
  }

  .provider-game-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 96px;
    margin-bottom: 24px;
  }

  .game-search-section {
    display: flex;
    gap: 24px;
    align-items: center;
  }

  .btn-filter {
    @include flex-center;
    position: relative;
    flex-shrink: 0;
    height: 72px;
    padding: 0 24px;
    margin-left: 25px;
    font-size: 32px;
    font-weight: 600;
    line-height: 48px;
    color: #fff;
    text-align: center;
    text-transform: capitalize;
    border: 2px solid rgb(255 255 255 / 23%);
    border-radius: 16px;

    .am-icon {
      display: block;
      width: 40px;
      height: 40px;
      margin-right: 16px;
    }

    .filter-count {
      @include flex-center;
      position: absolute;
      top: -20px;
      right: -20px;
      min-width: 40px;
      height: 40px;
      padding: 0 12px;
      font-size: 20px;
      line-height: 1;
      color: #fff;
      background-color: rgb(0 180 216 / 100%);
      border-radius: 20px;
    }
  }

  .btn-clear {
    @include flex-center;
    width: 72px;
    height: 72px;
    color: #fff;
    border: 2px solid rgb(255 255 255 / 23%);
    border-radius: 16px;

    .am-icon {
      display: block;
      width: 30px;
      height: 30px;
    }
  }

  .provider-btn {
    flex-shrink: 0;
    width: 188px;
    height: 60px;
    padding: 13px 12px;
    margin-left: 20px;
    font-size: 20px;
    font-weight: 600;
    color: #76797d;
    background: #fff;
    border: 2px solid #d9e1ec;
    border-radius: 71px;

    @include flex-center;

    &.on {
      background: linear-gradient(90deg, #97d2fe 0%, #2a87f1 100%);
      border-radius: 70px;
    }

    .icon-provider {
      width: 34px;
      height: 34px;
      margin-right: 8px;
    }

    .default-vendor {
      display: flex;
      align-items: center;
    }

    .arrow-down {
      flex-shrink: 0;
      width: 24px;
      height: 24px;
      margin-left: 8px;
    }

    .close-select {
      width: 24px;
      height: 24px;
      margin-left: 10px;
    }
  }

  .category-games {
    .category-game-list {
      @include game-list;
    }
  }

  .game-line {
    position: relative;
    width: 100%;
    padding: 0 20px;
    margin: 24px 0;

    &::after {
      display: block;
      width: 100%;
      height: 2px;
      content: "";
      background: rgb(255 255 255 / 10%);
    }
  }

  .game-list-filter-wrap {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 20px;
  }

  .select-bar {
    display: flex;
    flex-shrink: 0;
    gap: 16px;
    align-items: center;
    width: 100%;
    overflow: hidden;
  }

  .select-btn {
    @include flex-center;
    position: relative;
    flex-shrink: 0;
    width: 56px;
    height: 56px;
    font-size: 24px;
    font-weight: 400;
    line-height: normal;
    background: var(--bg-color-menu-default);
    border-radius: 8px;

    &.on {
      background: var(--bg-color-menu-active);
    }

    .am-icon {
      display: block;
      width: 32px;
      height: 32px;
    }
  }

  .game-pagination {
    @include flex-center;
    gap: 20px;
    width: 100%;
    margin-top: 20px;
  }

  .game-list-content {
    width: 100%;
    overflow: hidden;
  }

  .game-list-layout {
    flex: 1;
    height: 100%;
    overflow: hidden;
  }

  .game-right-content {
    width: calc(100% - 114px);
    padding: 20px 26px 20px 10px;
    overflow: hidden;

    .game-select {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      width: 100%;
      margin-bottom: 20px;
    }
  }
}
