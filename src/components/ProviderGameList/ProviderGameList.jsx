import React from "react";
import { with<PERSON><PERSON>er } from "react-router";
import { Icon, Toast } from "antd-mobile";
import cz from "classnames";
import { isEmpty } from "lodash";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import Qs from "qs";

import GameListItem from "@/components/GameListItem/GameListItem";
import Nodata from "@/components/NoData/NoData";
import SearchInput from "@/components/SearchInput/SearchInput";
import Skeleton from "@/components/Skeleton/Skeleton";
import VendorMenu from "@/components/VendorMenu/VendorMenuSwiper";
import deploy from "@/config/deploy.config";
import { navConfig } from "@/config/game.config";
import List from "@/ui/List";

import ScrollVendorNav from "./ScrollVendorNav";

import "./ProviderGameList.scss";

const PAGE_SIZE = 36;
const { gameSelectType } = deploy;

@inject("gameCenter", "languageShell", "common", "auth", "tcgCommon")
@withRouter
@observer
class ProviderGameList extends React.Component {
  get currentVendors() {
    const data = this.props.gameCenter.gameVendor.mapping[this.props.gameType] || [];
    // if (this.classifyVendors?.length > 0) {
    //   return data.filter((item) => this.classifyVendors.includes(item.vassalage));
    // }
    return data;
  }
  get classifyVendors() {
    const data = this.props.tcgCommon.gameVendor.mapping[this.state.gameType]?.gameClassify || [];
    const item = data.find((item) => item.key === this.state.gameClassify);
    return item?.vendors || [];
  }
  get activeGameList() {
    if (this.state.dataType === gameSelectType.FAV) {
      return this.props.gameCenter.favGameList.content;
    } else if (this.state.dataType === gameSelectType.HOT) {
      if (["INHOUSE"].includes(this.props.gameType)) {
        return this.props.gameCenter.hotGames.filter(
          (item) => item.gameType === this.state.gameType && deploy.inHouse.includes(item.vassalage)
        );
      }
      if (this.state.gameType && this.state.vassalage) {
        return this.props.gameCenter.hotGames.filter(
          (item) => item.gameType === this.state.gameType && item.vassalage === this.state.vassalage
        );
      }
      return this.state.gameType
        ? this.props.gameCenter.hotGames.filter((item) => item.gameType === this.state.gameType)
        : this.props.gameCenter.hotGames;
    }
    return this.state.gameList;
  }
  get displayName() {
    const game = this.currentVendors.find((item) => item.vassalage === this.state.vassalage);
    return game?.displayName || this.props.languageShell.t("in_type_all");
  }
  get classifyName() {
    if (navConfig[this.state.gameClassify]) {
      return this.props.languageShell.t(navConfig[this.state.gameClassify]?.name);
    }
    return this.props.languageShell.t(this.state.gameClassify);
  }
  get gameCount() {
    return this.props.tcgCommon.gameVendor.mapping[this.state.gameType]?.gameCount;
  }
  get gameTotalCount() {
    if (this.state.dataType === gameSelectType.HOT) {
      return this.activeGameList?.length;
    }
    if ([gameSelectType.FAV, gameSelectType.RECENT].includes(this.state.dataType)) {
      return 0;
    }
    return this.state.totalCount;
  }
  get activeVendorIcon() {
    return `${this.props.common.vendorIconCDN}${this.state.vassalage}-WHITE.png`;
  }
  get sportVendor() {
    return (this.props.gameCenter.gameVendor.mapping["SPORTS"] || []).map((item) => item.vassalage).join(",");
  }
  get hasFilter() {
    return ![gameSelectType.SEARCH, gameSelectType.FAV, gameSelectType.HOT, gameSelectType.RECENT].includes(
      this.props.dataType
    );
  }
  get hasVendorSelect() {
    return !["BINGO", "JL", "INHOUSE", "RTG", "JACKPOT", "NEW"].includes(this.props.gameType) && this.hasFilter;
  }
  get mapping() {
    return this.props.tcgCommon.gameVendor.mapping;
  }
  get filterCount() {
    const data = [this.state.vassalage || null, this.state.gameClassify || null];
    return data.filter(Boolean).length;
  }
  constructor(props) {
    super(props);
    this.state = {
      gameList: [],
      gameType: props.gameType,
      vassalage: props.vassalage,
      gameClassify: "",
      minBet: "",
      gameName: "",
      dataType: props.dataType,
      currentPage: 1,
      totalPages: 1,
      hasMore: true,
      totalCount: 0,
      skeleton: false,
      loading: false,
      noData: false,
      filterType: "",
      openFilter: false,
      showGameClassify: false,
      showSearch: false,
      vendorLayout: "vertical", // horizontal
    };
    this.inputRef = null;
  }
  componentDidMount() {
    this.initGame();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.vassalage !== this.props.vassalage) {
      this.initGame();
    }
    if (this.props.dataType && prevProps.dataType !== this.props.dataType) {
      this.initGame();
    }
    if (prevProps.gameName !== this.props.gameName) {
      this.handleSearchByName(this.props.gameName);
    }
  }
  initGame() {
    if (this.props.gameName) {
      return this.handleSearchByName(this.props.gameName);
    }

    if (this.props.gameType === "BINGO") {
      return this.filterClick({
        gameType: "RNG",
        gameClassify: "BG",
      });
    }

    if (this.props.gameType === "INHOUSE") {
      return this.filterClick({
        gameType: "RNG",
        vassalage: deploy.inHouse.join(","),
      });
    }
    if (this.props.gameType === gameSelectType.NEW) {
      return this.filterClick({
        gameType: "",
        gameClassify: gameSelectType.NEW,
      });
    }
    if (this.props.gameType === "JACKPOT") {
      return this.filterClick({
        gameType: "RNG",
        gameClassify: "JP",
      });
    }
    if (this.props.gameType === "ESPORTS") {
      return this.filterClick({
        gameType: "SPORTS",
        vassalage: this.currentVendors[0]?.vassalage,
      });
    }
    if (this.props.gameType === "COCKFIGHT") {
      return this.filterClick({
        gameType: "SPORTS",
        vassalage: this.currentVendors[0]?.vassalage,
      });
    }
    if ([gameSelectType.ALL, gameSelectType.HOT].includes(this.props.gameType)) {
      return this.filterClick({
        gameType: "",
      });
    }
    if ([gameSelectType.HOT, gameSelectType.FAV, gameSelectType.RECENT].includes(this.props.dataType)) {
      return this.filterClick({
        gameType: "",
        dataType: this.props.dataType,
        gameClassify: this.props.dataType,
      });
    }
    return this.filterClick({
      gameType: this.props.gameType,
      dataType: this.props.dataType,
      vassalage: this.props.vassalage,
      gameClassify: this.props.gameClassify,
    });
  }
  getRecentGame = (refresh, loading) => {
    if (!this.props.auth.currentToken) {
      return this.props.history.push("/m/login");
    }
    if (loading) {
      this.setState({
        skeleton: true,
      });
    }
    return this.props.gameCenter
      .getRecentGameList({
        page: this.state.currentPage || 1,
        pageSize: this.props.pageSize || PAGE_SIZE,
        gameType: this.state.gameType,
        vendor: this.state.vassalage,
        language: this.props.languageShell.language,
        clientType: 2,
      })
      .then((res) => {
        Toast.hide();
        if (refresh) {
          this.setState({
            gameList: res.list,
            totalPages: res.totalPages,
          });
        } else {
          this.setState((state) => ({
            gameList: state.gameList.concat(res.list),
            totalPages: res.totalPages,
          }));
        }
        const hasMore = this.state.currentPage < res.totalPages;
        this.setState({ hasMore, skeleton: false, noData: this.activeGameList?.length < 1 });
      })
      .finally(() => {
        this.setState({
          skeleton: false,
          loading: false,
        });
      });
  };

  getFavGameList = (refresh, loading = false) => {
    if (!this.props.auth.currentToken) {
      return this.props.history.push("/m/login");
    }
    if (loading) {
      this.setState({
        skeleton: true,
      });
    }
    return this.props.gameCenter
      .getFavGameList(
        {
          pageNo: this.state.currentPage,
          gameType: this.state.gameType,
          vendor: this.state.vassalage,
          pageSize: this.props.pageSize || PAGE_SIZE,
          language: this.props.languageShell.currentLanguage,
        },
        !refresh
      )
      .then((res) => {
        const { totalPages, pageNum } = res;
        const hasMore = pageNum < totalPages;
        this.setState({ hasMore, totalPages, skeleton: false, noData: this.activeGameList?.length < 1 });
      })
      .finally(() => {
        this.setState({
          skeleton: false,
          loading: false,
        });
      });
  };
  getGameList = (refresh, loading = false) => {
    if (!this.state.hasMore || loading) {
      this.setState({
        skeleton: true,
      });
    }
    return this.props.gameCenter
      .getGameList(this.getQuery())
      .then((res) => {
        if (refresh) {
          this.setState({
            gameList: res.games,
            currentPage: res.pageNum,
          });
        } else {
          this.setState((state) => ({
            gameList: state.gameList.concat(res.games),
            currentPage: res.pageNum,
          }));
        }
        const { totalPages, pageNum, totalCount } = res;
        const hasMore = pageNum < totalPages;
        this.setState({ hasMore, totalCount: totalCount, totalPages, noData: this.activeGameList?.length < 1 });
      })
      .finally(() => {
        this.setState({
          skeleton: false,
          loading: false,
        });
      });
  };
  getQuery(state = this.state) {
    return {
      merchant: deploy.merchant,
      platform: "html5",
      gameName: state.gameName,
      gameType: state.gameType,
      pageNo: state.currentPage,
      pageSize: this.props.pageSize || PAGE_SIZE,
      vassalage: state.vassalage,
      gameClassify: [gameSelectType.NEW, gameSelectType.ALL].includes(state.gameClassify)
        ? ""
        : this.state.gameClassify,
      minBet: state.minBet,
      minLine: state.minLine,
      isNew: state.gameClassify === gameSelectType.NEW ? "1" : "",
      language: this.props.languageShell.language,
    };
  }
  loadMore = (retry) => {
    return new Promise((resolve, reject) => {
      this.setState(
        {
          currentPage: retry ? this.state.currentPage : this.state.currentPage + 1,
          loading: true,
        },
        () => {
          this.getData(false).then(resolve).catch(reject);
        }
      );
    });
  };
  pageChange = (page) => {
    this.setState(
      {
        currentPage: page,
      },
      () => {
        this.getData(true, true);
      }
    );
  };
  getData = (refresh, loading) => {
    switch (this.state.dataType) {
      case gameSelectType.FAV:
        return this.getFavGameList(refresh, loading);
      case gameSelectType.RECENT:
        return this.getRecentGame(refresh, loading);
      case gameSelectType.HOT:
        this.setState({
          skeleton: false,
          loading: false,
        });
        return true;
      default:
        return this.getGameList(refresh, loading);
    }
  };
  handleSearchByName = (name) => {
    this.filterClick({
      gameName: name,
      dataType: gameSelectType.ALL,
      minBet: "",
      minLine: "",
    });
  };
  handleSelectVassalage = (vassalage) => {
    this.filterClick({
      vassalage,
      dataType: gameSelectType.ALL,
      gameClassify: "",
      minBet: "",
      minLine: "",
    });
  };
  handleChangeType = (dataType) => {
    this.filterClick({ dataType, gameClassify: "" });
  };
  handleChangeClassity = (item) => {
    const { gameClassify } = item;
    this.filterClick({ gameClassify, dataType: gameSelectType.ALL });
  };
  changeFilter = (data) => {
    this.filterClick({
      gameName: "",
      vassalage: data.vassalage,
      gameClassify: data.gameClassify,
      dataType: gameSelectType.ALL,
    });
  };
  clearFilter = () => {
    const url = Qs.parse(window.location.search, {
      ignoreQueryPrefix: true,
    });
    if (!isEmpty(url)) {
      return this.props.history.push(location.pathname);
    }
    this.filterClick({
      vassalage: "",
      gameClassify: "",
      minBet: "",
      minLine: "",
      dataType: gameSelectType.ALL,
    });
  };
  filterClick = (item = {}) => {
    const {
      vassalage = this.state.vassalage,
      gameType = this.state.gameType,
      dataType = this.state.dataType,
      gameClassify = this.state.gameClassify,
      minBet = "",
      minLine = "",
      gameName = this.state.gameName,
    } = item;
    let type = dataType;
    if ([gameSelectType.HOT, gameSelectType.FAV, gameSelectType.RECENT].includes(gameClassify)) {
      type = gameClassify;
    }
    if ([gameSelectType.FAV, gameSelectType.RECENT].includes(gameClassify) && !this.props.auth.currentToken) {
      return this.props.history.push("/m/login");
    }
    this.setState(
      {
        gameType,
        vassalage,
        gameClassify,
        gameList: [],
        dataType: type,
        currentPage: 1,
        totalPages: 1,
        minBet,
        minLine,
        gameName,
        hasMore: false,
        noData: false,
      },
      () => {
        this.getData(true, true);
      }
    );
  };

  toggleFilter = () => {
    this.setState({
      openFilter: !this.state.openFilter,
    });
  };

  toggleClassify = () => {
    this.setState({
      showGameClassify: !this.state.showGameClassify,
    });
  };

  toggleProvider = () => {
    this.props.common.showProvider(!this.props.common.providerStatus);
  };

  handleCleanVassalage = (e) => {
    e.stopPropagation();
    this.handleSelectVassalage("");
  };

  handleCleanClassify = () => {
    this.handleChangeClassity({ gameClassify: "" });
  };

  toggleSearch = () => {
    this.setState(
      {
        showSearch: !this.state.showSearch,
      },
      () => {
        if (this.state.showSearch) {
          this.inputRef?.focus();
        }
      }
    );
  };

  setInputRef = (e) => {
    this.inputRef = e;
  };

  toggleLayout = () => {
    this.setState({
      vendorLayout: this.state.vendorLayout === "vertical" ? "horizontal" : "vertical",
    });
  };
  renderCurrentFilter = () => {
    if ((this.state.vassalage || this.state.gameClassify) && this.hasVendorSelect) {
      return (
        <div className="current-filter">
          {this.state.vassalage && (
            <div className="filter-item">
              <span>{this.displayName}</span>
              <Icon
                className="btn-close"
                type={require("!svg-sprite-loader!@/assets/images/common/icon-close.svg")}
                onClick={this.handleCleanVassalage}
              />
            </div>
          )}
          {this.state.gameClassify && (
            <div className="filter-item">
              <span>{this.classifyName}</span>
              <Icon
                className="btn-close"
                type={require("!svg-sprite-loader!@/assets/images/common/icon-close.svg")}
                onClick={this.handleCleanClassify}
              />
            </div>
          )}
        </div>
      );
    }
    return null;
  };

  renderGameFilter = () => {
    if (!this.hasVendorSelect) return null;
    return (
      <div className="game-filter-box">
        <div className="game-search-box">
          <SearchInput
            searchGame={this.handleSearchByName}
            gameName={this.state.gameName}
            setRef={this.setInputRef}
            close={this.toggleSearch}
          />
          {/* <div className="toggle-layout" onClick={this.toggleLayout}>
            {this.state.vendorLayout === "vertical" ? (
              <Icon type={require("!svg-sprite-loader!@/assets/images/game/vertical.svg")} />
            ) : (
              <Icon type={require("!svg-sprite-loader!@/assets/images/game/horizontal.svg")} />
            )}
          </div> */}
        </div>
        {this.state.vendorLayout === "horizontal" && (
          <VendorMenu
            gameType={this.props.gameType}
            vassalage={this.state.vassalage}
            vendors={this.currentVendors}
            handleSelect={this.handleSelectVassalage}
          />
        )}
      </div>
    );
  };

  renderScrollNav = () => {
    if (!this.hasVendorSelect) return null;
    if (this.state.vendorLayout === "vertical")
      return (
        <ScrollVendorNav
          vendors={this.currentVendors}
          vassalage={this.state.vassalage}
          onChangeVendor={this.handleSelectVassalage}
        />
      );
    return null;
  };

  renderSelectBar = () => {
    if (!this.hasVendorSelect) return null;
    return (
      <div className={`select-bar select-${this.props.gameType}`}>
        {/* <div
          className={cz("select-btn btn-all", { on: this.state.dataType === gameSelectType.ALL })}
          onClick={() => this.handleChangeType(gameSelectType.ALL)}
        >
          <span>{this.props.languageShell.t("in_type_all")}</span>
          <Icon
            className="search-icon"
            type={require("!svg-sprite-loader!@/assets/images/common/search-icon-all.svg")}
          />
        </div> */}
        <div
          className={cz("select-btn hot", { on: this.state.dataType === gameSelectType.HOT })}
          onClick={() => this.handleChangeType(gameSelectType.HOT)}
        >
          {/* <span>{this.props.languageShell.t("hot")}</span> */}
          <Icon
            className="search-icon"
            type={require("!svg-sprite-loader!@/assets/images/common/search-icon-hot.svg")}
          />
        </div>
        <div
          className={cz("select-btn fav", { on: this.state.dataType === gameSelectType.FAV })}
          onClick={() => this.handleChangeType(gameSelectType.FAV)}
        >
          {/* <span>{this.props.languageShell.t("in_favorite")}</span> */}
          <Icon
            className="search-icon"
            type={require("!svg-sprite-loader!@/assets/images/common/search-icon-fav.svg")}
          />
        </div>
        <div
          className={cz("select-btn recent", { on: this.state.dataType === gameSelectType.RECENT })}
          onClick={() => this.handleChangeType(gameSelectType.RECENT)}
        >
          {/* <span>{this.props.languageShell.t("recent")}</span> */}
          <Icon
            className="search-icon"
            type={require("!svg-sprite-loader!@/assets/images/common/search-icon-recent.svg")}
          />
        </div>
      </div>
    );
  };

  render() {
    const { gameType, dataType } = this.props;
    const { gameClassify, vassalage } = this.state;
    return (
      <div className={cz("provider-game-list-wrap", this.props.className)} ref={(e) => (this.providerWrap = e)}>
        {/* <GameFilter
          gameType={this.props.gameType}
          vassalage={this.state.vassalage}
          vendors={this.currentVendors}
          open={this.state.openFilter}
          close={this.toggleFilter}
          filterGame={this.changeFilter}
          filterType={this.state.filterType}
        /> */}
        {/* <ProviderPopup
          vassalage={this.state.vassalage}
          vendors={this.currentVendors}
          handleSelect={this.handleSelectVassalage}
        />
        <GameFilterPop
          show={this.state.openFilter}
          gameType={this.state.gameType}
          vassalage={this.state.vassalage}
          gameName={this.state.gameName}
          gameClassify={this.state.gameClassify}
          handleSelect={this.changeFilter}
          handleClose={this.toggleFilter}
        /> */}
        {/* {this.hasFilter && (
          <div className="game-filter-wrap">
            <div className={cz("provider-btn", { on: !!this.state.vassalage })} onClick={this.toggleProvider}>
              {this.state.vassalage ? (
                <div className="active-vendor">
                  <img className="active-vendor-icon" src={this.activeVendorIcon} />
                  <Icon
                    className="close-select"
                    type={require("!svg-sprite-loader!@/assets/images/common/icon-close.svg")}
                    onClick={(e) => this.handleCleanVassalage(e)}
                  />
                </div>
              ) : (
                <div className="default-vendor">
                  <Icon
                    className="icon-provider"
                    type={require("!svg-sprite-loader!@/assets/images/common/icon-provider.svg")}
                  />
                  <span>{this.props.languageShell.t("providers")}</span>
                  <Icon
                    className="arrow-down"
                    type={require("!svg-sprite-loader!@/assets/images/common/arrow-down.svg")}
                  />
                </div>
              )}
            </div>
            <VendorSelect
              gameType={gameType}
              vassalage={this.state.vassalage}
              vendors={this.currentVendors}
              handleSelect={this.handleSelectVassalage}
            />
          </div>
        )} */}
        <div className="game-filter-wrap">
          <SearchInput
            searchGame={this.handleSearchByName}
            gameName={this.state.gameName}
            setRef={this.setInputRef}
            close={this.toggleSearch}
          />
          {this.renderSelectBar()}
          {this.hasVendorSelect && (
            <VendorMenu
              gameType={gameType}
              vassalage={this.state.vassalage}
              vendors={this.currentVendors}
              handleSelect={this.handleSelectVassalage}
            />
          )}
        </div>

        <div className={cz("provider-game-list-content", this.state.vendorLayout)}>
          {/* {this.hasVendorSelect && (
            <div className="game-filter-wrap">
              <GameClassifyMenu
              gameType={this.state.gameType}
              vassalage={this.state.vassalage}
              gameClassify={gameClassify}
              menuClick={this.handleChangeClassity}
            />
            </div>
          )} */}

          <div className="game-list-content">
            <div className="category-games">
              {this.state.skeleton ? (
                <Skeleton size={PAGE_SIZE} />
              ) : (
                // <div className="category-game-list">
                //   {this.activeGameList.map((item, index) => {
                //     return <GameListItem key={`${gameType}_${item.nodeId}`} item={item} />;
                //   })}
                // </div>
                <List
                  finished={!this.state.hasMore}
                  onLoad={this.loadMore}
                  errorText={this.props.languageShell.t("load_failed_tip")}
                >
                  <div className="game-list-wrapper">
                    <div className="category-game-list">
                      {this.activeGameList.map((item, index) => {
                        return <GameListItem key={`${gameType}_${item.nodeId}`} item={item} />;
                      })}
                    </div>
                  </div>
                </List>
              )}

              {this.state.noData && <Nodata />}

              {/* {this.state.hasMore && (
                <GameMoreBtn onClick={this.loadMore} loading={this.state.loading} hasMore={this.state.hasMore} />
              )} */}
              {/* <div className="game-pagination">
                <GameProgress count={this.activeGameList.length} totalCount={this.gameTotalCount} />
              </div> */}
            </div>

            {/* <div className="game-pagination">
              <Pagination
                totalPages={this.state.totalPages}
                currentPage={this.state.currentPage}
                onChange={this.pageChange}
              />
            </div> */}
          </div>
          {/* <LoadMoreBtn
            totalCount={this.gameTotalCount}
            hasMore={this.state.hasMore}
            count={this.activeGameList.length}
            loadMore={this.loadMore}
          /> */}
        </div>
      </div>
    );
  }
}

ProviderGameList.propTypes = {
  className: PropTypes.string,
  gameType: PropTypes.string.isRequired,
  vassalage: PropTypes.string,
  gameClassify: PropTypes.string,
  dataType: PropTypes.string,
  hasTitle: PropTypes.bool,
};

ProviderGameList.defaultProps = {
  className: "",
  gameType: "", // RNG LIVE...
  vassalage: "",
  gameClassify: "",
  dataType: gameSelectType.ALL,
  hasTitle: false,
};

export default ProviderGameList;
