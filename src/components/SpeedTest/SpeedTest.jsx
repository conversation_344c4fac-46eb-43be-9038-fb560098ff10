import React from "react";
import Axios from "axios";
import cz from "classnames";
import { random } from "lodash";
import { inject, observer } from "mobx-react";
import { domainUtil } from "tcg-mobile-common";

import "./SpeedTest.scss";

@inject("common", "languageShell")
@observer
class SpeedTest extends React.Component {
  get domainRoute() {
    return this.props.common.domainRoute;
  }
  get activeServer() {
    return this.state.domainList[this.state.activeIndex];
  }
  constructor(props) {
    super(props);
    this.state = { domainList: [], activeIndex: 0, showPopup: false };
  }
  componentDidMount() {
    this.getDomainRoute();
  }
  getDomainRoute = () => {
    this.props.common.getDomainRoute().then(() => {
      this.getDurationTime(this.domainRoute);
    });
  };
  getTime = () => {
    return window.performance ? window.performance.now() : new Date().getTime();
  };
  getDurationTime(speedList) {
    const domainMap = speedList.map((item, idx) => ({
      idx,
      speed: 0,
      domainName: item.domainName,
      description: 0,
      percentage: null,
      test: false,
      server: `server${idx + 1}`,
    }));

    for (let index = 0, size = speedList.length; index < size; ++index) {
      const ele = speedList[index];

      let speed = 9999;
      this.getAxios(ele).then((res) => {
        if (res && res.status === 200) {
          if (res.elapsed >= 400) {
            speed = random(100, 150);
          } else if (res.elapsed >= 200) {
            speed = random(60, 100);
          } else if (res.elapsed >= 100) {
            speed = random(40, 60);
          } else {
            speed = random(20, 40);
          }
        }
        const description = Math.min(speed, 9999);

        let type = "";
        let percentage = 0;
        if (description <= 80) {
          type = "fast";
          percentage = 1;
        } else if (description <= 150) {
          type = "normal";
          percentage = 0.8;
        } else if (description === 9999) {
          type = "slow";
          percentage = 0;
        } else {
          type = "slow";
          percentage = 0.6;
        }

        const target = domainMap.find(({ idx }) => idx === index);
        target["speed"] = speed;
        target["description"] = description;
        target["percentage"] = percentage;
        target["test"] = true;
        target["type"] = type;

        domainMap.sort((a, b) => {
          // 未測速域名放最後
          const aa = a.test ? a : 99999999;
          const bb = b.test ? b : 99999999;
          return aa.speed - bb.speed;
        });
        let currentIndex = domainMap.findIndex((item) => {
          return item.domainName.includes(domainUtil.getCleanDomain(window.location.hostname));
        });
        currentIndex = currentIndex > -1 ? currentIndex : 0;
        this.setState({
          domainList: domainMap,
          activeIndex: currentIndex,
        });
      });
    }
    return true;
  }
  getAxios = (item) => {
    let basicDomain = item.domainName;
    const protocol = basicDomain.startsWith("https") ? "https" : "http";
    const startUrl = basicDomain.replace(/(https|http|:\/\/)/gi, "").replace(/\/.*/gi, "");

    let startTime = this.getTime();
    return Axios.get(`${protocol}://${startUrl}/wps/system/test/ping?speed=${Math.random()}`)
      .then((v) => {
        return { ...v, elapsed: (this.getTime() - startTime) | 0 };
      })
      .catch((e) => e);
  };
  togglePopup = () => {
    this.setState({
      showPopup: !this.state.showPopup,
    });
    this.props.common.showLeftSide(false);
  };
  handleSelect = (domainName) => {
    const url = this.activeServer?.domainName;
    if (url !== domainName) {
      window.location.href = domainName;
    }
  };
  render() {
    if (!this.state.domainList.length) {
      return null;
    }
    return (
      <div className="speed-test-wrap">
        {/* <div className="speed-info">
          <div className="left-info">
            <div className="speed-icon" />
            <span>{this.activeServer?.server}</span>
            {this.activeServer?.speed && <div className="speed-ms">{this.activeServer?.speed}MS</div>}
          </div>
          <div className="active-server">
            <span>{this.activeServer?.server}</span>
          </div>
        </div> */}
        <div className="speed-title">{this.props.languageShell.t("high_speed")}</div>
        <div className="speed-content">
          <div className="speed-list">
            {this.state.domainList.map((item, index) => {
              return (
                <div
                  key={`server_${index}`}
                  onClick={() => this.handleSelect(item.domainName, index)}
                  className={cz("speed-item", { on: this.state.activeIndex === index })}
                >
                  <div className="speed-info">
                    {/* <div className="speed-name">
                      <div className="speed-icon" />
                      <span>{item.domainName}</span>
                      <span>{item.server}</span>
                    </div> */}
                    <div className="speed-line">
                      <div className="line-num">
                        <span>LINK</span>
                        {String(index + 1).padStart(2, "0")}
                      </div>
                      <div className="come-now">{this.props.languageShell.t("come_in_now")}</div>
                    </div>
                    <div className="speed-ms">{item.speed}ms</div>
                  </div>

                  {/* <div className={cz("check-icon", { on: this.state.activeIndex === index })}>
                    {this.state.activeIndex === index && (
                      <Icon
                        className="correct"
                        type={require("!svg-sprite-loader!@/assets/images/common/icon-correct.svg")}
                      />
                    )}
                  </div> */}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  }
}

export default SpeedTest;
