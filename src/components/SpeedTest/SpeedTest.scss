.speed-test-wrap {
  width: 100%;
  padding: 0 25px;
  margin-top: 60px;
  overflow: hidden;

  .speed-title {
    margin-bottom: 29px;
    font-size: 37.5px;
    font-weight: 900;
    line-height: normal;
    text-align: center;
  }

  .speed-icon {
    width: 36px;
    height: 36px;
    margin-right: 20px;
    background: url("~@/assets/images/common/speed-icon.png") no-repeat center/cover;
  }

  .arrow-icon {
    display: block;
    width: 34px;
    height: 34px;
  }

  .left-info {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: 700;
    line-height: normal;
    color: #7279a2;
  }

  .active-server {
    display: flex;
    align-items: center;
    font-size: 22px;
    font-weight: 500;
    color: #686868;
  }
}

.speed-test-modal {
  width: 440px;
  background: #f3f6f9;
  border-radius: 20px;

  .speed-content {
    padding: 28px 30px;
  }
}

.speed-header {
  margin-bottom: 30px;
  font-size: 30px;
  font-weight: 700;
  color: #333;
  text-align: center;
}

.speed-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 17px;

  .speed-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 0;
    height: 60px;
    font-size: 28px;
    font-weight: 500;
    line-height: normal;
    color: #66a2ef;
    background: url(~@/assets/images/service/item-bg.png) no-repeat center/100% 100%;
  }

  .speed-info {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .speed-line {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 50%;
    height: 60px;
    font-size: 19px;
    font-weight: 500;
    line-height: normal;
    color: #fff;
    text-align: center;
  }

  .speed-ms {
    width: 50%;
    font-size: 22px;
    font-weight: 700;
    line-height: normal;
    text-align: center;
  }

  .speed-name {
    display: flex;
    align-items: center;
  }

  .speed-icon {
    flex-shrink: 0;
    width: 36px;
    height: 36px;
    margin-right: 20px;
    background: url("~@/assets/images/common/speed-icon.png") no-repeat center/cover;
  }

  .speed-item.speed-active {
    color: $main-color;
  }

  .check-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    margin-left: 14px;
  }

  .correct {
    display: block;
    width: 100%;
    height: 100%;
  }
}
