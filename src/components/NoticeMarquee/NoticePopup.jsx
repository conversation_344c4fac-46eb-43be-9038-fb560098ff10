import React from "react";
import { withRouter } from "react-router-dom";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { wysiwygUtil } from "tcg-mobile-common";
import MC from "tcgmodulemc";

import CloseIcon from "@/components/CloseIcon";
import CommonPopup from "@/ui/CommonPopup/CommonPopup";

import "./NoticePopup.scss";

@inject("common", "languageShell")
@withRouter
@observer
class NoticePopup extends React.Component {
  constructor(props) {
    super(props);
    this.swiper = null;
    this.noticeSwiperRef = null;
    this.prevNavRef = null;
    this.nextNavRef = null;
  }
  componentDidMount() {}
  componentDidUpdate(prevProps, prevState) {}
  componentWillUnmount() {
    this.swiper?.destroy(true, true);
  }
  initSwiper = () => {
    if (this.swiper) {
      this.swiper?.destroy(true, true);
    }
    if (this.props.noticeList?.length > 0) {
      this.swiper = new MC.Swiper(this.noticeSwiperRef, {
        observer: true,
        observeParents: true,
        initialSlide: this.props.activeIndex,
        spaceBetween: 20,
        navigation: {
          nextEl: this.nextNavRef,
          prevEl: this.prevNavRef,
        },
      });
    }
  };
  render() {
    const { activeItem, noticeList } = this.props;
    return (
      <CommonPopup
        className="notice-popup-wrap popup-standard"
        show={this.props.show}
        onClose={this.props.closePopup}
        closeIcon={<CloseIcon />}
      >
        <div className="notice-popup-content popup-standard-bg">
          <div className="popup-standard-header">
            <span>{this.props.languageShell.t("notice_popup_title")}</span>
          </div>
          <div className="notice-popup-inner">
            {noticeList.map((item, index) => {
              return (
                <div
                  key={`notice_popup_item_${index}`}
                  className="item-content"
                  dangerouslySetInnerHTML={{
                    __html: wysiwygUtil.convertContent(item?.content),
                  }}
                />
              );
            })}
          </div>
        </div>
      </CommonPopup>
    );
    // return (
    //   <CommonPopup
    //     className="notice-popup-wrap"
    //     show={this.props.show}
    //     onOpened={this.initSwiper}
    //     onClose={this.props.closePopup}
    //     closeable={false}
    //   >
    //     <div className="notice-popup-content">
    //       <div className="popup-nav nav-prev" ref={(c) => (this.prevNavRef = c)}>
    //         <img className="nav-icon" src={require("@/assets/images/common/nav-left.png")} />
    //       </div>
    //       <div className="popup-nav nav-next" ref={(c) => (this.nextNavRef = c)}>
    //         <img className="nav-icon" src={require("@/assets/images/common/nav-right.png")} />
    //       </div>
    //       <div className="swiper-container  notice-popup-swiper" ref={(c) => (this.noticeSwiperRef = c)}>
    //         <div className="swiper-wrapper notice-list">
    //           {this.props.noticeList.map((item, idx) => {
    //             return (
    //               <div className="notice-slide-item swiper-slide" key={`notice_popup_slide_${idx}`}>
    //                 <div className="slide-item-inner">
    //                   <div className="item-title">
    //                     <span>{item.title}</span>
    //                   </div>
    //                   <div
    //                     className="item-content"
    //                     dangerouslySetInnerHTML={{
    //                       __html: wysiwygUtil.convertContent(item?.content),
    //                     }}
    //                   />
    //                 </div>
    //               </div>
    //             );
    //           })}
    //         </div>
    //       </div>
    //     </div>
    //   </CommonPopup>
    // );
  }
}

NoticePopup.propTypes = {
  noticeList: PropTypes.array.isRequired,
  show: PropTypes.bool.isRequired,
  closePopup: PropTypes.func.isRequired,
  activeIndex: PropTypes.number,
  activeItem: PropTypes.object,
};

NoticePopup.defaultProps = {
  noticeList: [],
  show: false,
  activeIndex: 0,
  closePopup: () => {},
};

export default NoticePopup;
