import React from "react";
import dayjs from "dayjs";
import { inject, observer } from "mobx-react";
import { AutoIcon, Marquee, navigatorUtil, withCs } from "tcg-mobile-common";

import withDownload from "@/hoc/withDownload";

import NoticePopup from "./NoticePopup";

// Import Common Stylesheets
import "./NoticeMarquee.scss";

const { isApp, isPWA, isIos, isSafari } = navigatorUtil;
const showDownload = !isApp && !isPWA;

@inject("common", "languageShell", "mcLanguage", "mcCommon")
@withCs
@withDownload
@observer
class NoticeBars extends React.Component {
  get inboxUnreadCount() {
    return this.props.common.inboxUnreadCount;
  }
  get noticeList() {
    return this.props.common.not;
  }
  constructor(props) {
    super(props);
    this.state = {
      items: [],
      isEmpty: true,
      activeKey: "",
      showPopup: false,
      activeIndex: 0,
      activeItem: {},
    };
  }

  componentDidMount() {
    this.setItems();
  }

  componentDidUpdate(prevProps, nextState, nextContext) {
    if (prevProps.items !== this.props.items) {
      this.setItems();
    }
  }

  setItems() {
    let items = this.props.items;
    let isEmpty = false;
    if (!items || items.length === 0) {
      items = [
        {
          id: -1,
          title: this.props.languageShell.resource["no_announcement"],
        },
      ];
      isEmpty = true;
    }
    this.setState({
      items,
      isEmpty,
    });
  }

  getContent() {
    const { items, isEmpty } = this.state;
    return (
      <Marquee isEmpty={isEmpty} fps={40}>
        {items.map((i, idx) => (
          <span
            className={`content-item ${isEmpty ? "empty" : ""}`}
            key={`ms-${i.id}`}
            onClick={() => this.toNotice(idx, i)}
          >
            {i.title}
          </span>
        ))}
      </Marquee>
    );
  }

  toNotice = (index, item) => {
    this.props.history.push({
      pathname: "/m/notice",
      query: `${index}`,
    });
    // this.setState({
    //   activeItem: item,
    //   activeIndex: index,
    //   showPopup: true,
    //   activeKey: `${index}`,
    // });
  };

  formatDate = (date) => {
    return dayjs(date).format("YYYY/MM/DD");
  };

  coverContent = (content) => {
    const temp = document.createElement("div");
    temp.style.display = "none";
    temp.innerHTML = content;
    const convert = temp.textContent || temp.innerText;
    temp.remove();
    return convert;
  };

  closeNotice = () => {
    this.setState({
      showDetail: false,
      activeKey: "",
    });
  };

  togglePopup = () => {
    this.setState({
      showPopup: !this.state.showPopup,
    });
  };

  render() {
    return (
      <div className={`notice-root`}>
        <NoticePopup
          noticeList={this.noticeList}
          activeIndex={this.state.activeIndex}
          activeItem={this.state.activeItem}
          show={this.state.showPopup}
          closePopup={this.togglePopup}
        />
        <div className="notice-bg">
          <div className="notice-icon">
            <AutoIcon
              className="icon-notice"
              icon={require("!svg-sprite-loader!@/assets/images/home/<USER>")}
            />
          </div>
          <div className="marquee">{this.getContent()}</div>
          {showDownload && (
            <div className="btn-download" onClick={this.props.handleDownload}>
              <AutoIcon
                className="icon-download"
                icon={require("!svg-sprite-loader!@/assets/images/home/<USER>")}
              />
              <span>APP</span>
            </div>
          )}
        </div>

        {/* <div className="btn-message" onClick={() => this.props.history.push("/m/webEmail")}>
          <img className="icon-msg" src={require("@/assets/images/home/<USER>")} alt="" />
          <UnreadBubble count={this.inboxUnreadCount} />
        </div> */}
      </div>
    );
  }
}
export default NoticeBars;
