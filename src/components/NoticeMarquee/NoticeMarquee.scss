.notice-root {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  width: 100%;

  .notice-bg {
    position: relative;
    z-index: 2;
    display: flex;
    flex: 1;
    align-items: center;
    height: 75px;
    padding: 6px 24px;
    overflow: hidden;
    background: var(--bg-color-surface);
  }

  .notice-icon {
    margin-right: 14px;

    img,
    .am-icon {
      display: block;
      width: 32px;
      height: 32px;
      object-fit: contain;
    }
  }

  .btn-download {
    @include flex-center;
    flex-shrink: 0;
    flex-direction: column;
    gap: 4px;
    width: 63px;
    height: 63px;
    margin-left: 14px;
    font-size: 16px;
    font-weight: 600;
    line-height: normal;
    color: #fff;
    text-align: center;
    background: var(--bg-color-button-primary);
    border-radius: 100px;

    .icon-download {
      display: block;
      width: 25px;
      height: 24px;
      object-fit: contain;
    }
  }

  .btn-message {
    position: relative;
    flex-shrink: 0;
    margin-left: 15px;

    .icon-msg {
      display: block;
      width: 50px;
      height: 50px;
    }
  }

  .notice-tip {
    margin-right: 10px;
  }

  .marquee {
    position: relative;
    display: flex;
    flex: 1;
    align-items: center;
    height: 100%;
    overflow: hidden;
    font-size: 24px;
    line-height: normal;

    .marquee_content {
      position: relative;
      white-space: nowrap;
      will-change: right;

      &.empty {
        width: 100%;
        margin-left: 0;
        text-align: center;
        will-change: initial;
      }
    }

    .content-item {
      margin-right: 40px;
    }
  }

  .marquee table {
    border: 0 !important;
  }

  .marquee tbody {
    border: 0 !important;
  }

  .marquee td {
    border: 0 !important;
  }

  .marquee tfoot {
    border: 0 !important;
  }

  .marquee th {
    border: 0 !important;
  }

  .marquee thead {
    border: 0 !important;
  }

  .marquee tr {
    border: 0 !important;
  }

  .marquee tt {
    border: 0 !important;
  }
}
