.notice-popup-wrap {
  .notice-popup-content {
    position: relative;

    .notice-popup-header {
      height: 83px;
      background: url("~@/assets/images/home/<USER>") no-repeat center/cover;
    }

    .notice-popup-inner {
      width: 100%;
      padding: 50px 40px;

      .item-content:last-child {
        padding: 20px 0 0;
      }

      .item-content:first-child {
        padding: 0 0 20px;
      }

      .item-content:not(:last-child) {
        border-bottom: 1px dashed #1c6783;
      }
    }

    .item-content {
      padding: 20px 0;
      font-size: 27px;
      font-weight: 400;
      line-height: 120%;

      img {
        display: block;
        max-width: 100%;
      }
    }

    .btn-confirm {
      width: fit-content;
      padding: 14px 48px;
      margin: 13px auto 0;
      font-size: 32px;
      line-height: 41.6px;
      text-align: center;
      background: #668bff;
      border-radius: 6px;
    }

    .notice-popup-swiper {
      display: flex;
      flex: 1;
      width: 100%;
    }

    .notice-list {
      flex: 1;
      height: auto;
    }

    .notice-slide-item {
      height: 100%;
    }

    .slide-item-inner {
      height: 100%;
      overflow: hidden auto;

      .item-title {
        margin-bottom: 30px;
        font-size: 30px;
        font-weight: 700;
        line-height: 32px;
        color: #fff;
        text-align: center;
      }
    }

    .popup-nav {
      position: absolute;
      top: 50%;
      z-index: 10;
      width: 41px;
      height: 64px;
      margin-top: -32px;

      .nav-icon {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .nav-prev {
      left: -42px;
    }

    .nav-next {
      right: -42px;
    }
  }
}
