import React from "react";
import { withRouter } from "react-router";
import { inject, observer } from "mobx-react";

import CloseIcon from "@/components/CloseIcon";
import Language from "@/components/LanguageSelect/Language";
import ChangePsw from "@/containers/ChangePsw/ChangePsw";
import Forget from "@/containers/Forget/Forget";
import Login from "@/containers/Login/Login";
import Register from "@/containers/Register/Register";
import CommonPopup from "@/ui/CommonPopup/CommonPopup";

import "./FormPopupGroup.scss";

@inject("common", "languageShell")
@withRouter
@observer
class FormPopupGroup extends React.Component {
  componentDidMount() {
    // const path = location.pathname;
    // if (path === "/m/login") {
    //   this.props.common.showLogin(true);
    // } else if (["/m/register", "/m/register.html"].includes(path)) {
    //   this.props.common.showRegister(true);
    // } else if (path === "/m/forget") {
    //   this.props.common.showForget(true);
    // } else if (path === "/m/changePsw") {
    //   if (get("MC_SESSION_INFO")) {
    //     this.props.common.showChangePsw(true);
    //   } else {
    //     this.props.history.replace("/m/home");
    //   }
    // }
  }
  closeLogin = () => {
    this.props.common.showLogin(false);
    if (location.pathname === "/m/login") {
      this.props.history.replace("/m/home");
    }
  };
  closeRegister = () => {
    this.props.common.showRegister(false);
  };
  closeForget = () => {
    this.props.common.showForget(false);
  };
  closeChangePsw = () => {
    this.props.common.showChangePsw(false);
  };
  closeLanguage = () => {
    this.props.common.showLanguage(false);
  };
  closeForm = () => {
    this.props.common.showFormPopup(false);
  };
  handleExited = () => {
    if (["/m/register", "/m/login", "/m/register.html", "/m/forget", "/m/changePsw"].includes(location.pathname)) {
      this.props.history.replace("/m/home");
    }
  };

  render() {
    return (
      <div className="form-popup-group">
        <CommonPopup
          className="form-popup-wrap popup-standard"
          show={this.props.common.formPopupStatus}
          onClose={this.closeForm}
          onClosed={this.handleExited}
          // closeIconPlacement="outside"
          closeIcon={<CloseIcon />}
        >
          {this.props.common.loginModalStatus && <Login {...this.props} />}
          {this.props.common.forgetModalStatus && <Forget {...this.props} />}
          {this.props.common.registerModalStatus && <Register {...this.props} />}
        </CommonPopup>
        <CommonPopup
          className="form-popup-wrap popup-standard"
          show={this.props.common.changePswModalStatus}
          onClose={this.closeChangePsw}
          onClosed={this.handleExited}
          // closeIconPlacement="outside"
          closeIcon={<CloseIcon />}
        >
          <ChangePsw {...this.props} />
        </CommonPopup>
        <CommonPopup
          className="language-popup-wrap"
          show={this.props.common.languageModalStatus}
          onClose={this.closeLanguage}
        >
          <Language />
        </CommonPopup>
      </div>
    );
  }
}

export default FormPopupGroup;
