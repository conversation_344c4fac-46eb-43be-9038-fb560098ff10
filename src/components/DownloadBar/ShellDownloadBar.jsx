import React from "react";
import { withRouter } from "react-router-dom";
import { inject, observer } from "mobx-react";
import { DownloadBar, withReferral } from "tcg-mobile-common";

import "./ShellDownloadBar.scss";

@inject("languageShell", "common", "tcgCommon", "mcCommon")
@withRouter
@withReferral
@observer
class ShellDownloadBar extends React.Component {
  get resources() {
    return this.props.common.downLinkObj;
  }
  getIconUrl = () => {
    const host = this.props.mcCommon.imageCDNOne;
    if (this.resources["AppDownloadIcon"]) {
      return this.resources["AppDownloadIcon"]?.url;
    }
    return `${host}/TCG_PROD_IMAGES/B2C/download/${this.props.mcCommon.merchant}/appIcon.png`;
  };
  centerSlot = () => {
    return (
      <div className="shell-download-content">
        <div className="download-bar-info" onClick={() => this.props.common.setDownPopup(true)}>
          <div className="download-bar-banner">
            <img className="download-img" src={require("./images/download-bar.gif")} />
          </div>

          <span className="shell-download-btn">{this.props.languageShell.t("download")}</span>
          {/* <div className="app-text">
            <div className="app-down-title">{this.props.languageShell.t("casino_app")}</div>
            <div className="app-down-tip" style={{ color: this.props.tcgCommon.mobileTemplate?.app_desc_font_color }}>
              <span>
                {this.props.tcgCommon.mobileTemplate?.app_desc || this.props.languageShell.t("download_get_reward")}
              </span>
            </div>
          </div> */}
        </div>
        {/* <div
          className="shell-download-btn animate__animated animate__heartBeat animate__delay-1s animate__infinite	infinite animate__slower"
          onClick={() => this.props.history.push("/m/download")}
        >
          <span>{this.props.languageShell.t("download")}</span>
        </div> */}
      </div>
    );
  };
  render() {
    return (
      <DownloadBar
        {...this.props}
        // centerSlot={this.centerSlot}
        onClickDownload={() => this.props.history.push("/m/download")}
      />
    );
  }
}

export default ShellDownloadBar;
