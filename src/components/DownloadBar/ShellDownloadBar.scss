.download-bar {
  .download-bar-btn {
  }

  .download-bar-center {
    top: 0 !important;
    right: 0;
    left: auto !important;
    width: 100%;
    height: 100%;
    transform: translate(0) !important;

    .shell-download-btn {
      @include flex-center;
      position: relative;
      flex-shrink: 0;
      width: 150px;
      height: 50px;
      padding: 0 10px;
      font-size: 20px;
      color: #fff;
      text-align: center;
      background: #000;
      border-radius: 10px;
    }

    .shell-download-content {
      width: 100%;
      height: 100%;
      padding-left: 80px;
      background: #fff;
    }

    .download-bar-info {
      display: flex;
      align-items: center;
      width: 100%;
      height: 100%;
      padding: 0 10px 0 0;
    }

    .btn-close {
      flex-shrink: 0;
      width: 20px;
      height: 20px;
      margin-right: 30px;
    }

    .download-bar-banner {
      display: flex;
      flex: 1;
      align-items: center;
      height: 100%;
    }

    .download-img {
      display: block;
      height: 50px;
    }

    .app-down-title {
      position: relative;
      margin-bottom: 4px;
      font-size: 28px;
      font-weight: 700;
      line-height: normal;
      background: linear-gradient(180deg, #fcb632 24.24%, #ff7304 35.15%, #ff9948 67.61%, #fffaa6 78.79%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .app-down-tip {
      font-size: 20px;
      line-height: normal;
      color: #fff;
    }

    .app-icon {
      width: 50px;
      height: 50px;
      margin-right: 20px;
    }
  }
}
