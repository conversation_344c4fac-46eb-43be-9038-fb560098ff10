._container_box {
  .download-bar {
    z-index: $download-bar-index !important;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: var(--download-bar-height) !important;
    padding: 0 0 0 128px;
    color: var(--text-color-primary);
    background: var(--download-bar-bg);

    .download-bar-icon {
      top: 20px;
      left: 32px;
      width: 80px;
      height: 80px;
      background-color: transparent;
      border-radius: 6px;

      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    .download-bar-btn {
      @include flex-center;
      position: absolute;
      top: 50%;
      right: 108px;
      min-width: 199px;
      height: 61px;
      padding: 0 24px;
      font-size: 24px;
      font-weight: 900;
      line-height: 61px;
      letter-spacing: 0.48px;
      background: var(--bg-color-gradient-primary);
      border-radius: 100px;
      transform: translateY(-50%);
    }

    .download-bar-close {
      top: 34.5px;
      right: 32px;
      width: 52px;
      height: 52px;
      background: url("./images/close.png") no-repeat center/cover;

      .am-icon {
        display: none;
        width: 100%;
        height: 100%;
      }
    }

    .app-full-name-wrap {
      max-width: 42vw;
      height: auto;
      margin-bottom: 12px;
      font-size: 24px;
      font-weight: 600;
      line-height: normal;
      letter-spacing: 0.48px;
    }

    .app-star-grade {
      display: flex;
      align-items: center;
      height: fit-content;
      margin: 0;

      .grade-text {
        display: none;
      }

      .star-icon {
        width: 32px;
        height: 32px;
        margin-right: 8px;
        background: url("./images/star.svg") no-repeat center/cover;
      }
    }
  }

  .app-win {
    display: none;
  }
}
