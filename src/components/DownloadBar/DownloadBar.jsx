import React from "react";
import { with<PERSON>outer } from "react-router-dom";
import { Icon, Modal } from "antd-mobile";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { navigatorUtil } from "tcg-mobile-common";

import { get } from "@/utils/storage";

// import { get as requestGET } from "@/apis/apiUtils";
// import DownloadPopup from "../DownloadPopup/DownloadPopup";
import "./DownloadBar.scss";

const keyHideDownloadBar = "hideDownloadBar";

const { isApp, isIos, isNotSupportPWAIos, isPWA, isSafari, isSockPuppet } = navigatorUtil;

@inject("mcLanguage", "tcgCommon", "common", "mcCommon", "personal", "languageShell")
@withRouter
@observer
class DownloadBar extends React.Component {
  state = { title: "", links: {}, hideBar: false, showHomeScreen: false };
  constructor(props) {
    super(props);
    this.isAPP = isApp;
    this.isIos = isIos;
    this.isSafari = isSafari();
    this.isWebclip = +window.localStorage.getItem("WEBCLIP") === 1;
    this.isPWA = isPWA;
    this.isNotSupportPWAIos = isNotSupportPWAIos();
    this.init(this.state);
  }
  init(state) {
    if (this.isPWA) {
      window.localStorage.setItem(keyHideDownloadBar, "1");
    }
    const hideDownloadBar = window.localStorage.getItem(keyHideDownloadBar);
    if (hideDownloadBar === undefined) {
      this.props.tcgCommon.showAppWin(false);
    }
    const hideBar = +hideDownloadBar === 1;
    state.hideBar = hideBar;
    this.props.tcgCommon.showAppWin(hideBar);
  }

  componentDidMount() {
    const session = get("MC_SESSION_INFO");
    if (!session) {
      this.props.tcgCommon.getAvailablePromotions();
    }
    this.setupLinks();
    if (!(this.isAPP || this.notSetupAnyLinks() || !this.props.tcgCommon.isAppWinMounted)) {
      window.scrollTo(0, 1);
    }
  }

  setupLinks() {
    const links = this.props.common.downLink.reduce((acc, curr) => {
      return {
        ...acc,
        [curr.resourceType]: (curr.url || "").trim(),
      };
    }, {});
    this.setState({ links });

    // webclip check
    if (this.isWebclip && !links["IOS"]) {
      this.setState({ hideBar: true });
      this.props.tcgCommon.showAppWin(false);
    }

    // old ios
    if (this.isNotSupportPWAIos && this.isPWA) {
      this.setState({ hideBar: true });
      this.props.tcgCommon.showAppWin(false);
    }
  }
  canShowProfile() {
    return this.props.common.downLink.some(({ resourceType }) => resourceType === "MOBILECONFIG");
  }
  handleHide = () => {
    this.setState({ hideBar: true });
    // window.localStorage.setItem(keyHideDownloadBar, 1);
    this.props.tcgCommon.showAppWin(true);
    this.props.onClose();
  };
  canShow() {
    if (this.props.requiredLogin) {
      return !this.state.hideBar && get("MC_SESSION_INFO");
    }
    return !this.state.hideBar;
  }
  handlePopupHide = () => {
    this.props.tcgCommon.showDownloadPopup(false);
  };
  getIconUrl() {
    const host = this.props.mcCommon.imageCDNOne;
    if (this.state.links["AppDownloadIcon"]) {
      return this.state.links["AppDownloadIcon"];
    }
    return `${host}/TCG_PROD_IMAGES/B2C/download/${this.props.mcCommon.merchant}/appIcon.png`;
  }
  handlePopupShow = () => {
    if (this.isIos && !this.isSafari) {
      const msg = this.props.mcLanguage.td("app_download_safari_required", "Please use Safari for download App.");
      Modal.alert(this.props.mcLanguage.t("notice"), msg, [
        {
          text: this.props.mcLanguage.t("confirm"),
        },
      ]);
      return;
    }
    this.props.common.setDownPopup(true);
    // this.props.tcgCommon.showDownloadPopup(true);
  };
  notSetupAnyLinks() {
    if (this.isIos) {
      const hasIosApp = this.state.links["IOS"] && !this.props.hideIosApp;
      return !(hasIosApp || this.state.links["MOBILECONFIG"] || !this.props.hideHomeScreen);
    } else {
      return !this.state.links["Android"];
    }
  }
  isShowAppBonus() {
    const { showAppBonus } = this.props;
    const session = get("MC_SESSION_INFO");
    if (session) {
      return showAppBonus && this.props.personal.availablePromosByKey("APP_DOWNLOAD").enabled;
    } else {
      return showAppBonus && this.props.tcgCommon.availablePromosByKey("APP_DOWNLOAD").enabled;
    }
  }
  render() {
    const { t } = this.props.mcLanguage;
    const { showAppBonus } = this.props;
    // AppWin 有出現, 下載bar才能出現
    if (this.isAPP || this.notSetupAnyLinks() || !this.props.tcgCommon.isAppWinMounted) {
      return null;
    }

    return (
      <div
        className={`shell-download-bar ${this.props.className} ${!this.canShow() ? "hide-bar" : "show-bar"}`}
        style={{ background: this.props.tcgCommon.mobileTemplate.app_background }}
      >
        <div className="download-bar-wrap">
          <div className="download-bar-info">
            <div className="download-bar-close" onClick={this.handleHide}>
              <Icon type={require("!svg-sprite-loader!@/assets/images/common/icon-close.svg")} />
            </div>
            <img className="app-icon" src={this.getIconUrl()} alt="app-icon" />
            <div className="app-text">
              <span
                className="app-full-name"
                style={{ color: this.props.tcgCommon.mobileTemplate?.app_desc_font_color }}
              >
                {this.props.tcgCommon.mobileTemplate?.app_desc}
              </span>
              <div className="app-star-grade">
                <span className="star-icon" />
                <span className="star-icon" />
                <span className="star-icon" />
                <span className="star-icon" />
                <span className="star-icon" />
                {/* <span className="grade-text">5.0</span> */}
              </div>
            </div>
          </div>
          <div className="download-bar-btn" onClick={() => this.props.history.push("/m/download")}>
            {t("app_download_app")}
          </div>
        </div>
        {/* {this.isShowAppBonus() && (
          <div className="download-bar-gift">
            <i className="gift-icon animate__heartBeat" />
          </div>
        )} */}
        {/* <DownloadPopup
          show={this.props.tcgCommon.isShowDownloadPopup && !this.state.hideBar}
          hideHomeScreen={this.props.hideHomeScreen}
          onHide={this.handlePopupHide}
          showAppBonus={this.props.showAppBonus}
          hideIosApp={this.props.hideIosApp}
        /> */}
      </div>
    );
  }
}

DownloadBar.propTypes = {
  requiredLogin: PropTypes.bool,
  onHide: PropTypes.func,
  showAppBonus: PropTypes.bool,
};

DownloadBar.defaultProps = {
  className: "",
  requiredLogin: false,
  hideHomeScreen: false,
  hideIosApp: false,
  showAppBonus: true,
  onClose: () => {},
};

export default DownloadBar;
