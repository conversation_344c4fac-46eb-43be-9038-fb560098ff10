import React from "react";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import Qs from "qs";

import { isoLangMap } from "@/utils/isoLang";

import "./YoutubeVideo.scss";

const YoutubeVideo = (props) => {
  const formatSrc = (url) => {
    const isYoutube = /\.youtube\.com/.test(url);
    if (!isYoutube) {
      console.error("Not youtube link!");
      return null;
    }
    const playerVars = {
      autoplay: 0,
      // enablejsapi: 1,
      modestbranding: 1,
      hl: isoLangMap[props.languageShell.currentLanguage]?.code,
      cc_load_policy: 0,
      iv_load_policy: 3,
      // controls: 0,
      rel: 0,
    };
    const URLINFO = new URL(url);
    if (URLINFO.pathname === "/watch") {
      const params = Qs.parse(URLINFO.search, {
        ignoreQueryPrefix: true,
      });
      return `${URLINFO.origin}/embed/${params.v}?${Qs.stringify(playerVars)}`;
    }
    if (/\/shorts\//.test(URLINFO.pathname)) {
      const result = URLINFO.pathname.match(/\/shorts\/(.*)/);
      return `${URLINFO.origin}/embed/${result[1]}?${Qs.stringify(playerVars)}`;
    }
    return `${url}?${Qs.stringify(playerVars)}`;
  };
  const videoSrc = formatSrc(props.src);
  if (!videoSrc) {
    return null;
  }
  return (
    <div className={cz("youtube-video-wrap", props.className)}>
      <iframe
        className="js-iframe youtube-video"
        title="YouTube video player"
        frameborder="0"
        allowFullScreen="true"
        src={videoSrc}
      />
    </div>
  );
};

YoutubeVideo.propTypes = {
  src: PropTypes.string.isRequired,
};
YoutubeVideo.defaultProps = {
  className: "",
};

export default inject("languageShell")(observer(YoutubeVideo));
