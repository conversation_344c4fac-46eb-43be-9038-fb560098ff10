import React from "react";
import { withRouter } from "react-router-dom";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { difference, get as _get, merge } from "lodash";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { wysiwygUtil } from "tcg-mobile-common";

import CommonSwiperMenu from "@/components/CommonSwiperMenu/CommonSwiperMenu";
import CommonPopup from "@/ui/CommonPopup/CommonPopup";
import { get, localGet, localSet, set } from "@/utils/storage";

import "./PopupV2.scss";

const LOGIN_PU_A = "SHELL_PU_A";
const LOGIN_PU_B = "SHELL_PU_B";

function getTomorrow(base) {
  const baseDay = new Date(base);
  const tomorrow = new Date(baseDay);
  tomorrow.setDate(baseDay.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0);
  return tomorrow.getTime();
}

/**
 * 初始資料用，今天內是否不再提示判斷
 * @return boolean show or not
 */
function canShow() {
  const showInThisSession = get(getPopupType())?.show ?? true;
  const oldStorage = localGet(getPopupType()) || {};
  if (oldStorage.todayHideTime) {
    return Date.now() > getTomorrow(oldStorage.todayHideTime);
  }
  return showInThisSession;
}

function getPopupType() {
  const session = get("MC_SESSION_INFO");
  return session ? LOGIN_PU_A + `${session.id || session.customerId}` : LOGIN_PU_B;
}

@inject("mcLanguage", "languageShell")
@withRouter
@observer
class PopupV2 extends React.Component {
  get show() {
    const { current } = this.state;
    const canShow = this.state.popupType && this.state[this.state.popupType];
    const { popupList } = this.props;
    if (!popupList || popupList.length === 0) {
      return false;
    }
    if (!canShow) {
      return false;
    }
    return current.length !== 0;
  }
  constructor(props) {
    super(props);
    this.state = {
      current: [],
      popupType: "",
      closing: false,
      [getPopupType()]: canShow(),
      curIndex: 0,
      todayHide: false,
      activeItem: null,
      activeKey: 0,
    };
  }
  componentDidMount() {
    this.updateListDelay(true);
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.popupList !== this.props.popupList) {
      this.updateListDelay(false);
    }
  }
  componentWillUnmount() {
    document.querySelector("body").classList.remove("popup-on");
  }

  updateListDelay(init) {
    // 避開自動登入, 連續彈了登入前及登入後彈窗
    setTimeout(() => {
      this.updateList(init);
    }, 500);
  }
  updateList(init) {
    const session = get("MC_SESSION_INFO");
    const popupType = session ? LOGIN_PU_A + `${session.id || session.customerId}` : LOGIN_PU_B;
    const status = session ? "A" : "B";
    const current = (this.props.popupList || []).filter((ele) => {
      if (status === "A" && session.firstTimeLogin && ele.execution === "F") {
        return true;
      } else if (ele.execution === status) {
        return true;
      }
      return false;
    });

    if (!init || current.length > 0) {
      const oldStorage = localGet(popupType) || {};
      const oldData = _get(oldStorage, `data`, []);
      const newData = current.map(({ title, content }) => CryptoJS.MD5(`${title},${content}`).toString());
      this.saveStorageData(popupType, newData);

      const showWhenTodayEnd = oldStorage.todayHideTime && Date.now() > getTomorrow(oldStorage.todayHideTime);
      const hasNewPopupShow = showWhenTodayEnd || difference(newData, oldData).length > 0;
      if (hasNewPopupShow) {
        this.saveStorageShow(popupType, true);
        if (!this.state[popupType]) {
          this.setState({ [popupType]: true });
        }
      }

      // 強制彈出處理, 每次無session就彈出
      if (this.props.forcePopup && !get(popupType)) {
        this.setState({ [popupType]: true });
      }
    }

    this.setState({ popupType, current });
  }
  saveStorageData(popupType, data = []) {
    const old = localGet(popupType) || {};
    localSet(popupType, merge(old, { data }));
  }
  saveStorageShow(popupType, show) {
    const data = { show: !!show };
    set(popupType, data);
    if (show) {
      this.removeTodayHide(popupType);
    }
  }
  saveTodayHide(popupType) {
    if (this.state.todayHide) {
      const old = localGet(popupType);
      const data = { todayHideTime: Date.now() };
      localSet(popupType, merge(old, data));
    } else {
      this.removeTodayHide(popupType);
    }
  }
  removeTodayHide(key) {
    const old = localGet(key);
    delete old.todayHideTime;
    localSet(key, old);
  }
  closePopup = () => {
    document.querySelector("body").classList.remove("popup-on");
    this.setState({ [this.state.popupType]: false });
    this.saveStorageShow(this.state.popupType, false);
    this.saveTodayHide(this.state.popupType);
  };
  checkDetail = (item) => {
    this.setState({
      activeItem: item,
    });
  };
  navClick = (item, index) => {
    this.setState({
      activeKey: index,
    });
  };
  viewAnnoun() {
    if (!this.show) {
      return null;
    }

    return (
      <div className="shell-popup-content">
        <div className="common-popup-header">
          <p>{this.props.languageShell.t("in_increase_announcement")}</p>
          <div className="normal-close" onClick={this.closePopup}>
            <Icon type={require("!svg-sprite-loader!@/assets/images/common/popup-close.svg")} />
          </div>
        </div>
        <div className="popup-body hide-scrollbar">
          <div className="popup-nav">
            {this.show && (
              <CommonSwiperMenu
                menus={this.state.current}
                activeIndex={this.state.activeKey}
                menuClick={this.navClick}
                name="title"
              />
            )}
          </div>
          <div className="popup-content-inner">
            {this.state.current.map((i, idx) => {
              return (
                <div className={cz("popup-item-content", { on: this.state.activeKey === idx })} key={`popup_${idx}`}>
                  <div
                    dangerouslySetInnerHTML={{
                      __html: wysiwygUtil.convertContent(i.content),
                    }}
                  />
                </div>
              );
            })}
          </div>
        </div>
        {/* <div className="shell-popup-footer">
          <div className="btn-got" onClick={this.closePopup}>
            <span>{this.props.languageShell.t("i_know")}</span>
          </div>
        </div>
        <div className="today-bar">
          <CheckBox
            checked={this.state.todayHide}
            onClick={() =>
              this.setState(({ todayHide }) => ({
                todayHide: !todayHide,
              }))
            }
          >
            <p>{this.props.languageShell.t("today_hide")}</p>
          </CheckBox>
        </div> */}
      </div>
    );
  }
  closeDetail = () => {
    this.setState({
      activeItem: null,
    });
  };
  render() {
    const { popupList } = this.props;
    if (!popupList || popupList.length === 0) {
      return null;
    }
    return (
      <div className="shell-popup-container">
        <CommonPopup className="popup-normal" showClose={false} show={this.show}>
          {this.viewAnnoun()}
        </CommonPopup>
      </div>
    );
  }
}

PopupV2.propTypes = {
  popupList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  autoplay: PropTypes.bool,
  logo: PropTypes.node,
  header: PropTypes.node,
};

PopupV2.defaultProps = {
  autoplay: false,
};

export default PopupV2;
