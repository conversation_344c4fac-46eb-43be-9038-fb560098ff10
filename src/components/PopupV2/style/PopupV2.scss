.shell_popup_container_v2 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100011;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgb(0 0 0 / 70%);

  &.show-popup {
    .popup_body {
      animation-name: zoomIn;
      animation-duration: var(--animate-duration);
      animation-fill-mode: both;
    }
  }

  &.hide-popup {
    animation-name: fadeOut;
    animation-duration: var(--animate-duration);
    animation-fill-mode: both;

    .popup_body {
      animation-name: zoomOut;
      animation-duration: var(--animate-duration);
      animation-fill-mode: both;
    }
  }

  .popup_body {
    position: relative;
    width: 630px;
    height: 800px;

    .disable-btn,
    .active-btn {
      position: absolute;
      top: 45%;
      z-index: 1;
      width: 56px;
      height: 56px;
      border-radius: 50%;
      transform: translateY(-50%);

      i {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    .nav-left {
      left: -20px;

      i {
        background: center / 20% no-repeat resolve("../images/left.png");
      }
    }

    .nav-right {
      right: -20px;

      i {
        background: center / 20% no-repeat resolve("../images/right.png");
      }
    }

    .disable-btn {
      background-color: #757575;
      box-shadow: 0 4px 5px 0 rgb(140 140 140 / 37%);
    }

    .active-btn {
      background-image: linear-gradient(to bottom, #0cd2af, #07b8c5);
      box-shadow: 0 4px 5px 0 rgb(7 185 196 / 37%);
    }
  }

  .popup_content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 210px 0 30px;
    overflow: hidden;
    background-color: #1b294c;
    border-radius: 10px;
  }

  .header {
    position: relative;
    height: auto !important;
  }

  .close-btn {
    position: absolute;
    top: -90px;
    right: -10px;
    z-index: 10002;
    width: 50px;
    height: 50px;
    background: url("../../../assets/images/common/popup-close.png") no-repeat center/cover;

    svg {
      position: absolute;
      top: 20%;
      right: 20%;
      width: 40%;
      height: 40%;
      fill: currentcolor;
    }
  }

  .popup_title {
    position: absolute;
    top: -85px;
    left: 0;
    z-index: 10;
    width: 100%;
    height: 274px;
    background: url("../images/title.png") no-repeat center/cover;
  }

  .text {
    box-sizing: border-box;
    flex: auto;
    width: 100%;
    height: 100%;
    padding: 0 20px;
    overflow: hidden auto;
    font-size: 26px;
    line-height: 1.5;
    color: #8a8a8a;
    border-radius: 10px;

    img {
      width: 100%;
    }
  }

  .popup_accordion {
    width: 100%;
    height: 100%;
    border: none;

    &::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 1px;
      content: "";
      background-color: #b29cd1;
      border: none;
      transform: scaleY(0.5);
    }

    .am-accordion-header {
      padding: 0 20px;
      background-color: transparent !important;

      &::after {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        content: "";
        background-color: #b29cd1;
        border: none;
        transform: scaleY(0.5);
      }

      .popup_item {
        width: 100%;
        padding-right: 45px;

        .popup_item_title {
          width: 100%;
          font-size: 26px;
          color: #fff0be;

          @include ellipsis;
        }
      }
    }

    .am-accordion-content {
      padding: 20px;
      background: none !important;

      .list {
        font-size: 26px;
        color: #fff0be;
      }
    }

    .am-accordion-content-box::after {
      display: none !important;
    }
  }

  .popup_pagination_footer {
    display: flex;
    justify-content: space-around;
    width: 100%;
    margin-top: 40px;

    &.hide {
      display: none;
    }

    .action {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 39%;
      min-height: 70px;
      font-size: 28px;
      color: #fff;
      background: linear-gradient(to bottom, #00e1fa, #4aa5fb);
      border-radius: 70px;
      box-shadow: 0 4px 5px 0 rgb(7 185 196 / 37%);

      &.disable {
        color: #9d9d9d;
        background: #eaeaea;
        box-shadow: 0 4px 5px 0 #8c8c8c;
      }
    }
  }

  .swiper-slide {
    overflow: hidden auto;
  }
}
