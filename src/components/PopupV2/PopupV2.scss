.shell-popup-container {
  .common-popup-container {
  }

  .shell-popup-content {
    width: 100%;
    height: 100%;
  }

  .shell-popup-header {
    position: relative;
    padding: 20px 0;
    font-size: 40px;
    font-weight: 500;
    line-height: 64px;
    color: #fff;
    text-align: center;
    background: #404040;
    border-radius: 16px 16px 0 0;

    @include flex-center;
  }

  .shell-popup-footer {
    @include flex-center;
    height: 92px;
    font-size: 36px;
    line-height: 40px;
    color: #fff;
    text-align: center;
    background: #404040;
    border-radius: 0 0 16px 16px;
  }

  .shell-popup-body {
    height: 600px;
    overflow: auto;
    background: #fff;
  }

  .today-bar {
    display: flex;
    justify-content: flex-end;
    margin-top: 5px;
  }

  .popup-item-content {
    display: none;
    font-size: 24px;
    font-weight: 500;
    line-height: normal;
    color: #fff;

    &.on {
      display: block;
    }

    .item-title {
      @include ellipsis;
    }

    .arrow-right {
      flex-shrink: 0;
      width: 25px;
      height: 25px;
    }
  }

  .btn-close {
    position: absolute;
    top: 40px;
    right: 40px;
    width: 20px;
    height: 20px;

    .am-icon {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}
