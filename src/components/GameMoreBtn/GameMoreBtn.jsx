import React from "react";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import "./GameMoreBtn.scss";

@inject("languageShell")
@observer
class GameMoreBtn extends React.Component {
  render() {
    if (!this.props.hasMore) {
      return null;
    }
    return (
      <div className="game-more">
        <div className="btn-more" onClick={this.props.onClick}>
          <span>{this.props.languageShell.t("more_game")}</span>
          {this.props.loading && <div className="progress-loading" />}
        </div>
      </div>
    );
  }
}

GameMoreBtn.propTypes = {
  onClick: PropTypes.func,
  loading: PropTypes.bool,
  hasMore: PropTypes.bool,
};

GameMoreBtn.defaultProps = {
  onClick: () => {},
  loading: false,
  hasMore: false,
};

export default GameMoreBtn;
