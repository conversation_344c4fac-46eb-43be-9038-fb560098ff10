.home-download-wrap {
  position: relative;
  width: 100%;
  height: 1214px;
  padding: 20px 31px 0 25px;
  overflow: hidden;
  background: url("./images/home-download-bg.png") no-repeat center/100% 100%;
  border-radius: 24px;

  &::before {
    position: absolute;
    top: 0;
    right: 0;
    width: 402px;
    height: 431px;
    content: "";
    background: url("./images/home-download-right.png") no-repeat center/100% 100%;
  }

  &::after {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 496px;
    height: 414px;
    content: "";
    background: url("./images/home-download-left.png") no-repeat center/100% 100%;
  }

  .download-logo {
    display: block;
    height: 118px;
  }

  .download-info {
    width: 100%;

    p {
      margin-top: 9px;
      font-size: 24px;
      font-weight: 500;
      line-height: normal;
      color: #fff;
      letter-spacing: 0.24px;
    }
  }

  .home-download-item {
    position: relative;
    width: 623px;
    height: 275px;

    &::after {
      position: absolute;
      right: -10px;
      bottom: -10px;
      z-index: 1;
      width: 622px;
      height: 275px;
      content: "";
      background: url("./images/item-bg2.png") no-repeat center/cover;
    }

    .home-download-item-inner {
      position: relative;
      z-index: 2;
      width: 100%;
      height: 100%;
      padding: 25px 0 0 37px;
      background: url("./images/item-bg1.png") no-repeat center/100% 100%;
    }

    .item-title {
      max-width: 200px;
      font-size: 32px;
      font-weight: 700;
      line-height: normal;
      color: #448ee4;
      letter-spacing: 0.32px;
    }
  }

  .download-banner {
    img {
      display: block;
      width: 351px;
      object-fit: contain;
    }
  }

  .home-download-content {
    position: relative;
    display: grid;
    gap: 43px;
    width: 100%;
    margin-top: 25px;

    .download-item-banner {
      position: absolute;
      top: 0;
      right: 0;
      width: 257px;
      height: 210px;
    }

    .download-qrcode {
      display: flex;
      gap: 16px;
      align-items: center;
      margin-top: 12px;

      .qr-code {
        padding: 8px;
        background: #fff;
        border: 1px solid #73d3ff;
        border-radius: 12px;
      }
    }

    .download-item {
      display: flex;
      flex-direction: column;
      gap: 8px;
      align-items: center;
      width: 128px;
      font-size: 24px;
      font-weight: 700;
      line-height: normal;
      color: #448ee4;
      letter-spacing: 0.24px;
    }

    .sys-name {
      display: flex;
      gap: 8px;
      align-items: center;

      .icon-sys {
        width: 24px;
        height: 23px;
      }
    }

    .responsible-gaming {
      width: 342px;
      height: 93px;
      margin: 40px 0 0 15px;
      object-fit: contain;
    }

    .app-info {
      padding-right: 100px;
      margin-top: 20px;

      h5 {
        margin-bottom: 10px;
        font-size: 30px;
        font-weight: 500;
        line-height: normal;
        color: #fff;
      }

      p {
        font-size: 25px;
        line-height: normal;
        color: #7f8696;
      }
    }
  }
}
