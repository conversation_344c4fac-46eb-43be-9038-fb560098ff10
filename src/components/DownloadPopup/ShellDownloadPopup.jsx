import React from "react";
import { inject, observer } from "mobx-react";
import { CommonLogo, navigatorUtil, withCs, withDownload } from "tcg-mobile-common";

import CommonPopup from "@/ui/CommonPopup/CommonPopup";
import { handleWindowPage } from "@/utils/openLink";

import HomeScreenPopup from "../HomeScreenPopup/HomeScreenPopup";

import "./ShellDownloadPopup.scss";

const { isApp, isPWA, isIos, isMobile, isSafari } = navigatorUtil;

@withCs
@withDownload
@inject("common", "languageShell", "mcLanguage", "mcCommon")
@observer
class AppDownload extends React.Component {
  get resources() {
    return this.props.common.downLinkObj;
  }
  state = {
    showHomeScreen: false,
    links: {},
  };
  componentDidMount() {
    this.setupLinks();
  }

  openResources = (type) => {
    const link = this.resources[type];
    if (link?.url) {
      handleWindowPage(link?.url);
    }
  };

  setupLinks() {
    const links = this.props.common.downLink.reduce((acc, curr) => {
      return {
        ...acc,
        [curr.resourceType]: (curr.url || "").trim(),
      };
    }, {});
    this.setState({ links });
  }
  canShowProfile() {
    const profile = this.state.links["MOBILECONFIG"];
    return profile && window.localStorage.getItem("WEBCLIP") !== "1" && isIos;
  }
  canShowIosAPP() {
    const ios = this.state.links["IOS"];
    return !isMobile || (ios && isIos && !this.props.hideIosApp);
  }
  canShowAndroidAPP() {
    return !isMobile || !isIos;
  }
  canShowHomeScreen() {
    return !isPWA && isIos && isSafari && !this.props.hideHomeScreen;
  }
  handleHomeScreenShow = (showHomeScreen) => () => {
    this.setState({
      showHomeScreen,
    });
  };
  handleIOSDownload = () => {
    const IOS = this.props.common.downLinkObj["IOS"];
    const MOBILECONFIG = this.props.common.downLinkObj["MOBILECONFIG"];
    if (IOS?.url) {
      window.location.href = IOS.url;
      return;
    }
    if (!MOBILECONFIG?.url) {
      return null;
    }
    window.location.href = MOBILECONFIG.url;
    this.props.processProfileDownload();
  };
  handleAndroidDownload = () => {
    const android = this.props.common.downLinkObj["Android"];
    if (android?.url) {
      window.location.href = android.url;
    }
  };
  handleDownload = () => {
    if (isIos) {
      this.handleIOSDownload();
    } else {
      this.handleAndroidDownload();
    }
  };
  hideDownloadPopup = () => {
    this.props.common.setDownPopup(false);
  };
  getIcon = () => {
    const host = this.props.mcCommon.imageCDNOne;
    if (this.resources["AppDownloadIcon"]) {
      return this.resources["AppDownloadIcon"]?.url;
    }
    return `${host}/TCG_PROD_IMAGES/B2C/download/${this.props.mcCommon.merchant}/appIcon.png`;
  };
  closeAll = () => {
    this.setState({
      showHomeScreen: false,
    });
  };
  render() {
    return (
      <div className="download-popup-wrap">
        <CommonPopup
          className="app-download-popup"
          show={this.props.common.showDownPopup}
          onClose={this.hideDownloadPopup}
        >
          <div className="app-download-content">
            {/* <div className="app-download-header">
              <img className="download-banner" src={require("./images/download-banner.png")} alt="" />
            </div> */}
            <CommonLogo className="download-logo" src={require("@/assets/images/logo/logo.png")} />
            <div className="download-title">{this.props.languageShell.t("download_promo")}</div>
            <div className="download-main">
              <div className="download-item">
                <div className="app-info">
                  {/* <Icon className="sys-icon" type={require("!svg-sprite-loader!@/assets/images/common/ios.svg")} /> */}
                  <img className="app-icon" src={this.getIcon()} alt="homescreen" />
                  <p>iOS APP</p>
                </div>
                <div className="down-btn" onClick={this.handleIOSDownload}>
                  <span>{this.props.languageShell.t("download")}</span>
                </div>
              </div>
              <div className="download-item">
                <div className="app-info">
                  {/* <Icon className="sys-icon" type={require("!svg-sprite-loader!@/assets/images/common/android.svg")} /> */}
                  <img className="app-icon" src={this.getIcon()} alt="homescreen" />
                  <p>Android APP</p>
                </div>
                <div className="down-btn" onClick={this.handleAndroidDownload}>
                  <span>{this.props.languageShell.t("download")}</span>
                </div>
              </div>

              {/* {this.canShowHomeScreen() && (
                  <div className="download-item">
                    <div className="app-info">
                      <img className="app-icon" src={this.getIcon()} alt="homescreen" />
                      <h5>{this.props.mcLanguage.t("app_popup_homescreen")}</h5>
                    </div>
                    <span className="down-btn" onClick={this.handleHomeScreenShow(true)}>
                      {this.props.languageShell.t("download")}
                    </span>
                  </div>
                )}
                {this.canShowAndroidAPP() && (
                  <div className="download-item">
                    <div className="app-info">
                      <img className="app-icon" src={this.getIcon()} alt="homescreen" />
                      <h5>{this.props.mcLanguage.t("app_popup_android")}</h5>
                    </div>
                    <a
                      className="down-btn"
                      rel="nofollow me noopener noreferrer"
                      href={this.state.links["Android"]}
                      target={this.props.getDownloadTarget(this.state.links["Android"])}
                    >
                      {this.props.languageShell.t("download")}
                    </a>
                  </div>
                )}
                {this.canShowProfile() && (
                  <div className="download-item iphone-link1">
                    <div className="app-info">
                      <img className="app-icon" src={this.getIcon()} alt="homescreen" />
                      <h5>{this.props.mcLanguage.t("app_popup_profile")}</h5>
                    </div>
                    <a
                      className="down-btn"
                      rel="nofollow me noopener noreferrer"
                      href={this.state.links["MOBILECONFIG"]}
                      target={this.props.getDownloadTarget(this.state.links["MOBILECONFIG"])}
                      onClick={this.props.processProfileDownload}
                    >
                      {this.props.languageShell.t("download")}
                    </a>
                  </div>
                )}
                {this.canShowIosAPP() && (
                  <div className="download-item iphone-link1">
                    <div className="app-info">
                      <img className="app-icon" src={this.getIcon()} alt="APP" />
                      <h5>{this.props.mcLanguage.t("app_popup_ios")}</h5>
                    </div>
                    <a
                      className="down-btn"
                      rel="nofollow me noopener noreferrer"
                      href={this.state.links["IOS"]}
                      target={this.props.getDownloadTarget(this.state.links["IOS"])}
                    >
                      {this.props.languageShell.t("download")}
                    </a>
                  </div>
                )} */}
            </div>
          </div>
        </CommonPopup>
        <HomeScreenPopup show={this.state.showHomeScreen} onHide={this.closeAll} />
      </div>
    );
  }
}

export default AppDownload;
