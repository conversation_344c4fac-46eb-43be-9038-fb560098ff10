import React from "react";
import { withRouter } from "react-router-dom";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";
import QRCode from "qrcode.react";
import { CommonLogo, navigatorUtil, withCs, withDownload } from "tcg-mobile-common";

import { handleWindowPage } from "@/utils/openLink";

import "./HomeDownload.scss";

const { isApp, isPWA, isIos, isMobile, isSafari } = navigatorUtil;

const paymentList = [
  { icon: require("@/assets/images/footer/payment1.png") },
  { icon: require("@/assets/images/footer/payment2.png") },
];

@withCs
@withDownload
@inject("common", "languageShell", "mcLanguage", "mcCommon")
@withRouter
@observer
class HomeDownload extends React.Component {
  get resources() {
    return this.props.common.downLinkObj;
  }
  componentDidMount() {
    window.scrollTo(0, 0);
  }
  handleIOSDownload = () => {
    const IOS = this.props.common.downLinkObj["IOS"];
    const MOBILECONFIG = this.props.common.downLinkObj["MOBILECONFIG"];
    if (IOS?.url) {
      window.location.href = IOS.url;
      return;
    }
    if (!MOBILECONFIG?.url) {
      return null;
    }
    window.location.href = MOBILECONFIG.url;
    this.props.processProfileDownload();
  };
  handleAndroidDownload = () => {
    const android = this.props.common.downLinkObj["Android"];
    if (android?.url) {
      window.location.href = android.url;
    }
  };
  handleDownload = () => {
    if (isIos) {
      this.handleIOSDownload();
    } else {
      this.handleAndroidDownload();
    }
  };
  canShowProfile() {
    const profile = this.resources["MOBILECONFIG"];
    return profile && window.localStorage.getItem("WEBCLIP") !== "1" && isIos;
  }
  canShowIosAPP() {
    const ios = this.resources["IOS"];
    return !isMobile() || (ios && isIos && !this.props.hideIosApp);
  }
  canShowAndroidAPP() {
    return !isMobile() || !isIos;
  }
  getIcon = () => {
    const host = this.props.mcCommon.imageCDNOne;
    if (this.resources["AppDownloadIcon"]) {
      return this.resources["AppDownloadIcon"]?.url;
    }
    return `${host}/TCG_PROD_IMAGES/B2C/download/${this.props.mcCommon.merchant}/appIcon.png`;
  };
  openResources = (type) => {
    const link = this.resources[type];
    if (link?.url) {
      handleWindowPage(link?.url);
    }
  };
  checkIosTutorial = () => {
    this.props.common.showIosTutorial(true);
  };
  render() {
    return (
      <div className="home-download-wrap">
        <div className="download-info">
          <CommonLogo className="download-logo" src={require("@/assets/images/logo/logo.png")} />
          <p>{this.props.languageShell.t("home_download_info")}</p>
        </div>
        <div className="home-download-content">
          <div className="home-download-item">
            <div className="home-download-item-inner">
              <div className="item-title">{this.props.languageShell.t("download_promo")}</div>
              <div className="download-qrcode">
                {this.resources["Android"]?.url && (
                  <div className="download-item" onClick={this.handleAndroidDownload}>
                    <div className="qr-code">
                      <QRCode value={this.resources["Android"]?.url} size={43} />
                    </div>
                    <div className="sys-name">
                      <Icon className="icon-sys" type={require("!svg-sprite-loader!./images/android.svg")} />
                      <span>Android</span>
                    </div>
                  </div>
                )}
                {this.resources["MOBILECONFIG"]?.url && (
                  <div className="download-item">
                    <div className="qr-code">
                      <QRCode value={this.resources["MOBILECONFIG"]?.url} size={43} />
                    </div>
                    <div className="sys-name">
                      <Icon className="icon-sys" type={require("!svg-sprite-loader!./images/ios.svg")} />
                      <span>iOS</span>
                    </div>
                  </div>
                )}
              </div>
              <img className="download-item-banner" src={require("./images/download-item-right.png")} alt="" />
            </div>
          </div>

          <div className="home-download-item">
            <div className="home-download-item-inner">
              <div className="item-title">{this.props.languageShell.t("responsible_gaming")}</div>
              <img className="responsible-gaming" src={require("@/assets/images/footer/responsible.png")} alt="" />
            </div>
          </div>

          <div className="home-download-item">
            <div className="home-download-item-inner">
              <div className="item-title">{this.props.languageShell.t("payment_method")}</div>
              <div className="payment-list">
                {paymentList.map((item, index) => {
                  return <img key={`footer_paymen_${index}`} className="payment-icon" src={item.icon} alt="" />;
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default HomeDownload;
