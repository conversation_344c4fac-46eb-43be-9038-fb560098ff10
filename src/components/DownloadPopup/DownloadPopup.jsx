import React from "react";
import { withRouter } from "react-router-dom";
import { Icon } from "antd-mobile";
import cx from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { withDownload } from "tcg-mobile-common";

import { isApp, isIos, isMobile, isPWA, isSafari } from "@/utils/navigatorUtil";

import HomeScreenPopup from "../HomeScreenPopup/HomeScreenPopup";

import "./DownloadPopup.scss";

@withDownload
@inject("mcLanguage", "common", "mcCommon", "languageShell")
@withRouter
@observer
class DownloadPopup extends React.Component {
  state = {
    showHomeScreen: false,
    showAppStep: false,
    links: {},
  };
  constructor(props) {
    super(props);
    this.isAPP = isApp();
    this.isMobile = isMobile();
    this.isSafari = isSafari();
  }
  componentDidMount() {
    this.setupLinks();
  }

  setupLinks() {
    const links = this.props.common.downLink.reduce((acc, curr) => {
      return {
        ...acc,
        [curr.resourceType]: (curr.url || "").trim(),
      };
    }, {});
    this.setState({ links });
  }

  canShowProfile() {
    const profile = this.state.links["MOBILECONFIG"];
    return profile && window.localStorage.getItem("WEBCLIP") !== "1" && isIos();
  }
  canShowIosAPP() {
    const ios = this.state.links["IOS"];
    return !this.isMobile || (ios && isIos() && !this.props.hideIosApp);
  }
  canShowAndroidAPP() {
    return !this.isMobile || !isIos();
  }
  canShowHomeScreen() {
    return !isPWA() && isIos() && this.isSafari && !this.props.hideHomeScreen;
  }
  handleHomeScreenShow = (showHomeScreen) => () => {
    this.setState({
      showHomeScreen,
      showAppStep: false,
    });
  };
  handleShowAppStep = (showAppStep) => () => {
    this.setState({ showAppStep });
  };
  closeAll = () => {
    this.setState(
      {
        showHomeScreen: false,
      },
      () => {
        this.props.onHide();
      }
    );
  };
  getIcon() {
    const host = this.props.mcCommon.imageCDNOne;
    if (this.state.links["AppDownloadIcon"]) {
      return this.state.links["AppDownloadIcon"];
    }
    return `${host}/TCG_PROD_IMAGES/B2C/download/${this.props.mcCommon.merchant}/appIcon.png`;
  }
  render() {
    const { t } = this.props.mcLanguage;
    if (!this.props.show) {
      return null;
    }
    const classNames = cx("download-bar-model-shell", {
      "hide-model": this.state.showHomeScreen,
    });
    return (
      <div>
        <div className={classNames}>
          <div className="bar-model-mask" onClick={this.props.onHide} />
          <div className="bar-model-contents">
            <div className="bar-model-closes" onClick={this.props.onHide}>
              <Icon type={require("!svg-sprite-loader!@/assets/images/common/btn-close.svg")} />
            </div>
            <div className="bar-model-bg">
              <img className="download-bg" src={require("./images/download-bg.png")} alt="" />
              {/* <div className="modal-title">
                <span>{this.props.languageShell.t("download_promo")}</span>
              </div> */}
            </div>
            <div className="appdown-list">
              {this.canShowHomeScreen() && (
                <div className="item-link main-home">
                  <div className="app-info">
                    <img className="app-icon" src={this.getIcon()} alt="homescreen" />
                    <div>
                      <h5>{this.props.mcLanguage.t("app_popup_homescreen")}</h5>
                    </div>
                  </div>
                  <span className="down-btn" onClick={this.handleHomeScreenShow(true)}>
                    {this.props.languageShell.t("download")}
                  </span>
                </div>
              )}
              {this.canShowAndroidAPP() && (
                <div className="item-link">
                  <div className="app-info">
                    <img className="app-icon" src={this.getIcon()} alt="homescreen" />
                    <div>
                      <h5>{this.props.mcLanguage.t("app_popup_android")}</h5>
                    </div>
                  </div>
                  <a
                    className="down-btn"
                    rel="nofollow me noopener noreferrer"
                    href={this.state.links["Android"]}
                    target={this.props.getDownloadTarget(this.state.links["Android"])}
                  >
                    {this.props.languageShell.t("download")}
                  </a>
                </div>
              )}
              {this.canShowProfile() && (
                <div className="item-link iphone-link1">
                  <div className="app-info">
                    <img className="app-icon" src={this.getIcon()} alt="homescreen" />
                    <div>
                      <h5>{this.props.mcLanguage.t("app_popup_profile")}</h5>
                    </div>
                  </div>
                  <a
                    className="down-btn"
                    rel="nofollow me noopener noreferrer"
                    href={this.state.links["MOBILECONFIG"]}
                    target={this.props.getDownloadTarget(this.state.links["MOBILECONFIG"])}
                    onClick={this.props.processProfileDownload}
                  >
                    {this.props.languageShell.t("download")}
                  </a>
                </div>
              )}
              {this.canShowIosAPP() && (
                <div className="item-link iphone-link1">
                  <div className="app-info">
                    <img className="app-icon" src={this.getIcon()} alt="APP" />
                    <div>
                      <h5>{this.props.mcLanguage.t("app_popup_ios")}</h5>
                    </div>
                  </div>
                  <a
                    className="down-btn"
                    rel="nofollow me noopener noreferrer"
                    href={this.state.links["IOS"]}
                    target={this.props.getDownloadTarget(this.state.links["IOS"])}
                  >
                    {this.props.languageShell.t("download")}
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
        <HomeScreenPopup show={this.state.showHomeScreen} onHide={this.closeAll} />
      </div>
    );
  }
}

DownloadPopup.propTypes = {
  show: PropTypes.bool,
  onHide: PropTypes.func,
  hideHomeScreen: PropTypes.bool,
  hideIosApp: PropTypes.bool,
};

DownloadPopup.defaultProps = {
  show: false,
  hideHomeScreen: false,
  hideIosApp: false,
  onHide: () => {},
};

export default DownloadPopup;
