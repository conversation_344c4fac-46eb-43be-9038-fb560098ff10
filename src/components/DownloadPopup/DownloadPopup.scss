.download-bar-model-shell {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10001;
  width: 100%;
  height: 100%;

  &.hide-model {
    display: none;
  }

  .bar-model-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .bar-model-closes {
    position: absolute;
    top: -76px;
    right: 0;
    z-index: 10;
    width: 52px;
    height: 52px;

    .am-icon {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .bar-model-contents {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 635px;
    transform: translate(-50%, -50%);
    animation: zoomShow 0.2s both;

    .bar-model-bg {
      position: relative;
      width: 100%;
      height: 264px;

      .modal-title {
        position: absolute;
        top: 26px;
        left: 20px;
        width: 365px;
        font-size: 73px;
        line-height: 78px;
        color: #fff;
        text-shadow: 0 4px 12px rgb(17 18 18 / 40%);
      }

      .modal-logo {
        position: absolute;
        bottom: 71px;
        left: 40px;
        height: 75px;
      }

      .download-bg {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 100%;
      }
    }

    .appdown-list {
      width: 100%;
      padding: 32px 16px;
      background: #0a0a0a;
      border-radius: 0 0 16px 16px;
    }

    .item-link {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 88px;
      padding: 0 12px;
      background: #383838;
      border-radius: 12px;

      &:not(:last-child) {
        margin-bottom: 32px;
      }

      .app-info {
        display: flex;
        align-items: center;

        h5 {
          font-size: 24px;
          font-weight: 600;
          color: #fff;
        }

        .app-icon {
          width: 64px;
          height: 64px;
          margin-right: 16px;
          border-radius: 10px;
        }
      }

      .down-btn {
        height: 54px;
        padding: 0 24px;
        font-size: 24px;
        font-weight: 600;
        line-height: 54px;
        color: #fff;
        text-align: center;
        white-space: nowrap;
        text-shadow: 0 1px 1px #150f02;
        outline: none;
        background: linear-gradient(180deg, #b5f131 0%, #7db602 46.9%, #aadd1a 100%);
        border: none;
        border-radius: 100px;
      }
    }
  }
}
