.scroll-game-container {
  position: relative;
  width: 100%;
  padding: 0 32px;

  .game-list-title {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    font-size: 32px;
    font-weight: 800;
    line-height: normal;
    color: var(--text-color-accent);

    .title-content {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }

  .game-list-nav {
  }

  .more-game {
    @include flex-center;
    height: 50px;
    padding: 0 16px;
    font-size: 24px;
    font-weight: 400;
    line-height: normal;
    color: var(--text-color-primary);
    letter-spacing: 0.48px;
    background: var(--bg-color-surface);
    border-radius: 8px;

    .game-count {
      margin-left: 10px;
    }
  }

  .game-nav {
    @include flex-center;
    position: absolute;
    top: 50%;
    z-index: 1;
    width: 48px;
    height: 48px;
    color: #fff;
    background: var(--bg-color-menu-active);
    border: none;
    border-radius: 8px;
    transform: translateY(-50%);

    &:disabled {
      background: var(--bg-color-menu-default);
    }

    .am-icon {
      display: block;
      width: 32px;
      height: 32px;
      object-fit: contain;
    }
  }

  .game-prev {
    left: -12px;
  }

  .game-next {
    right: -12px;
  }

  .scroll-game-list-wrap {
    position: relative;
    width: 100%;
  }

  .scroll-game-list {
    --grid-col-width: 220px;
    display: grid;
    grid-template-rows: repeat(var(--grid-rows), 1fr);
    grid-auto-columns: var(--grid-col-width);
    grid-auto-flow: column;
    column-gap: 12px;
    // padding: 0 32px;
    overflow: scroll hidden;
    scroll-snap-type: x mandatory;
    // scroll-padding-inline: 32px;
    scroll-behavior: smooth;

    .scroll-slide {
      scroll-snap-align: start;
    }
  }

  .game-scrollbar-wrap {
    padding: 0 95px;
    margin-top: 30px;
  }

  .game-scrollbar {
    height: 6px;
    background-color: rgb(255 255 255 / 30%);
    border-radius: 5px;

    .swiper-scrollbar-drag {
      background: linear-gradient(to left, #f9edd2, #efcb7a, #b48331);
      border-radius: 5px;
    }
  }
}
