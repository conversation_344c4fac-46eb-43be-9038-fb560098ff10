import React from "react";
import { withRouter } from "react-router-dom";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import { homeScrollTop } from "@/utils/dom";

import "./ScrollGameList.scss";

@inject("languageShell", "gameCenter")
@withRouter
@observer
class ScrollGameList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      canScrollLeft: false,
      canScrollRight: true,
    };
    this.scrollContainerRef = null;
    this.cardsToScroll = props.scrollCount;
  }
  componentDidMount() {
    this.initStyle();
    this.updateNavState();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.rows !== this.props.rows) {
      this.initStyle();
    }
    if (prevProps.children !== this.props.children) {
      this.updateNavState();
    }
  }
  initStyle = () => {
    if (this.scrollContainerRef) {
      this.scrollContainerRef.style.setProperty("--grid-rows", this.props.rows);
    }
  };

  updateNavState = () => {
    window.requestAnimationFrame(() => {
      if (this.scrollContainerRef) {
        const { scrollLeft, scrollWidth, clientWidth } = this.scrollContainerRef;
        this.setState({
          canScrollLeft: scrollLeft > 1,
          canScrollRight: scrollLeft + clientWidth < scrollWidth - 1,
        });
      }
    });
  };
  getCardWidth = () => {
    const columnGap = parseFloat(getComputedStyle(this.scrollContainerRef)?.columnGap) || 0;
    const card = this.scrollContainerRef?.children?.[0];

    return card ? card.offsetWidth + columnGap : 0;
  };
  scrollRight = () => {
    const scrollContainer = this.scrollContainerRef;
    const cardWidth = this.getCardWidth();
    const currentScrollLeft = scrollContainer.scrollLeft;

    const nextPosition = currentScrollLeft + this.cardsToScroll * cardWidth;

    scrollContainer.scrollTo({ left: nextPosition, behavior: "smooth" });
  };

  scrollLeft = () => {
    const scrollContainer = this.scrollContainerRef;
    const cardWidth = this.getCardWidth();
    const currentScrollLeft = scrollContainer.scrollLeft;

    const previousPosition = currentScrollLeft - this.cardsToScroll * cardWidth;

    scrollContainer.scrollTo({ left: previousPosition, behavior: "smooth" });
  };
  moreGame = async () => {
    await this.props.gameCenter.setCurrentGameCategory(this.props.gameType);
    homeScrollTop();
  };
  render() {
    return (
      <div className={cz("scroll-game-container", this.props.className)}>
        <div className="scroll-game-content">
          <div className="game-list-title">
            <div className="title-content">
              <span>{this.props.title}</span>
            </div>
            {this.props.showNav && (
              <div className="more-game" onClick={this.moreGame}>
                <span>{this.props.languageShell.t("see_all")}</span>
              </div>
            )}
          </div>

          <div className="scroll-game-list-wrap">
            {this.props.showNav && (
              <div className="game-list-nav">
                <button
                  key={`game_nav_prev`}
                  className={`game-nav game-prev`}
                  disabled={!this.state.canScrollLeft}
                  onClick={this.scrollLeft}
                >
                  <Icon type={require("!svg-sprite-loader!@/assets/images/common/arrow-left.svg")} />
                </button>
                <button
                  key={`game_nav_next`}
                  className={`game-nav game-next`}
                  disabled={!this.state.canScrollRight}
                  onClick={this.scrollRight}
                >
                  <Icon type={require("!svg-sprite-loader!@/assets/images/common/arrow-right.svg")} />
                </button>
              </div>
            )}
            <div
              className="scroll-game-list hide-scrollbar"
              ref={(c) => (this.scrollContainerRef = c)}
              onScroll={this.updateNavState}
            >
              {React.Children.map(this.props.children, (child, index) =>
                child ? (
                  <div className="scroll-slide" key={child.key || `slide_${index}`}>
                    {child}
                  </div>
                ) : null
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

ScrollGameList.propTypes = {
  className: PropTypes.string,
  gameType: PropTypes.string,
  showNav: PropTypes.bool,
  rows: PropTypes.number,
  scrollCount: PropTypes.number,
};

ScrollGameList.defaultProps = {
  gameType: "",
  showNav: true,
  rows: 1,
  scrollCount: 3,
};

export default ScrollGameList;
