import React from "react";
import dayjs from "dayjs";

import "./CurrentTime.scss";

class CurrentTime extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      currentTime: "",
      currentTimeZone: "",
    };
    this.timer = null;
  }
  componentDidMount() {
    this.getTimeZone();
    this.getCurrentTime();
  }
  componentWillUnmount() {
    clearInterval(this.timer);
  }
  getTimeZone = () => {
    const offsetTime = dayjs().utcOffset();
    this.setState({
      currentTimeZone: offsetTime > 0 ? `+${offsetTime / 60}` : offsetTime / 60,
    });
  };
  getCurrentTime = () => {
    this.setState({
      currentTime: dayjs().format("DD/MM/YYYY HH:mm:ss"),
    });
    this.timer = setInterval(() => {
      this.setState({
        currentTime: dayjs().format("DD/MM/YYYY HH:mm:ss"),
      });
    }, 1000);
  };
  render() {
    return (
      <div className="current-time-wrap">
        <span>{this.state.currentTime}</span>
        <span>{`(GTM${this.state.currentTimeZone})`}</span>
      </div>
    );
  }
}

export default CurrentTime;
