import React from "react";
import cz from "classnames";
import { isEqual } from "lodash";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import MC from "tcgmodulemc";

import { remToPx } from "@/utils/dom";

import "./GameClassifySwiper.scss";

@inject("languageShell")
@observer
class GameClassifySwiper extends React.Component {
  componentDidMount() {
    if (this.props.gameClassifyList.length) {
      this.initSwiper();
    }
    this.props.setRef && this.props.setRef(this);
  }
  componentDidUpdate(prevProps, prevState) {
    if (!isEqual(prevProps.gameClassifyList, this.props.gameClassifyList)) {
      this.initSwiper();
    }
  }
  componentWillUnmount() {
    this.swiper?.destroy?.();
  }
  initSwiper = () => {
    if (this.swiper) {
      this.swiper?.destroy(true, true);
    }
    this.swiper = new MC.Swiper(this.menuSwiper, {
      observer: true,
      observeParents: true,
      freeMode: true,
      freeModeMomentumRatio: 0.5,
      slidesPerView: "auto",
      slidesPerGroup: 3,
      spaceBetween: remToPx(0.3),
      navigation: {
        nextEl: this.nextNav,
        prevEl: this.prevNav,
      },
    });
  };
  tabChange = (item, index) => {
    this.props.handleClick({ gameClassify: item.key });
    this.moveToCenter(index);
  };
  moveToCenter = (index) => {
    if (this.swiper) {
      const swiperWidth = this.swiper.width;
      const maxTranslate = this.swiper.maxTranslate();
      const maxWidth = -maxTranslate + swiperWidth / 2;
      const slide = this.swiper.slides[index];
      let slideLeft = slide.offsetLeft;
      let slideWidth = slide.clientWidth;
      let slideCenter = slideLeft + slideWidth / 2;

      this.swiper.setTransition(200);

      if (slideCenter < swiperWidth / 2) {
        this.swiper.setTranslate(0);
      } else if (slideCenter > maxWidth) {
        this.swiper.setTranslate(maxTranslate);
      } else {
        const nowTlanslate = slideCenter - swiperWidth / 2;
        this.swiper.setTranslate(-nowTlanslate);
      }
    }
  };
  render() {
    return (
      <div className="classify-swiper-wrap">
        {/* <div className="menu-nav nav-prev" ref={(e) => (this.prevNav = e)}>
          <Icon type={require("!svg-sprite-loader!@/assets/images/common/nav-left.svg")} />
        </div>
        <div className="menu-nav nav-next" ref={(e) => (this.nextNav = e)}>
          <Icon type={require("!svg-sprite-loader!@/assets/images/common/nav-right.svg")} />
        </div> */}
        <div className="swiper-container  classify-menu-swiper" ref={(c) => (this.menuSwiper = c)}>
          <div className="swiper-wrapper">
            {this.props.gameClassifyList.map((item, idx) => {
              return (
                <div
                  key={`classify_menu_${idx}`}
                  className={`classify-menu-item swiper-slide ${cz({
                    on: this.props.gameClassify === item.key,
                  })}`}
                  onClick={() => this.tabChange(item, idx)}
                >
                  <div className="classify-menu-name">{this.props.languageShell.t(item.displayName)}</div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  }
}

GameClassifySwiper.propTypes = {
  handleClick: PropTypes.func.isRequired,
  gameClassifyList: PropTypes.array.isRequired,
  gameClassify: PropTypes.string.isRequired,
};

GameClassifySwiper.defaultProps = {
  gameClassifyList: [],
};

export default GameClassifySwiper;
