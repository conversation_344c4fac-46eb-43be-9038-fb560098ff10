import React, { Component } from "react";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import deployConfig from "@/config/deploy.config";
import { SimpleSelect } from "@/ui/SimpleSelect";

import "./GameClassifySelect.scss";

const { gameSelectType } = deployConfig;

@inject("languageShell", "tcgCommon", "gameCenter")
@observer
class GameClassifySelect extends Component {
  get allGameCount() {
    return this.props.tcgCommon.gameVendor.mapping[this.props.gameType]?.gameCount;
  }

  get gameClassify() {
    if (!this.props.gameType) return [];
    const list = [
      { key: gameSelectType.ALL, displayName: "in_type_all", gameCount: this.allGameCount },
      // { key: gameSelectType.FAV, displayName: "in_favorite" },
      // { key: gameSelectType.NEW, displayName: "game_new" },
      // { key: gameSelectType.HOT, displayName: "in_hot_games" },
    ];
    const data = this.props.tcgCommon.gameVendor.mapping[this.props.gameType]?.gameClassify;
    if (data && data.length > 1) {
      list.push(...data);
    }
    return list;
  }

  get filterGameClassify() {
    const filterGame = this.gameClassify.filter((item) =>
      this.props.languageShell.t(item.displayName)?.toLowerCase()?.includes(this.state.classifyName.toLowerCase())
    );
    if (filterGame?.length > 0) {
      return filterGame;
    }
    return this.gameClassify;
  }

  get activeName() {
    const activeItem = this.gameClassify.find((item) => item.key === this.props.gameClassify);
    if (activeItem) {
      return this.props.languageShell.t(activeItem.displayName);
    }
    return "";
  }

  get activeGameCount() {
    const activeItem = this.gameClassify.find((item) => item.key === this.props.gameClassify);
    if (activeItem) {
      return this.props.languageShell.t(activeItem.gameCount);
    }
    return "";
  }
  constructor(props) {
    super(props);
    this.listRef = {};
    this.state = {
      classifyName: "",
    };
  }

  componentDidMount() {}

  componentDidUpdate(prevProps, prevState) {}

  componentWillUnmount() {}

  handleActive = (item) => {
    const { key } = item;
    this.props.menuClick({ gameClassify: key });
  };

  onChange = (key) => {
    this.props.menuClick({ gameClassify: key });
    this.gameSelect?.close();
  };

  handleClean = () => {
    this.props.menuClick({ gameClassify: "" });
  };

  getSelected = () => {
    if (this.props.gameClassify) {
      return (
        <div className="game-selected">
          <div className="selected-name">
            <Icon
              className="selected-icon"
              type={require("!svg-sprite-loader!@/assets/images/common/icon-theme.svg")}
            />
            <span>{this.activeName}</span>
            {/* <span className="game-count">{this.activeGameCount}</span> */}
          </div>
          <Icon
            className="close-select"
            type={require("!svg-sprite-loader!@/assets/images/common/icon-close.svg")}
            onClick={this.handleClean}
          />
        </div>
      );
    }
    return (
      <div className="game-selected">
        <div className="selected-name">
          <Icon className="selected-icon" type={require("!svg-sprite-loader!@/assets/images/common/icon-theme.svg")} />
          <span>{this.props.languageShell.t("game_category")}</span>
        </div>
      </div>
    );
  };

  setGameName = (e) => {
    const name = e.target.value;
    this.setState({ classifyName: name });
  };

  render() {
    if (!this.gameClassify.length) {
      return null;
    }
    return (
      <div className={cz("game-select-wrap game-classify-select", { "value-selected": this.props.gameClassify })}>
        <SimpleSelect
          setRef={(c) => (this.gameSelect = c)}
          value={this.props.gameClassify}
          selected={this.getSelected()}
          onChange={() => {}}
        >
          {/* <div className="game-search-box">
            <Icon
              className="search-icon"
              type={require("!svg-sprite-loader!@/assets/images/common/icon-search.svg")}
            />
            <input
              className="search-input"
              type="text"
              autoComplete="off"
              placeholder={this.props.languageShell.t("theme")}
              value={this.state.classifyName}
              onInput={this.setGameName}
            />
          </div> */}
          {this.filterGameClassify.map((item, index) => {
            return (
              <div
                className="game-classify-item"
                key={`classify_select_${index}`}
                onClick={() => this.onChange(item.key)}
              >
                <span className="category-name">{this.props.languageShell.t(item.displayName)}</span>
                {/* <span className="game-count">{item.gameCount}</span> */}
              </div>
            );
          })}
        </SimpleSelect>
      </div>
    );
  }
}

GameClassifySelect.propTypes = {
  gameType: PropTypes.string.isRequired, // RNG, LIVE ...
  gameClassify: PropTypes.string.isRequired,
  menuClick: PropTypes.func.isRequired,
};

export default GameClassifySelect;
