.game-classify-popup {
  .classify-popup-mask {
    position: fixed;
    top: 0;
    left: 0;
    z-index: $side-mask-index;
    width: 100%;
    height: 100vh;
    height: calc(100 * var(--vh, 1vh));
  }

  .classify-popup-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: $side-menu-index;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 69%;
    overflow-y: auto;
    background: #000;
    border-radius: 32px 32px 0 0;
  }

  .classify-header {
    position: relative;
    flex-shrink: 0;
    height: 112px;
    padding: 0 24px;
    border-bottom: 2px solid rgb(255 255 255 / 10%);

    @include flex-center;

    h6 {
      font-size: 38px;
      font-weight: 700;
      line-height: normal;
      color: #fff;
      text-align: center;
    }

    .close-pop {
      position: absolute;
      top: 32px;
      right: 24px;
      width: 48px;
      height: 48px;
      color: #fff;
    }
  }

  .game-classify-content {
    flex: 1;
    overflow: auto;
  }

  .game-classify-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px 17px;
    padding: 32px 40px;
  }

  .classify-menu-item {
    @include flex-center;
    height: 80px;
    font-size: 24px;
    font-weight: 600;
    line-height: normal;
    color: rgb(255 255 255 / 50%);
    text-align: center;
    background: #292621;
    border-radius: 24px;

    &.item-active {
      color: #f8ff13;
      background: #54514d;
      border: 2px solid #f8ff13;
      border-radius: 24px;
    }
  }
}

.popup-slide-enter {
  transform: translateY(110%);
}

.popup-slide-enter-active {
  transform: translateY(0);
  transition: all 250ms ease-out;
}

.popup-slide-exit {
  transform: translateY(0);
}

.popup-slide-exit-active {
  transform: translateY(110%);
  transition: all 250ms ease-out;
}
