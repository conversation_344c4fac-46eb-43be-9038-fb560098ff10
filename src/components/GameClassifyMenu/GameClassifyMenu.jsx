import React, { Component } from "react";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import deployConfig from "@/config/deploy.config";
import { scrollLeftTo } from "@/utils/scroll";

import "./GameClassifyMenu.scss";

const { gameSelectType } = deployConfig;

@inject("languageShell", "tcgCommon", "gameCenter")
@observer
class GameClassifyMenu extends Component {
  get gameClassify() {
    if (!this.props.gameType) return [];
    const list = [
      { key: "", displayName: "in_type_all" },
      // { key: gameSelectType.FAV, displayName: "in_favorite" },
      // { key: gameSelectType.RECENT, displayName: "in_recent_game" },
      { key: gameSelectType.NEW, displayName: "game_new" },
      // { key: gameSelectType.HOT, displayName: "hot" },
    ];
    const data = this.props.tcgCommon.gameVendor.mapping[this.props.gameType]?.gameClassify || [];
    const results = data.filter((item) => (this.props.vassalage ? item.vendors.includes(this.props.vassalage) : true));
    if (results && results.length > 0) {
      list.push(...results);
    }
    return list;
  }

  menuGame = {};

  changeClassify = (item, index) => {
    const { key } = item;
    this.props.menuClick({ gameClassify: key });
    this.scrollLeftCenter(index);
  };

  handleClick = (item) => {
    this.props.menuClick(item);
  };

  scrollLeftCenter = (index) => {
    const el = this.menuGame[index];
    if (el) {
      const containerWidth = this.menuRef.offsetWidth;
      const itemOffsetLeft = el.offsetLeft;
      const scrollPosition = itemOffsetLeft - containerWidth / 2 + el.offsetWidth / 2;
      scrollLeftTo(this.menuRef, scrollPosition, 300);
    }
  };

  render() {
    return (
      // <GameClassifySwiper
      //   gameClassifyList={this.gameClassify}
      //   gameClassify={this.props.gameClassify}
      //   handleClick={this.handleClick}
      // />
      <div className="game-classify-wrap">
        <div className="game-classify-scroll hide-scrollbar" ref={(c) => (this.menuRef = c)}>
          {this.gameClassify.map((item, index) => (
            <div
              className={cz("classify-menu-item", { "item-active": item.key === this.props.gameClassify })}
              onClick={() => this.changeClassify(item, index)}
              key={`classify_${index}`}
              ref={(e) => (this.menuGame[index] = e)}
            >
              <span>{this.props.languageShell.t(item.displayName)}</span>
            </div>
          ))}
        </div>
      </div>
    );
  }
}

GameClassifyMenu.propTypes = {
  gameType: PropTypes.string.isRequired, // RNG, LIVE ...
  gameClassify: PropTypes.string.isRequired,
  vassalage: PropTypes.string,
  menuClick: PropTypes.func.isRequired,
};

GameClassifyMenu.defaultProps = {
  vassalage: "",
};

export default GameClassifyMenu;
