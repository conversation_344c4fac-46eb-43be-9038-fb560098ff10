import React from "react";
import { CSSTransition } from "react-transition-group";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import deployConfig from "@/config/deploy.config";

import "./GameClassifyPop.scss";

const { gameSelectType } = deployConfig;

@inject("languageShell", "common", "gameCenter", "tcgCommon")
@observer
class GameClassifyPop extends React.Component {
  get gameClassify() {
    if (!this.props.gameType) return [];
    const list = [
      { key: "", displayName: "in_type_all" },
      { key: gameSelectType.NEW, displayName: "game_new" },
      { key: gameSelectType.HOT, displayName: "hot" },
      { key: gameSelectType.RECENT, displayName: "in_recent_game" },
      { key: gameSelectType.FAV, displayName: "in_favorite" },
    ];
    const data = this.props.tcgCommon.gameVendor.mapping[this.props.gameType]?.gameClassify || [];
    const results = data.filter((item) => (this.props.vassalage ? item.vendors.includes(this.props.vassalage) : true));
    if (results && results.length > 0) {
      list.push(...results);
    }
    return list;
  }
  handleActive = (e, item) => {
    e.stopPropagation();
    this.props.handleSelect({ gameClassify: item.key });
    this.props.handleClose();
  };
  render() {
    return (
      <div className="game-classify-popup">
        <CSSTransition in={this.props.show} timeout={250} classNames={"popup-fade"} unmountOnExit={true}>
          <div className="classify-popup-mask" onClick={this.props.handleClose} />
        </CSSTransition>
        <CSSTransition in={this.props.show} timeout={250} classNames={"popup-slide"} unmountOnExit={true}>
          <div className="classify-popup-wrap">
            <div className="classify-header">
              <h6>{this.props.languageShell.t("theme")}</h6>
              <Icon
                className="close-pop"
                onClick={this.props.handleClose}
                type={require("!svg-sprite-loader!@/assets/images/common/btn-close.svg")}
              />
            </div>
            <div className="game-classify-content">
              <div className="game-classify-list" ref={(c) => (this.classifyMenu = c)}>
                {this.gameClassify.map((item, index) => (
                  <div
                    className={cz("classify-menu-item", { "item-active": item.key === this.props.gameClassify })}
                    onClick={(e) => this.handleActive(e, item)}
                    key={`classify_pop_${index}`}
                  >
                    <span>{this.props.languageShell.t(item.displayName)}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CSSTransition>
      </div>
    );
  }
}

GameClassifyPop.propTypes = {
  show: PropTypes.bool,
  gameType: PropTypes.string,
  gameClassify: PropTypes.string.isRequired,
  vassalage: PropTypes.string,
  handleSelect: PropTypes.func.isRequired,
  handleClose: PropTypes.func.isRequired,
};

GameClassifyPop.defaultProps = {
  gameType: "",
  show: false,
  handleSelect: () => {},
  handleClose: () => {},
};

export default GameClassifyPop;
