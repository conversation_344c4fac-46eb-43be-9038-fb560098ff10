.game-classify-wrap {
  position: relative;
  width: 100%;
  margin-top: 10px;
  background: linear-gradient(0deg, #2d2d2d 0%, #2d2d2d 100%), linear-gradient(90deg, #fee93c 0%, #faa83c 100%);
  border: 0.798px solid #505050;
  border-radius: 7.979px;

  .game-classify-scroll {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 8px;
    overflow: auto hidden;
  }

  .classify-menu-item {
    @include flex-center;
    position: relative;
    flex-shrink: 0;
    min-width: 60px;
    height: 40px;
    font-size: 30px;
    font-weight: 500;
    color: #b0b0b0;
    text-align: center;
    white-space: nowrap;

    &:not(:last-child) {
      margin-right: 20px;
    }

    &.item-active {
      min-width: 80px;
      padding: 0 25px;
      font-size: 32px;
      color: #fff;
      border-radius: 10px;
    }
  }

  .classify-icon {
    display: block;
    width: 48px;
    height: 48px;
    margin-right: 8px;
  }
}
