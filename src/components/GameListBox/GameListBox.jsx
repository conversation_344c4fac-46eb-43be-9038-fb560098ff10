import React from "react";
import { withRouter } from "react-router";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import GameListItem from "@/components/GameListItem/GameListItem";
import NoData from "@/components/NoData/NoData";
import Skeleton from "@/components/Skeleton/Skeleton";
import deploy from "@/config/deploy.config";

import LoadMoreBtn from "../LoadMoreBtn/LoadMoreBtn";

import "./GameListBox.scss";

const PAGE_SIZE = 24;

const { gameSelectType } = deploy;

@inject("languageShell", "common", "gameCenter", "auth")
@withRouter
@observer
class GameListBox extends React.Component {
  get isLogin() {
    return this.props.auth.isLogin;
  }
  get activeGameList() {
    if (this.state.dataType === gameSelectType.FAV) {
      return this.props.gameCenter.favGameList.content;
    } else if (this.state.dataType === gameSelectType.HOT) {
      return this.state.gameType
        ? this.props.gameCenter.hotGames.filter((item) => item.gameType === this.state.gameType)
        : this.props.gameCenter.hotGames;
    }
    return this.state.gameList;
  }
  get gameTotalCount() {
    if (this.state.dataType === gameSelectType.HOT) {
      return this.activeGameList?.length;
    }
    if ([gameSelectType.FAV, gameSelectType.RECENT].includes(this.state.dataType)) {
      return 0;
    }
    return this.state.totalCount;
  }
  constructor(props) {
    super(props);
    this.state = {
      dataType: props.dataType,
      gameList: [],
      gameType: props.gameType,
      vassalage: "",
      gameClassify: "",
      minBet: "",
      gameName: props.gameName,
      currentPage: 1,
      totalPages: 1,
      hasMore: false,
      totalCount: 0,
      noData: false,
      skeleton: false,
      loading: false,
    };
  }
  componentDidMount() {
    this.initGame();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.gameName !== this.props.gameName) {
      this.handleSearchByName(this.props.gameName);
    }
  }
  initGame() {
    if (this.props.dataType === gameSelectType.SEARCH && !this.props.gameName) {
      return this.setState({
        noData: true,
      });
    }
    return this.filterClick({
      gameType: this.props.gameType,
      dataType: this.props.dataType,
      gameName: this.props.gameName,
    });
  }
  handleSearchByName = (gameName) => {
    this.setState(
      {
        gameList: [],
        gameName,
        hasMore: false,
        currentPage: 1,
        totalPages: 1,
        noData: !gameName,
      },
      () => {
        if (gameName) {
          this.getGameList(true, true);
        }
      }
    );
  };
  filterClick = (item = {}) => {
    const {
      vassalage = this.state.vassalage,
      gameType = this.state.gameType,
      dataType = this.state.dataType,
      gameClassify = this.state.gameClassify,
      minBet = "",
      minLine = "",
      gameName = this.state.gameName,
    } = item;

    this.setState(
      {
        gameType,
        vassalage,
        gameClassify,
        gameList: [],
        dataType,
        currentPage: 1,
        totalPages: 1,
        minBet,
        minLine,
        gameName,
        hasMore: false,
        noData: false,
      },
      () => {
        this.getData(true, true);
      }
    );
  };
  getRecentGame = (refresh, loading) => {
    if (!this.isLogin) {
      return this.setState({
        noData: true,
      });
    }
    if (loading) {
      this.setState({
        skeleton: true,
      });
    }
    this.props.gameCenter
      .getRecentGameList({
        page: this.state.currentPage || 1,
        pageSize: PAGE_SIZE,
        gameType: this.state.gameType,
        vendor: this.state.vassalage,
        language: this.props.languageShell.language,
        clientType: 2,
      })
      .then((res) => {
        if (refresh) {
          this.setState({
            gameList: res.list,
            totalPages: res.totalPages,
          });
        } else {
          this.setState((state) => ({
            gameList: state.gameList.concat(res.list),
            totalPages: res.totalPages,
          }));
        }
        const hasMore = this.state.currentPage < res.totalPages;
        this.setState({ hasMore, skeleton: false, noData: this.activeGameList?.length < 1 });
      })
      .finally(() => {
        this.setState({
          skeleton: false,
          loading: false,
        });
      });
  };

  getFavGameList = (refresh, loading = false) => {
    if (!this.isLogin) {
      return this.setState({
        noData: true,
      });
    }
    if (loading) {
      this.setState({
        skeleton: true,
      });
    }
    this.props.gameCenter
      .getFavGameList(
        {
          pageNo: this.state.currentPage,
          gameType: this.state.gameType,
          vendor: this.state.vassalage,
          pageSize: PAGE_SIZE,
          language: this.props.languageShell.currentLanguage,
        },
        !refresh
      )
      .then((res) => {
        const { totalPages, pageNum } = res;
        const hasMore = pageNum < totalPages;
        this.setState({ hasMore, totalPages, skeleton: false, noData: this.activeGameList?.length < 1 });
      })
      .finally(() => {
        this.setState({
          skeleton: false,
          loading: false,
        });
      });
  };
  getGameList = (refresh, loading = false) => {
    if (!this.state.hasMore || loading) {
      this.setState({
        skeleton: true,
      });
    }
    this.props.gameCenter
      .getGameList({
        merchant: deploy.merchant,
        platform: "html5",
        gameName: this.state.gameName,
        gameType: "",
        pageNo: this.state.currentPage,
        pageSize: PAGE_SIZE,
        vassalage: "",
        gameClassify: "",
        minBet: "",
        minLine: "",
        isNew: "",
        language: this.props.languageShell.language,
      })
      .then((res) => {
        if (refresh) {
          this.setState({
            gameList: res.games,
            currentPage: res.pageNum,
          });
        } else {
          this.setState((state) => ({
            gameList: state.gameList.concat(res.games),
            currentPage: res.pageNum,
          }));
        }
        const { totalPages, pageNum, totalCount } = res;
        const hasMore = pageNum < totalPages;
        this.setState({ hasMore, totalCount: totalCount, totalPages, noData: this.activeGameList?.length < 1 });
      })
      .finally(() => {
        this.setState({
          skeleton: false,
          loading: false,
        });
      });
  };
  loadMore = () => {
    this.setState(
      {
        currentPage: this.state.currentPage + 1,
      },
      () => {
        this.getData(false);
      }
    );
  };
  pageChange = (page) => {
    this.setState(
      {
        currentPage: page,
      },
      () => {
        this.getData(true, true);
      }
    );
  };
  getData = (refresh, loading) => {
    switch (this.state.dataType) {
      case gameSelectType.FAV:
        return this.getFavGameList(refresh, loading);
      case gameSelectType.RECENT:
        return this.getRecentGame(refresh, loading);
      case gameSelectType.HOT:
        this.setState({
          skeleton: false,
          loading: false,
          noData: this.activeGameList?.length < 1,
        });
        return null;
      case gameSelectType.SEARCH:
        return this.getGameList(refresh, loading);
      default:
        return null;
    }
  };
  render() {
    return (
      <div className="game-list-box-wrap">
        <div className="category-games">
          {this.state.skeleton ? (
            <Skeleton size={PAGE_SIZE} />
          ) : (
            <div className="category-game-list">
              {this.activeGameList.map((item) => {
                return <GameListItem key={`${item.gameType}_${item.nodeId}`} item={item} />;
              })}
            </div>
          )}

          {this.state.noData && <NoData />}
        </div>
        {this.state.hasMore && (
          <LoadMoreBtn
            totalCount={this.gameTotalCount}
            hasMore={this.state.hasMore}
            count={this.activeGameList.length}
            loadMore={this.loadMore}
          />
        )}
        {/* <Pagination
          totalPages={this.state.totalPages}
          currentPage={this.state.currentPage}
          onChange={this.pageChange}
        /> */}
      </div>
    );
  }
}

GameListBox.propTypes = {
  className: PropTypes.string,
  gameType: PropTypes.string.isRequired,
  dataType: PropTypes.string,
  gameName: PropTypes.string,
};

GameListBox.defaultProps = {
  className: "",
  gameType: "", // RNG LIVE...
  dataType: "",
  gameName: "",
};

export default GameListBox;
