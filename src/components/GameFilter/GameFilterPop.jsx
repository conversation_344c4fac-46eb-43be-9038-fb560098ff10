import React from "react";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import deployConfig from "@/config/deploy.config";
import { coverVassalage } from "@/config/game.config";
import { Accordion, AccordionItem } from "@/ui/Accordion";
import CheckBox from "@/ui/CheckBox";
import CommonPopup from "@/ui/CommonPopup";

import "./GameFilterPop.scss";

const { gameSelectType } = deployConfig;

@inject("languageShell", "common", "gameCenter", "tcgCommon")
@observer
class GameFilterPop extends React.Component {
  get currentVendors() {
    const data = this.props.gameCenter.gameVendor.mapping[this.props.gameType] || [];
    if (this.state.filterName) {
      return data.filter((item) => item.displayName.toLowerCase().indexOf(this.state.filterName) > -1);
    }
    return data;
  }
  get gameClassify() {
    if (!this.props.gameType) return [];
    const list = [
      // { key: "", displayName: "in_type_all" },
      { key: gameSelectType.HOT, displayName: "hot" },
      { key: gameSelectType.NEW, displayName: "game_new" },
      { key: gameSelectType.FAV, displayName: "in_favorite" },
      { key: gameSelectType.RECENT, displayName: "in_recent_game" },
    ];
    const data = this.props.tcgCommon.gameVendor.mapping[this.props.gameType]?.gameClassify || [];
    if (data && data.length > 0) {
      list.push(...data);
    }
    if (this.state.filterName) {
      return list.filter((item) => item.displayName.toLowerCase().indexOf(this.state.filterName) > -1);
    }
    return list;
  }
  state = {
    vassalage: "",
    gameClassify: "",
    filterName: "",
    gameName: "",
  };
  componentDidMount() {
    if (this.props.vassalage) {
      this.setState({
        vassalage: this.props.vassalage,
      });
    }
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.vassalage !== this.props.vassalage) {
      this.setState({
        vassalage: this.props.vassalage,
      });
    }
    if (prevProps.gameClassify !== this.props.gameClassify) {
      this.setState({
        gameClassify: this.props.gameClassify,
      });
    }
    if (prevProps.gameName !== this.props.gameName) {
      this.setState({
        gameName: this.props.gameName,
      });
    }
  }
  handleChangeClassify = (e, item) => {
    e.stopPropagation();
    this.setState({
      gameClassify: item.key === this.state.gameClassify ? "" : item.key,
    });
  };
  handleChangeVassalage = (e, item) => {
    e.stopPropagation();
    this.setState({
      vassalage: item.vassalage === this.state.vassalage ? "" : item.vassalage,
    });
  };
  changeProvider = (value, item) => {
    const { vassalage } = item;
    this.setState({
      vassalage: value ? vassalage : "",
    });
  };
  changeClassify = (value, item) => {
    const { key } = item;
    this.setState({
      gameClassify: value ? key : "",
    });
  };
  handleSearchByName = (name = "") => {
    this.setState({
      gameName: name.toLowerCase(),
    });
  };
  handleReset = () => {
    this.setState(
      {
        vassalage: "",
        gameClassify: "",
        gameName: "",
      },
      () => {
        // this.handleSubmit();
      }
    );
  };
  handleSubmit = () => {
    this.props.handleSelect({
      vassalage: this.state.vassalage,
      gameClassify: this.state.gameClassify,
      gameName: this.state.gameName,
    });
    this.props.handleClose();
  };
  render() {
    return (
      <CommonPopup
        className="game-filter-popup"
        closeable={false}
        position="right"
        show={this.props.show}
        onClose={this.props.handleClose}
      >
        <div className="filter-popup-content">
          <div className="filter-header">
            <h6>{this.props.languageShell.t("filter")}</h6>
            <div className="close-pop" onClick={this.props.handleClose}>
              <Icon className="icon-close" type={require("!svg-sprite-loader!@/assets/images/common/icon-close.svg")} />
            </div>
          </div>
          <div className="game-filter-content">
            {/* <div className="filter-search">
              <SearchInput searchGame={this.handleSearchByName} gameName={this.state.gameName} />
            </div> */}
            {this.currentVendors?.length > 0 && (
              <Accordion className="common-accordion" activeKey="">
                <AccordionItem data-key="filter-accordion-1" className="game-filter-group">
                  <div className="common-accordion-header">
                    <div className="header-info">
                      <span>{this.props.languageShell.t("game_providers")}</span>
                    </div>
                    <Icon
                      className="arrow-down"
                      type={require("!svg-sprite-loader!@/assets/images/common/arrow-down.svg")}
                    />
                  </div>
                  <div className="filter-nav-list">
                    {this.currentVendors.map((item, index) => {
                      return (
                        <div key={`vassalage_filter_${index}`} className="filter-nav-item">
                          <CheckBox
                            checked={this.state.vassalage === item.vassalage}
                            onChange={(value) => this.changeProvider(value, item)}
                          >
                            <img
                              className="provider-icon"
                              src={`${this.props.common.vendorIconCDN}${coverVassalage(item.vassalage)}-COLOR.png`}
                              alt=""
                            />
                            <span>{item.displayName}</span>
                          </CheckBox>
                        </div>
                      );
                    })}
                  </div>
                </AccordionItem>
                <AccordionItem data-key="filter-accordion-2" className="game-filter-group">
                  <div className="common-accordion-header">
                    <div className="header-info">
                      <span>{this.props.languageShell.t("game_type")}</span>
                    </div>
                    <Icon
                      className="arrow-down"
                      type={require("!svg-sprite-loader!@/assets/images/common/arrow-down.svg")}
                    />
                  </div>
                  <div className="filter-nav-list">
                    {this.gameClassify.map((item, index) => {
                      return (
                        <div key={`classify_filter_${index}`} className="filter-nav-item">
                          <CheckBox
                            checked={this.state.gameClassify === item.key}
                            onChange={(value) => this.changeClassify(value, item)}
                          >
                            <span>{this.props.languageShell.t(item.displayName)}</span>
                          </CheckBox>
                        </div>
                      );
                    })}
                  </div>
                </AccordionItem>
              </Accordion>
            )}
          </div>
          <div className="game-filter-bottom">
            <div className="filter-btn btn-reset" onClick={this.handleReset}>
              <span>{this.props.languageShell.t("btn_reset")}</span>
            </div>
            <div className="filter-btn btn-submit" onClick={this.handleSubmit}>
              <span>{this.props.languageShell.t("filter")}</span>
            </div>
          </div>
        </div>
      </CommonPopup>
    );
  }
}

GameFilterPop.propTypes = {
  show: PropTypes.bool,
  gameType: PropTypes.string,
  gameName: PropTypes.string,
  gameClassify: PropTypes.string,
  vassalage: PropTypes.string,
  handleSelect: PropTypes.func.isRequired,
  handleClose: PropTypes.func.isRequired,
};

GameFilterPop.defaultProps = {
  gameType: "",
  gameClassify: "",
  vassalage: "",
  gameName: "",
  show: false,
  handleSelect: () => {},
  handleClose: () => {},
};

export default GameFilterPop;
