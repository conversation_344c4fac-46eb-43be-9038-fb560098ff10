.game-filter-popup {
  top: 0;
  width: 100%;

  .filter-popup-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(var(--vh, 1vh) * 100);
    overflow: hidden;
    background: #141515;
  }

  .filter-header {
    position: relative;
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    height: 100px;
    padding: 0 30px;
    background: #141515;
    border-bottom: 2px solid #454745;

    h6 {
      font-size: 38px;
      font-weight: 700;
      line-height: normal;
      color: #fff;
    }

    .close-pop {
      color: #fff;

      .icon-close {
        display: block;
        width: 40px;
        height: 40px;
        object-fit: contain;
      }
    }
  }

  .game-filter-content {
    flex: 1;
    overflow: auto;
  }

  .filter-search {
    margin-bottom: 32px;
  }

  .filter-title {
    margin-bottom: 20px;
    font-size: 32px;
    font-weight: 500;
    line-height: 48px;
    color: #000;
  }

  .game-filter-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }

  .game-filter-group {
    padding: 32px;
    font-size: 28px;
    line-height: normal;
    color: #8d9aa5;
    border-bottom: 2px solid rgb(255 255 255 / 15%);

    .filter-nav-item {
      display: flex;
      align-items: center;
      height: 88px;
    }

    .checkbox-label {
      display: flex;
      align-items: center;
    }
  }

  .provider-icon {
    display: block;
    width: 52px;
    height: 26px;
    margin-right: 16px;
    object-fit: contain;
  }

  .filter-item {
    @include flex-center;
    height: 80px;
    font-size: 28px;
    line-height: normal;
    color: #fff;
    text-align: center;
    background: rgb(41 171 226 / 50%);
    border-radius: 10px;

    &.item-active {
      background: var(--Linear, linear-gradient(180deg, #00b3ff 0%, #1a8ce5 100%));
      border-radius: 10px;

      .icon-normal {
        display: none;
      }

      .icon-active {
        display: block;
      }
    }

    .item-icon {
      width: 120px;
      height: 60px;
      object-fit: contain;
    }

    .icon-active {
      display: none;
    }
  }

  .game-filter-bottom {
    display: flex;
    flex-shrink: 0;
    gap: 32px;
    padding: 32px;

    .filter-btn {
      @include flex-center;
      height: 100px;
      font-size: 28px;
      line-height: normal;
      color: #fff;
      text-align: center;
      border-radius: 6px;
    }

    .btn-reset {
      flex-shrink: 0;
      width: 272px;
      color: #8d9aa5;
      border: 2px solid #454745;
    }

    .btn-submit {
      flex: 1;
      background: #14805e;
    }
  }
}

.popup-slide-enter {
  transform: translateY(110%);
}

.popup-slide-enter-active {
  transform: translateY(0);
  transition: transform 225ms cubic-bezier(0, 0, 0.2, 1);
}

.popup-slide-exit {
  transform: translateY(0);
}

.popup-slide-exit-active {
  transform: translateY(110%);
  transition: transform 225ms cubic-bezier(0, 0, 0.2, 1);
}
