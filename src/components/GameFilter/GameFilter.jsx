import React from "react";
import { with<PERSON>outer } from "react-router-dom";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";

import deploy from "../../config/deploy.config";

import "./GameFilter.scss";

// const searchIcon = require("!svg-sprite-loader!@/assets/images/common/icon-search.svg");
const close = require("!svg-sprite-loader!@/assets/images/common/popup-close.svg");

const gameName = deploy.gameName;

const categoryList = [
  { caId: "", name: "in_type_all" },
  { caId: "JP", name: "in_accumulative_prize" },
  { caId: "SC", name: "in_scratch_music" },
  { caId: "VP", name: "in_video_poker" },
  { caId: "TG", name: "P<PERSON>" },
  { caId: "AC", name: "in_arcade_games" },
  { caId: "BG", name: "in_bingo" }, // only TY brand
];

const minbetList = [
  { bet: "", name: "in_type_all" },
  { bet: 0.01, name: "0.01" },
  { bet: 0.05, name: "0.05" },
  { bet: 0.1, name: "0.1" },
  { bet: 0.5, name: "0.5" },
  { bet: 0.75, name: "0.75" },
  { bet: 1, name: "in_minimum_bet" },
];

const minLineList = [
  { line: "", name: "in_type_all" },
  { line: 10, name: "10" },
  { line: 15, name: "15" },
  { line: 20, name: "20" },
  { line: 25, name: "in_lines_more" },
];

@inject("languageShell", "gameCenter", "mcCommon", "common")
@withRouter
@observer
class GameFilter extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      gameClassify: "",
      minBet: "",
      minLine: "",
      vassalage: "",
      gameName: "",
    };
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.vassalage !== this.props.vassalage) {
      this.setState({
        vassalage: this.props.vassalage,
      });
    }
  }
  setGameName = (event) => {
    this.setState({ gameName: event.target.value });
  };
  setVassalage = (vassalage) => {
    this.setState({
      vassalage,
    });
  };
  setGameClassify = (gameClassify) => {
    this.setState({
      gameClassify,
    });
  };
  setMinBet = (minBet) => {
    this.setState({
      minBet,
    });
  };
  setMinLine = (minLine) => {
    this.setState({
      minLine,
    });
  };
  onSubmit = () => {
    this.props.filterGame({
      gameClassify: this.state.gameClassify,
      minBet: this.state.minBet,
      minLine: this.state.minLine,
      vassalage: this.state.vassalage,
      gameName: this.state.gameName,
      filterType: this.props.filterType,
    });
  };
  changeGameType = (gameType) => {
    if (gameType === "LOTT") {
      this.props.history.replace(`/m/lottery`);
    } else {
      this.props.history.replace(`/m/gameList?gameType=${gameType}`);
    }
  };
  get otherGame() {
    const { sorting = [] } = this.props.mcCommon.gameVendor;
    return sorting.filter((item) => !["ELOTT"].includes(item.gameCategory));
  }
  render() {
    return (
      <div className={`filter-game-container ${this.props.open ? "open" : ""}`}>
        <div className="filter_title">
          {this.props.filterType === "vendor"
            ? this.props.languageShell.t("in_game_category")
            : this.props.languageShell.t("filter")}
        </div>
        <Icon type={close} className="close_fiflter" onClick={this.props.close} />
        {/* <div className="search-game">
          <Icon className="search-icon" type={searchIcon} onClick={this.searchGame} />
          <input
            className="search-input"
            type="text"
            autoComplete="off"
            placeholder={this.props.languageShell.resource["in_slot_search"]}
            value={this.state.gameName}
            onChange={this.setGameName}
          />
        </div> */}
        {this.props.filterType === "vendor" && (
          <div className="vassalage-box">
            <div className="category_title">{this.props.languageShell.t("in_manufacturer")}</div>
            <div className="filter_item">
              <span
                onClick={() => {
                  this.setVassalage("");
                }}
                className={this.state.vassalage === "" ? "classactive" : ""}
              >
                {this.props.languageShell.t("in_type_all")}
              </span>
              {this.props.vendors.map((item) => {
                return (
                  <span
                    onClick={() => {
                      this.setVassalage(item.vassalage);
                    }}
                    key={`filter_vendor_${item.vassalage}`}
                    className={this.state.vassalage === item.vassalage ? "classactive" : ""}
                  >
                    <img
                      className="vendor-icon"
                      src={`${this.props.common.vendorIconCDN}${item.vassalage}-WHITE.png`}
                      alt=""
                    />
                    {/* <img
                      className="vendor-icon color"
                      src={`${this.props.common.vendorIconCDN}${item.vassalage}-COLOR.png`}
                      alt=""
                    /> */}
                  </span>
                );
              })}
            </div>
          </div>
        )}
        {this.props.filterType === "filter" && [
          <div className="gameClassify-box">
            <div className="category_title">{this.props.languageShell.t("game_type")}</div>
            <div className="filter_item">
              {categoryList.map((item) => {
                return (
                  <span
                    onClick={() => {
                      this.setGameClassify(item.caId);
                    }}
                    className={this.state.gameClassify === item.caId ? "classactive" : ""}
                  >
                    {this.props.languageShell.t(item.name)}
                  </span>
                );
              })}
            </div>
          </div>,
          <div className="minbet-box">
            <div className="category_title">{this.props.languageShell.resource["in_minimum_bet"]}</div>
            <div className="filter_item">
              {minbetList.map((item) => {
                return (
                  <span
                    onClick={() => {
                      this.setMinBet(item.bet);
                    }}
                    className={this.state.minBet === item.bet ? "classactive" : ""}
                  >
                    {this.props.languageShell.t(item.name)}
                  </span>
                );
              })}
            </div>
          </div>,
          <div className="minbet-box">
            <div className="category_title">{this.props.languageShell.t("in_number_lines")}</div>
            <div className="filter_item">
              {minLineList.map((item) => {
                return (
                  <span
                    onClick={() => {
                      this.setMinLine(item.line);
                    }}
                    className={this.state.minLine === item.line ? "classactive" : ""}
                  >
                    {this.props.languageShell.t(item.name)}
                  </span>
                );
              })}
            </div>
          </div>,
        ]}
        <span className="filter_submit" onClick={this.onSubmit}>
          {this.props.languageShell.t("in_increase_submit")}
        </span>
        {/* {this.otherGame.length && (
          <div className="gameType-filter">
            <div className="gameClassify_title">{this.props.languageShell.t("other_games")}</div>
            <div className="filter_item">
              {this.otherGame.map((item) => {
                return (
                  <span onClick={() => this.changeGameType(item.gameCategory)}>
                    {this.props.languageShell.t(gameName[item.gameCategory])}
                  </span>
                );
              })}
            </div>
          </div>
        )} */}
      </div>
    );
  }
}

export default GameFilter;
