.filter-game-container {
  position: fixed;
  inset: 100% 0 0;
  z-index: 19999 !important;
  width: 100%;
  height: 100%;
  padding: 100px 45px 50px;
  overflow: auto;
  overscroll-behavior: none;
  font-size: 28px;
  color: #fff;
  background: rgb(0 0 0 / 70%);

  .search-game {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    height: 80px;
    margin: 30px 0;

    .am-icon-md {
      width: 40px;
      height: 35px;
      margin-left: 15px;
      color: #666;
    }

    .search-icon {
      position: absolute;
    }

    .search-input {
      height: 100%;
      padding-left: 70px;
      color: #fff;
      background-color: #383838;
      border: none;
      border-radius: 24px;
    }
  }

  .filter_title {
    margin-bottom: 60px;
    font-size: 38px;
    font-weight: 700;
    color: #fff;
    text-align: center;
  }

  .category_title {
    margin-bottom: 30px;
    font-size: 30px;
    font-weight: 700;
    color: #fff;
  }

  .close_fiflter {
    position: absolute;
    top: 60px;
    right: 40px;
    width: 50px;
    height: 50px;
    color: #000;
  }

  .gameClassify-box,
  .minbet-box {
    margin-top: 30px;
    text-align: left;
  }

  .filter_item {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
  }

  .filter_item span {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 100%;
    height: 80px;
    font-size: 24px;
    font-weight: 500;
    color: rgb(255 255 255 / 70%);
    text-align: center;
    background: #101a43;
    border: 2px solid rgb(255 255 255 / 10%);
    border-radius: 50px;

    .vendor-icon {
      display: block;
      width: 120px;
      height: 60px;
      object-fit: contain;
      opacity: 0.7;
    }

    .color {
      display: none;
    }

    &.classactive {
      color: #fff;
      background: $common-gradient;
      border: none;

      .vendor-icon {
        opacity: 1;
      }
    }
  }

  .filter_submit {
    display: block;
    width: 570px;
    height: 80px;
    margin: 60px auto 0;
    font-size: 30px;
    font-weight: 700;
    line-height: 80px;
    color: #fff;
    text-align: center;
    background: $common-gradient;
    border: none;
    border-radius: 50px;
  }

  .gameType-filter {
    margin-top: 30px;
  }
}

.filter-game-container.open {
  top: 0;
  transition: all 0.25s ease-in-out;
}
