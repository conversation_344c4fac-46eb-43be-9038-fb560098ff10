import React from "react";

import MyPortal from "@/ui/TargetPortal";

const SvgGradients = () => {
  return (
    <MyPortal>
      <svg style={{ position: "absolute", width: 0, height: 0 }} xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="icon_gradient1" x1="18" y1="0.28125" x2="18" y2="35.4375" gradientUnits="userSpaceOnUse">
            <stop stopColor="#FCB632" />
            <stop offset="0.2" stopColor="#FF7304" />
            <stop offset="0.795" stopColor="#FF9948" />
            <stop offset="1" stopColor="#FFFAA6" />
          </linearGradient>
          <radialGradient
            id="icon_gradient2"
            cx="0"
            cy="0"
            r="1"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(25.0278 -14.8369) rotate(90) scale(75.6513 34.7084)"
          >
            <stop offset="0.213454" stopColor="#CDA619" />
            <stop offset="0.662246" stopColor="#FFECA6" />
            <stop offset="0.667475" stopColor="#CDA619" />
            <stop offset="1" stopColor="#FFECA6" />
          </radialGradient>
          <linearGradient
            id="icon_gradient3"
            x1="21.8547"
            y1="6.66772"
            x2="21.8547"
            y2="37.4129"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#FFE600" />
            <stop offset="1" stopColor="#FFB800" />
          </linearGradient>
        </defs>
      </svg>
    </MyPortal>
  );
};

export default SvgGradients;
