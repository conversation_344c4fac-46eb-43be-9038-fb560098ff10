.home-footer-wrap {
  width: 100%;
  padding: 64px 32px;
  background: var(--bg-color-tooltip);

  .home-footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .footer-section-list {
    display: flex;
    flex-wrap: wrap;
    gap: 64px 80px;

    .section-item:nth-child(odd) {
      width: 330px;
    }

    .section-item:nth-child(even) {
      width: 267px;
    }
  }

  .footer-title {
    margin-bottom: 32px;
    font-size: 24px;
    font-style: italic;
    font-weight: 500;
    line-height: normal;
    color: var(--text-color-highlight);
  }

  .footer-logo {
    display: block;
    width: 100%;
    margin-top: -20px;
  }

  .footer-info {
    margin-top: 12px;
    font-size: 20px;
    font-weight: 400;
    line-height: normal;
    color: var(--text-color-primary);
  }

  .footer-list-nav {
    display: flex;
    flex-direction: column;
    gap: 32px;
    font-size: 24px;
    font-weight: 400;
    line-height: normal;
    color: var(--text-color-primary);
  }

  .icon-qualification {
    display: block;
    width: 257px;
  }

  .copyright {
    padding: 0 64px;
    margin-top: 64px;
    font-size: 24px;
    font-style: italic;
    font-weight: 500;
    line-height: normal;
    color: var(--text-color-primary);
    text-align: center;
  }
}
