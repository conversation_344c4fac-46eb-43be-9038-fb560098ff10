import React from "react";
import { withRouter } from "react-router";
import { inject, observer } from "mobx-react";
import { CommonLogo, navigatorUtil, withCs, withReferral } from "tcg-mobile-common";

import { navConfig } from "@/config/game.config";
import withGame from "@/hoc/withGame";
import { homeScrollTop } from "@/utils/dom";
import { handleWindowPage } from "@/utils/openLink";

import HelpNav from "../HelpNav/HelpNav";

import "./HomeFooter.scss";

const { isApp, isPWA } = navigatorUtil;

const showDownload = !isApp && !isPWA;

@inject("gameCenter", "common", "languageShell", "mcCommon", "auth", "mcMenu", "tcgCommon")
@withGame
@withCs
@withRouter
@withReferral
@observer
class HomeFooter extends React.Component {
  get isLogin() {
    return this.props.auth.isLogin;
  }
  get resources() {
    return this.props.common.downLinkObj;
  }
  get sorting() {
    return [{ gameCategory: "HOT" }, ...this.props.gameCenter.gameVendor.sorting];
  }
  get helpList() {
    return this.props.common.helpList;
  }
  get promoList() {
    return this.props.common.activity;
  }
  get slotVendor() {
    const list = this.props.gameCenter.gameVendor.mapping["RNG"] || [];
    return list;
  }
  state = {
    activeIndex: -1,
    activeHelpIndex: 0,
  };

  openResources = (type) => {
    const link = this.resources[type];
    if (link?.url) {
      handleWindowPage(link?.url);
    }
  };

  promoClick = (item) => {
    const { id } = item;
    if (id) {
      this.props.history.push(`/m/activityDetail?id=${id}`);
    }
  };

  helpClick = (index) => {
    this.props.history.push(`/m/help?i=${index}`);
  };

  handleGameNav = async (gameCategory) => {
    await this.props.gameCenter.setCurrentGameCategory(gameCategory);
    homeScrollTop();
  };

  render() {
    return (
      <div className="home-footer-wrap">
        <div className="home-footer-content">
          <div className="footer-section-list">
            <div className="section-item">
              <div className="footer-title">{this.props.languageShell.t("about_us")}</div>
              <CommonLogo className="footer-logo" src={require("@/assets/images/logo/logo.png")} />
              <div className="footer-info">
                <p>{this.props.languageShell.t("footer_info1")}</p>
                <p>{this.props.languageShell.t("footer_info2")}</p>
              </div>
            </div>
            <div className="section-item">
              <div className="footer-title">{this.props.languageShell.t("in_increase_game")}</div>
              <div className="footer-list-nav">
                {this.sorting.map((item) => {
                  return (
                    <div
                      key={`footer_game_nav_${item.gameCategory}`}
                      className="nav-item"
                      onClick={() => this.handleGameNav(item.gameCategory)}
                    >
                      {this.props.languageShell.t(navConfig[item.gameCategory]?.name)}
                    </div>
                  );
                })}
              </div>
            </div>
            <HelpNav className="section-item" />
            <div className="section-item">
              <div className="footer-title">{this.props.languageShell.t("qualification")}</div>
              <img className="icon-qualification" src={require("@/assets/images/footer/qualification.png")} alt="" />
            </div>
          </div>

          <div className="copyright">
            <p>©2025 ROYALMANILA.com ALL COPYRIGHT RIGHTS RESERVED.</p>
          </div>
        </div>

        {/* <HelpPopup activeIndex={this.state.activeHelpIndex} /> */}
      </div>
    );
  }
}

export default HomeFooter;
