import React from "react";
import { withRouter } from "react-router-dom";
import { Toast } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import { AutoIcon, navigatorUtil, withCs, withReferral } from "tcg-mobile-common";

import deploy from "@/config/deploy.config";
import { homePath } from "@/config/game.config";
import WithDownload from "@/hoc/withDownload";
import { handleWindowPage } from "@/utils/openLink";
import { get } from "@/utils/storage";

import "./Footer.scss";

const { gameSelectType } = deploy;

const { isApp, isPWA } = navigatorUtil;

const showDownload = !isApp && !isPWA;

@inject("languageShell", "common", "mcMenu", "auth", "gameCenter", "tcgCommon", "personal")
@withReferral
@withCs
@withRouter
@WithDownload
@observer
class Footer extends React.Component {
  get resources() {
    return this.props.common.downLinkObj;
  }
  get isLogin() {
    return this.props.auth.isLogin;
  }
  get showVipBenefit() {
    return this.props.tcgCommon.showVipBenefit;
  }
  get showMission() {
    return this.props.personal.availablePromosByKey("MISSION").enabled;
  }
  get showChat() {
    return this.props.mcMenu.whitelabelVal.CBS3;
  }

  state = { showService: false };

  componentDidMount() {
    this.getInboxUnreadCount();
    document.addEventListener("click", this.handleClickOutside);
  }
  componentWillUnmount() {
    document.removeEventListener("click", this.handleClickOutside);
  }
  handleClickOutside = (e) => {
    if (this.footerRef && !this.footerRef.contains(e.target)) {
      this.setState({
        showService: false,
      });
    }
  };
  getChatUnreadCount = () => {
    if (!this.props.mcMenu.whitelabelVal.CBS3) {
      return;
    }
    const { type = 4 } = get("MC_SESSION_INFO") || {};
    if (4 === +type) {
      return;
    }
    this.props.common.getChatUnreadCount();
  };

  getInboxUnreadCount = () => {
    const { type = 4 } = get("MC_SESSION_INFO") || {};
    if (4 === +type) {
      return;
    }
    this.props.common.getInboxUnreadCount();
  };

  convertCount(n) {
    if (+n > 99) {
      return "99+";
    }
    return +n;
  }

  goToHome = async () => {
    this.props.gameCenter.setCurrentDataType(gameSelectType.ALL);
    this.props.gameCenter.setCurrentVassalage("");
    await this.props.gameCenter.setCurrentGameCategory("HOME");
    await this.props.history.push(`/m/home`);
    setTimeout(() => {
      window.scrollTo(0, 0);
    }, 0);
  };

  toggleSideMenu = () => {
    this.props.common.showSideMenu(!this.props.common.sideMenuOpen);
  };

  handleBonus = () => {
    if (this.whitelabelVal["REWCEN3"]) {
      this.props.history.push(`/m/rewardCenter`);
    } else {
      Toast.info(this.props.languageShell.t("contact_service"), 2);
    }
  };

  handleService = () => {
    this.toggleService();
  };
  openResources = (type) => {
    const link = this.resources[type];
    if (link?.url) {
      handleWindowPage(link?.url);
    }
  };
  toggleService = () => {
    this.setState({
      showService: !this.state.showService,
    });
  };

  toggleDeposit = () => {
    this.setState({
      showDeposit: !this.state.showDeposit,
    });
  };

  handleVisibleService = (visible) => {
    this.setState({
      showService: visible,
    });
  };
  navClick = (item) => {
    const { path, type } = item;
    if (path) {
      return this.props.history.push(path);
    }
  };
  handleDownload = () => {
    this.props.common.setDownPopup(true);
  };
  render() {
    const session = get("MC_SESSION_INFO");
    const { totalUnreadCount = 0 } = this.props.common;
    const canShowUnreadCount = session && totalUnreadCount > 0;
    return (
      <div className={cz("footer-menu", { on: this.props.common.footerMenuStatus })} ref={(c) => (this.footerRef = c)}>
        <div className="footer-menu-bg">
          <div className="footer-menu-list">
            <div className={cz("footer-menu-item")} onClick={this.toggleSideMenu}>
              <div className="footer-icon">
                <AutoIcon icon={require("!svg-sprite-loader!@/assets/images/footer/footer-menu.svg")} alt="" />
              </div>
              <div className="footer-menu-name">
                <span>{this.props.languageShell.t("menu")}</span>
              </div>
            </div>
            <div
              className={cz("footer-menu-item", { on: homePath.includes(location.pathname) })}
              onClick={this.goToHome}
            >
              <div className="footer-icon">
                <AutoIcon icon={require("!svg-sprite-loader!@/assets/images/footer/footer-home.svg")} alt="" />
              </div>
              <div className="footer-menu-name">
                <span>{this.props.languageShell.t("hd_home")}</span>
              </div>
            </div>
            <div
              className={cz("footer-menu-item", {
                on: ["/m/activity", "/m/activityDetail"].includes(location.pathname),
              })}
              onClick={() => this.props.history.push("/m/activity")}
            >
              <div className="footer-icon">
                <AutoIcon icon={require("!svg-sprite-loader!@/assets/images/footer/footer-promo.svg")} alt="" />
              </div>
              <div className="footer-menu-name">
                <span>{this.props.languageShell.t("promotion")}</span>
              </div>
            </div>
            <div className={cz("footer-menu-item")} onClick={this.props.openReferral}>
              <div className="footer-icon">
                <AutoIcon icon={require("!svg-sprite-loader!@/assets/images/footer/footer-invite.svg")} alt="" />
              </div>
              <div className="footer-menu-name">
                <span>{this.props.languageShell.t("invite_tips")}</span>
              </div>
            </div>
            <div
              onClick={() => this.props.history.push("/m/member/home")}
              className={cz("footer-menu-item", { on: location.pathname === "/m/member/home" })}
            >
              <div className="footer-icon">
                <AutoIcon icon={require("!svg-sprite-loader!@/assets/images/footer/footer-member.svg")} alt="" />
              </div>
              <div className="footer-menu-name">
                <span>{this.props.languageShell.t("hd_member_center")}</span>
              </div>
            </div>
            {/* {this.showChat && (
              <div className="footer-menu-item">
                <ChatIcon
                  icon={require("@/assets/images/footer/footer-chat.png")}
                  title={this.props.languageShell.t("live_chat")}
                />
              </div>
            )} */}
            {/* <Link
              to={"/m/voucherCenter"}
              className={cz("footer-menu-item ", { on: location.pathname === "/m/voucherCenter" })}
            >
              <div className="footer-icon">
                <img className="icon-normal" src={require("@/assets/images/footer/footer-deposit.png")} alt="" />
              </div>
              <div className="footer-menu-name">
                <div>{`${this.props.languageShell.t("hd_deposit_button")}`}</div>
              </div>
            </Link> */}
          </div>
        </div>
      </div>
    );
  }
}

export default Footer;
