.shell-spinner-loading-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 240px;

  .shell-spinner-loading {
    width: 65px;
    height: 65px;
    animation: rotator 1.4s linear infinite;

    .path {
      stroke-dasharray: 187;
      stroke-dashoffset: 0;
      transform-origin: center;
      animation:
        dash 1.4s linear infinite,
        colors 5.6s linear infinite;
    }
  }
}

@keyframes rotator {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes colors {
  0% {
    stroke: #fefc41;
  }

  20% {
    stroke: #ff5676;
  }

  40% {
    stroke: #fa60ff;
  }

  60% {
    stroke: #3296ff;
  }

  80% {
    stroke: #30f282;
  }

  100% {
    stroke: #fefc41;
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 187;
  }

  50% {
    stroke-dashoffset: 46.75;
    transform: rotate(180deg);
  }

  100% {
    stroke-dashoffset: 187;
    transform: rotate(360deg);
  }
}
