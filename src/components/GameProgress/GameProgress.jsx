import React from "react";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import "./GameProgress.scss";

@inject("languageShell")
@observer
class GameProgress extends React.Component {
  get countPercent() {
    return `${Math.ceil((this.props.count / this.props.totalCount) * 100)}%`;
  }
  render() {
    if (!this.props.totalCount) {
      return null;
    }
    return (
      <div className="game-progress-wrap">
        <div className="game-progress">
          <div className="game-progress-bar">
            <span className="game-progress-fill" style={{ width: this.countPercent }} />
          </div>
          {/* <div className="game-percent">{this.countPercent}</div> */}
        </div>
      </div>
    );
  }
}

GameProgress.propTypes = {
  count: PropTypes.number,
  totalCount: PropTypes.number,
};

GameProgress.defaultProps = {
  count: 0,
  totalCount: 0,
};

export default GameProgress;
