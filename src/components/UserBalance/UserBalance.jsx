import React from "react";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";
import { currencyFormat } from "tcg-mobile-common";

import "./UserBalance.scss";

@inject("wallet", "common", "tcgCommon", "languageShell")
@observer
class UserBalance extends React.Component {
  state = {
    refresh: false,
  };
  componentDidMount() {
    this.props.setRef && this.props.setRef(this);
  }
  getUsername() {
    const { memberInfo } = this.props.common;
    if (!memberInfo) {
      return "";
    }
    return memberInfo.nickname ? memberInfo.nickname : memberInfo.account;
  }
  toggleBalance = () => {
    this.props.common.toggleBalance();
  };
  refreshBalance = (e) => {
    e.stopPropagation();
    this.setState({
      refresh: true,
    });
    this.props.wallet.getWallet().finally((res) => {
      this.setState({
        refresh: false,
      });
    });
  };
  renderBalance = () => {
    return this.props.common.hideBalance || this.state.refresh ? (
      <span className="amount">
        {currencyFormat.format({ value: this.props.wallet.wallet?.sumBalance, fixed: 2 }).toString().replace(/./g, "*")}
      </span>
    ) : (
      <span className="amount">{currencyFormat.format({ value: this.props.wallet.wallet?.sumBalance, fixed: 2 })}</span>
    );
  };
  render() {
    return (
      <div className="member-balance">
        <div className="member-balance-bg">
          <div className="sum-balance">
            {/* <i className="icon-wallet" /> */}
            <div className="toggle-balance" onClick={this.toggleBalance}>
              <Icon
                type={
                  this.props.common.hideBalance
                    ? require("!svg-sprite-loader!@/assets/images/form/eye-closed.svg")
                    : require("!svg-sprite-loader!@/assets/images/form/eye-open.svg")
                }
              />
            </div>

            {this.renderBalance()}
            <span className="symbol">{this.props.tcgCommon.currencySymbol}</span>
          </div>
          {/* <div
            className={cz("shell-refresh-balance", { rotateFull: this.state.refresh })}
            onClick={this.refreshBalance}
          >
            <Icon type={require("!svg-sprite-loader!@/assets/images/common/icon-refresh.svg")} />
          </div> */}
        </div>
      </div>
    );
  }
}

export default UserBalance;
