.member-balance {
  display: flex;
  align-items: center;

  .member-balance-bg {
    position: relative;
    display: flex;
    align-items: center;
  }

  .icon-wallet {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    background: url("~@/assets/images/common/icon-wallet.png") no-repeat center/cover;
  }

  .sum-balance {
    display: flex;
    align-items: center;

    .amount {
      @include ellipsis;
    }

    .symbol {
      margin-right: 5px;
    }

    .icon-symbol {
      width: 36px;
      height: 36px;
      margin-right: 10px;
    }
  }

  .toggle-balance {
    width: 24px;
    height: 24px;
    margin-right: 10px;

    .am-icon {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .shell-refresh-balance {
    @include flex-center;
    flex-shrink: 0;
    width: 30px;
    height: 30px;
    margin-left: 16px;

    &.rotateFull {
      animation: rotate-full 0.3s linear infinite;
    }

    svg {
      display: block;
      width: 100%;
      height: 100%;
      fill: none;
    }
  }
}
