.vendor-game-item {
  position: relative;
  flex-shrink: 0;
  width: 100%;
  min-width: 0;
  overflow: hidden;
  cursor: pointer;

  .game-maintain {
    @include flex-center;
    position: absolute;
    inset: 0;
    z-index: 2;
    cursor: not-allowed;
    background-color: rgb(0 0 0 / 50%);
    border-radius: 18px;

    .icon-maintain {
      display: block;
      width: 200px;
      height: 200px;
    }
  }

  .vendor-small-icon {
    position: absolute;
    bottom: 31px;
    left: 50%;
    width: 100px !important;
    height: 50px !important;
    transform: translateX(-50%);
  }

  .vendor-item-bg {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    border-radius: 13px;
  }

  .vendor-game-img {
    position: relative;
    width: 100%;
    overflow: hidden;

    .lazy-load-image-background {
      display: block !important;
    }

    .img-loading {
      width: 100%;
      object-fit: cover;
    }

    .vendor-large-icon {
      height: 100%;

      .common-vendor-icon {
        position: absolute;
        top: 0;
        right: 0;
        height: 107%;
        object-fit: contain;
      }
    }

    .vendor-game-name {
      position: absolute;
      top: 10px;
      left: 17px;
      z-index: 1;
      display: flex;
      flex-direction: column;
      width: 100px;
      font-size: 19.2px;
      font-style: normal;
      font-weight: 500;
      line-height: 23.08px;
      text-transform: capitalize;

      .common-vendor-icon {
        display: block;
        width: 100%;
      }

      .display-name {
        @include ellipsis;
        display: block;
      }
    }
  }

  .vendor-icon {
    position: absolute;
    bottom: 30px;
    left: 20px;
    width: 100px;
    height: 50px;
    object-fit: contain;
  }
}
