import React from "react";
import { withRouter } from "react-router";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { LazyLoadImage, VendorIcon } from "tcg-mobile-common";

import { gamePath } from "@/config/game.config";
import withGame from "@/hoc/withGame";

import "./VendorGameItem.scss";

@withGame
@inject("common", "gameCenter")
@withRouter
@observer
class VendorGameItem extends React.Component {
  state = { loaded: false };
  launchGame = () => {
    const { maintenance } = this.props.item;
    if (maintenance) return;
    this.props.launchGameVendor(this.props.item);
  };
  goPage = () => {
    const { gameType, gameClassify } = this.props.item;
    this.props.history.push(`${gamePath[gameType]}?gameClassify=${gameClassify}`);
  };
  handleOnLoad = (e) => {
    this.setState({
      loaded: true,
    });
  };
  render() {
    const { mobileLargeIcon, maintenance, displayName, img, gameType, vassalage } = this.props.item;
    return (
      <div className={cz("vendor-game-item", this.props.className, gameType.toLowerCase())} onClick={this.launchGame}>
        {/* <div className="vendor-item-bg shine" /> */}
        {/* {!this.state.loaded && <ImgPlaceholder />} */}
        {/* {maintenance && (
          <div className="game-maintain">
            <img className="icon-maintain" src={require("@/assets/images/game/maintain.png")} alt="" />
          </div>
        )} */}

        <div className="vendor-game-img">
          <LazyLoadImage
            className="img-loading"
            src={mobileLargeIcon}
            errorSrc={this.props.common.defaultImage}
            visibleByDefault={true}
            afterLoad={this.handleOnLoad}
          />
          {/* <VendorIcon
            className="vendor-large-icon"
            folder="SUBMENU_IMG"
            gameType={gameType}
            vendor={vassalage}
            styleName="STYLE4"
            visibleByDefault={true}
            afterLoad={this.handleOnLoad}
          /> */}
          <VendorIcon
            className="vendor-small-icon"
            visibleByDefault={true}
            folder="RNG_LIST_VENDOR"
            vendor={vassalage}
            color="COLOR2"
          />
          {/* <div className="vendor-game-name">
            <span className="display-name">{displayName}</span>
          </div> */}
        </div>
        {/* <img
          className="vendor-icon"
          src={`${this.props.common.vendorIconCDN}/${coverVassalage(vassalage)}-WHITE.png`}
          alt=""
        /> */}
        {/* <p className="vendor-game-name">{displayName}</p> */}
      </div>
    );
  }
}

VendorGameItem.propTypes = {
  className: PropTypes.string,
  gameType: PropTypes.string,
};

VendorGameItem.defaultProps = {
  className: "",
};

export default VendorGameItem;
