import React from "react";
import { inject, observer } from "mobx-react";

import "./ImgPlaceholder.scss";

const ImgPlaceholder = (props) => {
  return (
    <div className="img-placeholder">
      {/* <img className="icon-placeholder" src={require("@/assets/images/logo/logo.png")} /> */}
      <span>{props.languageShell.t("loading")}</span>
    </div>
  );
};

export default inject("languageShell")(observer(ImgPlaceholder));
