import React from "react";
import { withRouter } from "react-router-dom";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import GameListItem from "../GameListItem/GameListItem";
import SearchInput from "../SearchInput/SearchInput";

import "./HotGame.scss";

@inject("languageShell", "gameCenter", "mcCommon")
@withRouter
@observer
class HotGame extends React.Component {
  get hasMore() {
    return this.state.currentPage < this.totalPages;
  }
  get totalPages() {
    return Math.ceil(this.hotList?.length / this.props.pageSize);
  }
  get hotList() {
    return this.props.gameCenter.hotGames;
  }
  get activeGames() {
    const startIndex = (this.state.currentPage - 1) * this.props.pageSize;
    const endIndex = startIndex + this.props.pageSize;
    // return this.hotList.slice(startIndex, endIndex);
    return this.hotList.slice(0, endIndex);
  }
  get hotGames() {
    if (this.state.gameName) {
      const searchGameList = this.hotList.filter((item) => {
        return item.gameName.toLowerCase().indexOf(this.state.gameName) !== -1;
      });
      return searchGameList;
    }
    return this.hotList;
  }
  constructor(props) {
    super(props);
    this.state = {
      gameName: "",
      currentPage: 1,
    };
  }
  handleSearchByName = (name) => {
    this.setState({
      gameName: name?.trim()?.toLowerCase(),
      showMore: !!name,
    });
  };
  loadMore = () => {
    this.setState({
      currentPage: this.hasMore ? this.state.currentPage + 1 : 1,
    });
  };
  pageChange = (page) => {
    this.setState({
      currentPage: page,
    });
  };
  render() {
    return (
      <div className={cz("hot-game-wrap", this.props.className)}>
        <div className="hot-game-content">
          {/* <GameTitle gameType="HOT" title={this.props.languageShell.t("in_hot_games")} /> */}
          <div className="game-search-section">
            <SearchInput searchGame={this.handleSearchByName} />
          </div>
          <div className="category-games">
            <div className="category-game-list">
              {this.hotGames.map((item) => {
                return <GameListItem key={`hot-${item.gameId}`} item={item} gameType="HOT" />;
              })}
            </div>
            {/* <div className="game-pagination">
              <Pagination
                totalPages={this.totalPages}
                currentPage={this.state.currentPage}
                onChange={this.pageChange}
              />
            </div> */}
            {/* <GameMoreBtn onClick={this.loadMore} hasMore={this.hasMore} /> */}
          </div>
          {/* {this.hotList?.length > this.props.pageSize && (
            <LoadMoreBtn
              totalCount={this.hotList?.length}
              hasMore={this.hasMore}
              count={this.activeGames?.length}
              loadMore={this.loadMore}
            />
          )} */}
        </div>
      </div>
    );
  }
}

HotGame.propTypes = {
  className: PropTypes.string,
  pageSize: PropTypes.number,
  hasTitle: PropTypes.bool,
};

HotGame.defaultProps = {
  className: "",
  pageSize: 36,
  hasTitle: false,
};

export default HotGame;
