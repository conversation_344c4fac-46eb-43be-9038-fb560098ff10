import React from "react";

import { get } from "@/utils/storage";

import "./UnreadBubble.scss";

const UnreadBubble = ({ count }) => {
  function convertCount(n) {
    if (+n > 99) {
      return "99+";
    }
    return +n;
  }
  const login = get("MC_SESSION_INFO");
  if (!login || !count || count === 0) {
    return null;
  }
  return (
    <div className="unread-wrap">
      <i className="unread-count">{convertCount(count)}</i>
    </div>
  );
};

export default UnreadBubble;
