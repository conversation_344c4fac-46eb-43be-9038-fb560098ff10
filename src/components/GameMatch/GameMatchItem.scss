.game-match-item {
  width: 100%;
  height: 380px;
  overflow: hidden;
  color: #fff;
  background: url("./images/match-item-bg.png") no-repeat center/cover;
  border-radius: 6px;

  .match-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 30px 0 0;
  }

  .match-date {
    display: flex;
    flex-shrink: 0;
    flex-direction: column;
    align-items: center;

    .match-time {
      font-size: 47px;
      font-weight: 700;
      line-height: normal;
    }

    .match-day {
      font-size: 24px;
    }
  }

  .match-league {
    font-size: 47px;
    font-weight: 700;
    line-height: normal;
    text-align: center;
  }

  .match-icon {
    display: flex;
    gap: 30px;
    align-items: center;

    .team-icon {
      display: block;
      width: 180px;
      height: 180px;
      object-fit: contain;
    }

    .icon-vs {
      display: block;
      width: 178px;
      height: 178px;
      object-fit: contain;
    }
  }

  .match-info {
    display: grid;
    gap: 15px;

    .team-name {
      @include trim(1);
      font-size: 23px;
      line-height: 34.62px;
      color: #fff;
      letter-spacing: 0.216px;
    }
  }

  .team-odds {
    display: flex;
    gap: 20px;
    align-items: center;
    align-self: stretch;
    margin-top: 20px;
  }

  .play-btn {
    @include flex-center;
    flex: 1;
    height: 70px;
    padding: 0 10px;
    font-size: 24px;
    font-weight: 700;
    color: #161616;
    background: #fde9bf;
    border-radius: 8px;

    .arrow-right {
      width: 24px;
      height: 24px;
      margin-left: 20px;
    }
  }

  .m-line {
    width: 2px;
    height: 60px;
    margin-right: 12px;
    background: #c5c6c8;
    border-radius: 10px;
    opacity: 0.2;
  }

  .match-odd {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background: rgba($color: #3b2a5e, $alpha: 98%);
    border-radius: 8px;

    .odd-name {
      font-size: 23px;
      font-weight: 500;
      line-height: normal;
      color: #9784bf;
      text-transform: capitalize;
    }

    .odd-num {
      font-size: 23px;
      font-weight: 700;
      line-height: normal;
      color: #fff;
      text-align: right;
    }
  }
}
