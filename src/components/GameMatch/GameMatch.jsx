import React, { Component } from "react";
import dayjs from "dayjs";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import GameMatchItem from "./GameMatchItem";

import "./GameMatch.scss";

const matchIcon = {
  BADMINTON: require("@/assets/images/game/badminton.png"),
  BASKETBALL: require("@/assets/images/game/basketball.png"),
  CRICKET: require("@/assets/images/game/cricket.png"),
  SOCCER: require("@/assets/images/game/soccer.png"),
  TENNIS: require("@/assets/images/game/tennis.png"),
  TABLETENNIS: require("@/assets/images/game/tabletennis.png"),
  VOLLEYBALL: require("@/assets/images/game/volleyball.png"),
  OTHER: require("@/assets/images/game/other.png"),
};

@inject("languageShell", "common")
@observer
class GameMatch extends Component {
  formatStartTime = (time) => {
    return (
      <div className="match-date">
        <span>{dayjs(time).format("MM-DD HH:mm")}</span>
      </div>
    );
  };
  render() {
    const { gameName, gameClassifyNew, matchList } = this.props.item;
    if (matchList?.length < 1) {
      return null;
    }
    return (
      <div className="game-match-wrap">
        <div className="game-match-title">
          <img className="title-icon" src={matchIcon[gameClassifyNew] || matchIcon["OTHER"]} alt="" />
          <span>{gameName}</span>
        </div>
        <div className="game-match-content">
          <div className="game-match-list hide-scrollbar">
            {matchList.map((item) => {
              return <GameMatchItem key={`match-${item.matchId}`} item={item} />;
            })}
          </div>
        </div>
      </div>
    );
  }
}

GameMatch.propTypes = {
  item: PropTypes.shape({
    gameName: PropTypes.string.isRequired,
    gameClassifyNew: PropTypes.string.isRequired,
    matchList: PropTypes.array.isRequired,
  }).isRequired,
};

GameMatch.defaultProps = {
  item: {
    gameName: "",
    gameClassifyNew: "",
    matchList: [],
  },
};

export default GameMatch;
