import React, { Component } from "react";
import dayjs from "dayjs";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import ScrollGameList from "@/components/ScrollGameList/ScrollGameList";

import GameMatchItem from "./GameMatchItem";

import "./GameMatchList.scss";

const matches = [
  {
    awayTeam: "SK Slovan Bra",
    matchId: "2869190",
    league: "UEFA",
    moneyLine: { homeTeamOdds: 1.91, awayTeamOdds: 3.73 },
    homeTeam: "Bayern Munich",
    matchTime: 1739938800864,
  },
  {
    awayTeam: "Feyenoord",
    matchId: "2869191",
    league: "UEFA",
    moneyLine: { homeTeamOdds: 1.91, awayTeamOdds: 3.73 },
    homeTeam: "Lille OSC",
    matchTime: 1739938800864,
  },
];

@inject("languageShell", "common")
@observer
class GameMatch extends Component {
  get matchList() {
    return this.props.matchs.reduce((prev, curr) => {
      return [...prev, ...curr.matchList];
    }, []);
  }
  formatStartTime = (time) => {
    return (
      <div className="match-date">
        <span>{dayjs(time).format("MM-DD HH:mm")}</span>
      </div>
    );
  };
  render() {
    if (this.props.matchs?.length < 1) {
      return null;
    }
    return (
      <ScrollGameList className="game-match-wrap" scrollCount={1} title={this.props.languageShell.t("events")} rows={1}>
        {this.props.matchs?.map((items, index) => {
          return items?.matchList?.map((item, idx) => {
            return <GameMatchItem key={`match_list_${index}_${idx}`} item={item} gameName={items.gameName} />;
          });
        })}
      </ScrollGameList>
    );
  }
}

GameMatch.propTypes = {
  matchs: PropTypes.array,
};

GameMatch.defaultProps = {
  matchs: [],
};

export default GameMatch;
