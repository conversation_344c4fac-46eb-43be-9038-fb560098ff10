import React from "react";
import dayjs from "dayjs";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import { utils } from "tcgmodulemc";

import "./GameMatchItem.scss";

@inject("languageShell", "common")
@observer
class GameMatchItem extends React.Component {
  startGame = (game) => {
    const { vendor, gameId, nodeId, matchId, leagueId } = game;
    utils.gcUtils.launchGameV3({
      vendor,
      gameId,
      nodeId,
      matchId,
      leagueId,
    });
  };
  formatMatchDate = (time) => {
    const today = dayjs().startOf("day");
    const liveDate = dayjs(time).startOf("day");
    const differenceInDays = liveDate.diff(today, "day");

    if (differenceInDays === 0) {
      return this.props.languageShell.t("in_today");
    } else if (differenceInDays === 1) {
      return this.props.languageShell.t("in_tomorrow");
    } else {
      return dayjs(time).format("DD-MMM");
    }
  };
  formatDate = (time) => {
    return dayjs(time).format("HH:ss, DD/MM");
  };
  formatStartTime = (time) => {
    return (
      <div className="match-date">
        <div className="match-time">{dayjs(time).format("HH:mm")}</div>
        <div className="match-day">{dayjs(time).format("DD MMM YYYY(ddd)")}</div>
      </div>
    );
  };
  render() {
    const item = this.props.item;
    return (
      <div className="game-match-item" onClick={() => this.startGame(item)}>
        <div className="match-content">
          <div className="match-league">
            <span>{item.league}</span>
          </div>
          <div className="match-icon">
            <div className="team-icon">
              <img src={item.homeTeamImageUrl} alt="" />
            </div>
            <img className="icon-vs" src={require("./images/match-vs.png")} alt="" />
            <div className="team-icon">
              <img src={item.awayTeamImageUrl} alt="" />
            </div>
          </div>
          {this.formatStartTime(item.matchTime)}
          {/* <div className="match-info">
            <div className="team-name">
              <span>{item.homeTeam}</span>
            </div>
            <div className="team-name">
              <span>{item.awayTeam}</span>
            </div>
          </div> */}
          {/* <div className="team-odds">
            {item.moneyLine?.homeTeamOdds && (
              <div className="home-odd match-odd">
                <span className="odd-name">Home</span>
                <span className="odd-num">{item.moneyLine?.homeTeamOdds}</span>
              </div>
            )}
            {item.moneyLine?.homeTeamOdds && (
              <div className="match-odd">
                <span className="odd-name">1</span>
                <span className="odd-num">X</span>
              </div>
            )}
            {item.moneyLine?.awayTeamOdds && (
              <div className="away-odd match-odd">
                <span className="odd-name">Away</span>
                <span className="odd-num">{item.moneyLine?.awayTeamOdds}</span>
              </div>
            )}
            {item.moneyLine && <div className="m-line" />}
            <div className="play-btn">
              <span>{this.props.languageShell.t("play")}</span>
              <Icon
                className="arrow-right"
                type={require("!svg-sprite-loader!@/assets/images/common/arrow-right.svg")}
              />
            </div>
          </div> */}
        </div>
      </div>
    );
  }
}

GameMatchItem.propTypes = {
  item: PropTypes.object.isRequired,
  gameName: PropTypes.string,
};

GameMatchItem.defaultProps = {
  item: {},
  gameName: "",
};

export default GameMatchItem;
