.symbol-lang-popup {
  .popup-content {
    .popup-close {
      inset: 10px 10px auto auto;
      margin-left: 0;
    }
  }
}

.symbol-lang {
  position: relative;
  width: 600px;
  height: 100vh;
  height: calc(100 * var(--vh, 1vh));
  padding: 20px 36px;
  background: #151414;

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    content: "";
    background: linear-gradient(to right, #151414, #ff7304, #ff9948, #151414);
  }

  .symbol-lang-content {
    position: relative;
  }

  .symbol-lang-header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 62px;
    padding: 16px 0;
  }

  .close-sidebar {
    width: 30px;
    height: 30px;

    .am-icon {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .symbol-lang-title {
    position: relative;
    width: 100%;
    margin: 30px 0;
    font-size: 30px;
    font-weight: 700;
    line-height: normal;
    color: #fff;
  }

  .region-wrap {
    width: 100%;
  }

  .region-item {
    display: flex;
    align-items: center;
    padding: 24px 30px;
  }

  .lang-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .lang-item {
      display: flex;
      align-items: center;
      height: 50px;
      font-size: 24px;
      font-weight: 700;
      line-height: normal;
      color: #fff;

      &:not(:last-child) {
        margin-right: 60px;
      }

      .lang-select {
        @include flex-center;
        width: 40px;
        height: 40px;
        margin-right: 20px;
        border: 2px solid #fff9;
        border-radius: 10px;

        .icon-check {
          display: none;
          width: 24px;
          height: 24px;
        }
      }

      &.lang-active {
        .lang-select {
          background: linear-gradient(180deg, #fcb632 0%, #ff7304 20%, #ff9948 79.5%, #fffaa6 100%);
          border: none;
        }

        .icon-check {
          display: block;
        }
      }
    }
  }

  .submit-btn {
    width: 100%;
    height: 88px;
    margin: 30px 0 0;
    font-size: 40px;
    font-weight: 500;
    line-height: normal;
    color: #1d212d;
    text-align: center;
    background: linear-gradient(180deg, #fff5e2 0%, #f0cda3 100%);
    border-radius: 100px;

    @include flex-center;
  }

  .symbol-item {
    display: flex;
    align-items: center;
    font-size: 30px;
    font-weight: 700;
    line-height: normal;
    color: #fff;

    .language-icon {
      width: 50px;
      height: 50px;
      margin-right: 30px;
    }
  }

  .country-name {
    margin-bottom: 20px;
    font-size: 30px;
    font-weight: 700;
    line-height: normal;
    color: #fff;
  }
}

.main-nav-open {
  .symbol-lang-menu {
    transform: translateX(0);
  }

  .sidebar-mask {
    display: block;
  }
}
