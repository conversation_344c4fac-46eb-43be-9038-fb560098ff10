.language-select {
  .simple-select {
    display: flex;
    align-items: center;
    width: 100%;

    .option-wrap {
      top: calc(100% + 20px);
      right: 0;
      z-index: 100;
      width: 250px;

      .option-bg {
        position: relative;
        padding: 10px;
        overflow: hidden;
        background: #222424;
        border-radius: 6px;
      }
    }

    .option-item {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      height: 80px;
      padding: 0 20px;
      font-size: 28px;
      line-height: normal;
      color: var(--text-color-secondary);
      text-align: center;
      background: #303232;
      border-radius: 6px;

      &:not(:last-child) {
        margin-bottom: 10px;
      }

      &.selected {
      }

      .icon-lang {
        width: 48px;
        height: 48px;
        margin-right: 20px;
      }

      .lang-title {
        display: flex;
        align-items: center;
      }

      .lang-name {
        display: flex;
        align-items: center;
      }
    }

    .icon-selected {
      position: relative;
      width: 21px;
      height: 22px;
      margin-left: 15px;
      background: url("~@/assets/images/common/icon-correct.png") no-repeat center/cover;
    }
  }

  .select-wrap {
    display: flex;
    align-items: center;
    width: 100%;

    .select-icon {
      display: none;
    }

    &.on {
    }
  }

  .selected-lang {
    @include flex-center;
    width: 100%;
    font-size: 32px;
    font-weight: 700;
    line-height: normal;
    color: #e8f8eb;
    text-align: center;

    .icon-language {
      display: block;
      width: 48px;
      height: 48px;
    }

    .arrow-right {
      width: 48px;
      height: 48px;
    }
  }

  .selected-name {
    width: 140px;
    text-align: center;
  }
}
