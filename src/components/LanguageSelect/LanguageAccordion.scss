.lang-accordion {
  overflow: hidden;

  .lang-accordion-item {
  }

  .accordion-content-inner {
    margin-top: 15px;
  }

  .accordion-active {
    .arrow-down {
      // transform: rotate(90deg);
    }
  }

  .accordion-item-bg {
    position: relative;
    overflow: hidden;
  }

  .lang-header-wrap {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .lang-accordion-header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    font-size: 24px;
    font-weight: 500;
    line-height: normal;
    color: var(--text-color-primary);
    border: 1px solid var(--border-color-surface);
    border-radius: 12px;

    .icon-lang {
      display: block;
      width: 32px;
      height: 32px;
    }
  }

  .lang-nav-list {
    @include gradient-border(linear-gradient(0deg, #1778ff 0%, #fff 100%), var(--bg-color-secondary), 1px, 8px);
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
    padding: 24px;
    overflow: hidden;

    .flag-icon {
      display: block;
      width: 30px;
      height: 30px;
      margin-right: 15px;
    }
  }

  .lang-nav-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 24px;
    font-weight: 600;
    line-height: normal;
    color: #fff;

    &.on {
      color: var(--text-color-accent);
    }

    .icon-correct {
      display: block;
      flex-shrink: 0;
      width: 24px;
      height: 25px;
      background: url(~@/assets/images/common/icon-correct.png) no-repeat center/100% 100%;
    }
  }

  .arrow-down {
    width: 32px;
    height: 32px;
    transition: all 0.2s;
  }

  .icon-checked {
    width: 36px;
    height: 36px;
    margin-right: 20px;
    border: 2px solid #9784bf;
    border-radius: 100px;
  }
}
