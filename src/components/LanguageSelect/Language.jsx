import React from "react";
import { inject, observer } from "mobx-react";
import { NationFlag, nationFlagMap } from "tcg-mobile-common";

import "./Language.scss";

class LanguageItem extends React.Component {
  getFlagIcon = (lang) => {
    const sett = nationFlagMap[lang];
    if (!sett) {
      return "";
    }
    return `${this.props.common.countryIconCDN}${sett.code}.svg`;
  };
  onSelect = (e, value) => {
    e.stopPropagation();
    this.props.languageShell.changeLanguageTo(value);
    this.closeLanguage();
  };
  closeLanguage = () => {
    this.props.common.showLanguage(false);
  };
  selectRegion = () => {
    this.props.common.showSymbolLang(true);
    this.props.common.showLeftSide(false);
  };
  render() {
    return (
      <div className="language-wrap" onClick={() => this.props.common.showSideMenu(true)}>
        <NationFlag lang={this.props.languageShell.currentLanguage} type="2" />
        {/* <div className="language-list">
          {deploy.supportLanguages.map((item, index) => {
            return (
              <div
                key={`language_${index}`}
                onClick={(e) => {
                  this.onSelect(e, item);
                }}
                className={`language-item ${
                  item === this.props.languageShell.currentLanguage ? " language-active" : ""
                }`}
              >
                <div className="item-info">
                  <img className="language-icon" src={this.getFlagIcon(item)} alt="" />
                  <span className="language-name">{this.props.languageShell.t(item)}</span>
                </div>

                <div className={cz("icon-select", { on: item === this.props.languageShell.currentLanguage })} />
              </div>
            );
          })}
        </div> */}
      </div>
    );
  }
}
export default inject("common", "languageShell")(observer(LanguageItem));
