import React from "react";
import { Popover } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import { nationFlagMap } from "tcg-mobile-common";

import deploy from "@/config/deploy.config";
import { remToPx } from "@/utils/dom";

import "./LanguagePopover.scss";

@inject("languageShell", "common")
@observer
class LanguagePopover extends React.Component {
  get currentLanguage() {
    return this.props.languageShell.currentLanguage;
  }
  state = { visible: false };
  getFlagIcon = (lang) => {
    const sett = nationFlagMap[lang];
    if (!sett) {
      return "";
    }
    return `${this.props.common.countryIconCDN}${sett.code}.svg`;
  };
  onSelect = (node) => {
    this.props.languageShell.changeLanguageTo(node.props.value);
    this.setState({
      visible: false,
    });
  };
  handleVisibleChange = (visible) => {
    this.setState({
      visible,
    });
  };
  renderLanguage = () => {
    return deploy.supportLanguages.map((item, index) => {
      return (
        <Popover.Item
          className={cz("language-item", { on: item === this.currentLanguage })}
          value={item}
          key={`lang_${index}`}
        >
          <img className="language-icon" src={this.getFlagIcon(item)} alt="" />
          <span>{this.props.languageShell.t(item)}</span>
        </Popover.Item>
      );
    });
  };
  render() {
    return (
      <Popover
        visible={this.state.visible}
        onVisibleChange={this.handleVisibleChange}
        overlayClassName="language-popover"
        overlayStyle={{ color: "currentColor" }}
        overlay={this.renderLanguage()}
        align={{
          overflow: { adjustY: 0, adjustX: 0 },
          offset: [remToPx(2.8), -remToPx(1.46)],
        }}
        onSelect={this.onSelect}
      >
        <div className="main-nav-item">
          <img className="item-icon lang-icon" src={this.getFlagIcon(this.currentLanguage)} />
          <div className="item-name">
            <span>{this.props.languageShell.t("hd_language")}</span>
          </div>
        </div>
      </Popover>
    );
    // return (
    //   <div className="language-wrap">
    //     <div className="language-title">{this.props.languageShell.t("language")}</div>
    //     <div className="language-list">
    //       {deploy.supportLanguages.map((item, index) => {
    //         return (
    //           <div
    //             key={`language_${index}`}
    //             onClick={(e) => {
    //               this.onSelect(e, item);
    //             }}
    //             className={`language-item ${
    //               item === this.props.languageShell.currentLanguage ? " language-active" : ""
    //             }`}
    //           >
    //             <div className="item-info">
    //               <img className="language-icon" src={this.getFlagIcon(item)} alt="" />
    //               <span className="language-name">{this.props.languageShell.t(item)}</span>
    //             </div>
    //           </div>
    //         );
    //       })}
    //     </div>
    //   </div>
    // );
  }
}
export default LanguagePopover;
