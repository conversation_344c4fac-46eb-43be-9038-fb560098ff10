import React from "react";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";
import { nationFlagMap } from "tcg-mobile-common";

import deploy from "@/config/deploy.config";
import { SimpleOption, SimpleSelect } from "@/ui/SimpleSelect";

import "./LanguageSelect.scss";

@inject("languageShell", "common")
@observer
class LanguageSelect extends React.Component {
  onChange = (val) => {
    const { languageShell } = this.props;
    languageShell.changeLanguageTo(val);
  };
  getFlagIcon = (lang) => {
    const sett = nationFlagMap[lang];
    if (!sett) {
      return "";
    }
    return `${this.props.common.countryIconCDN}${sett.code}.svg`;
  };
  get selected() {
    const lang = this.props.languageShell.currentLanguage;
    return (
      <div className="selected-lang">
        <img className="icon-language" src={this.getFlagIcon(lang)} />
        {/* <span className="selected-name">{this.props.languageShell.t(lang)}</span> */}
      </div>
    );
  }
  renderSelect = () => {
    return (
      <div className="selected-lang">
        <Icon className="icon-language" type={require("!svg-sprite-loader!@/assets/images/common/icon-language.svg")} />
      </div>
    );
  };
  openLanguage = () => {
    this.props.common.showLanguage(true);
  };
  render() {
    return (
      <div className="language-select">
        <SimpleSelect
          value={this.props.languageShell.currentLanguage}
          selected={this.selected}
          onChange={this.onChange}
          position="down"
        >
          {deploy.supportLanguages.map((item, index) => {
            return (
              <SimpleOption className="lang-title" key={`lang_${index}`} value={item}>
                <div className="lang-name">
                  <img className="icon-lang" src={this.getFlagIcon(item)} alt="" />
                  <span>{this.props.languageShell.t(item)}</span>
                </div>

                {/* {this.props.languageShell.currentLanguage === item && <i className="icon-selected" />} */}
              </SimpleOption>
            );
          })}
        </SimpleSelect>
      </div>
    );
  }
}

export default LanguageSelect;
