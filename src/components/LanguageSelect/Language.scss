.language-wrap {
  @include flex-center;
  position: relative;
  width: 50px;
  height: 50px;

  .flag-icon {
    width: 44px;
    height: 44px;
  }

  .language-bg {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
  }

  .active-lang {
    display: flex;
    align-items: center;
    padding: 0 20px;
    font-size: 28px;
    font-weight: 700;
    line-height: normal;
    color: #fff;
  }

  .lang-icon {
    width: 50px;
    height: 50px;
    margin-right: 20px;
  }

  .btn-change {
    @include flex-center;
    width: 80px;
    height: 80px;

    .am-icon {
      width: 40px;
      height: 40px;
    }
  }

  .language-title {
    padding: 20.8px 31.2px;
    font-size: 31.2px;
    line-height: 35.36px;
    color: #fff;
    text-align: center;
  }

  .language-name {
    display: inline-block;
    border-radius: 10px;
  }

  .language-icon {
    width: 44px;
    height: 44px;
    margin-right: 8px;
  }

  .icon-select {
    width: 31.18px;
    height: 31.18px;
    border: 2px solid #3b9e7a;
    border-radius: 31.18px;

    &.on {
      background: #f9fd4e;
      border: 2px solid #f9fd4e;
      border-radius: 31.18px;
    }
  }

  .language-list {
    padding: 0 22.8px;

    .language-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 80px;
      font-size: 22.8px;
      color: #fff;
    }

    .language-item.language-active {
      color: #f9fd4e;
    }

    .item-info {
      display: flex;
      align-items: center;
    }

    .icon-correct {
      width: 52px;
      height: 52px;
      margin-left: 26px;
    }
  }

  .btn-confirm {
    @include flex-center;
    width: 100%;
    height: 70px;
    margin-top: 40px;
    font-size: 28px;
    font-weight: 600;
    line-height: normal;
    color: #000;
    background: linear-gradient(90deg, #53d41a 0%, #93f388 100%);
    border-radius: 20px;
  }
}
