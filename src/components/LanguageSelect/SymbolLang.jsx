import React from "react";
import { withRouter } from "react-router";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";

import { regionList, setCurrency } from "@/config/currency.config";
import CommonPopup from "@/ui/CommonPopup/CommonPopup";

import "./SymbolLang.scss";

@inject("common", "languageShell", "tcgCommon")
@withRouter
@observer
class SymbolLang extends React.Component {
  state = {
    currency: "",
    lang: "",
  };
  get currency() {
    return this.props.tcgCommon.currency;
  }
  get language() {
    return this.props.languageShell.language;
  }

  closePopup = () => {
    this.props.common.showSymbolLang(false);
    this.setState({ currency: "", lang: "" });
  };
  getFlagIcon = (lang) => {
    if (!lang) return null;
    return `${this.props.common.countryIconCDN}${lang}.svg`;
  };
  selectSymbolLang = () => {
    const { currency, lang } = this.state;
    this.props.languageShell.changeLanguageTo(lang);
    setCurrency(currency);
    this.closePopup();
  };
  changeLang = (lang) => {
    this.props.languageShell.changeLanguageTo(lang);
  };
  langActive = (currentCurrency, currentLang) => {
    const { currency, lang } = this.state;
    if (currency && lang) {
      return currency === currentCurrency && lang === currentLang;
    }
    return currentCurrency === this.currency && currentLang === this.language;
  };
  render() {
    return (
      <CommonPopup
        className="symbol-lang-popup"
        position="left"
        show={this.props.common.symbolLangOpen}
        onClose={this.closePopup}
        closeable={false}
      >
        <div className="symbol-lang">
          <div className="symbol-lang-content">
            <div className="symbol-lang-header">
              <div className="close-sidebar" onClick={this.closePopup}>
                <Icon type={require("!svg-sprite-loader!@/assets/images/common/icon-close.svg")} />
              </div>
            </div>

            <div className="symbol-lang-title">{this.props.languageShell.t("region_language")}</div>
            <div className="region-wrap">
              {regionList.map((item) => {
                return (
                  <div className="region-item" key={item.country}>
                    <div className="symbol-item">
                      <img className="language-icon" src={this.getFlagIcon(item.country)} alt="" />
                    </div>
                    <div className="item-content">
                      <span className="country-name">{item.countryName}</span>
                      <div className="lang-list">
                        {item.langList.map((lang) => {
                          return (
                            <div
                              // className={`lang-item ${this.langActive(item.currency, lang) ? "lang-active" : ""}`}
                              className={cz("lang-item", {
                                "lang-active": this.props.languageShell.currentLanguage === lang,
                              })}
                              key={lang}
                              // onClick={() => this.setState({ currency: item.currency, lang })}
                              onClick={() => this.changeLang(lang)}
                            >
                              <div className="lang-select">
                                <Icon
                                  className="icon-check"
                                  type={require("!svg-sprite-loader!@/assets/images/common/icon-check.svg")}
                                />
                              </div>
                              <span>{this.props.languageShell.t(lang)}</span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            {/* <div className="submit-btn" onClick={this.selectSymbolLang}>
              {this.props.languageShell.t("in_increase_confirm")}
            </div> */}
          </div>
        </div>
      </CommonPopup>
    );
  }
}

export default SymbolLang;
