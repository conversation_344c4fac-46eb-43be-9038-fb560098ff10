import React from "react";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";

import deploy from "@/config/deploy.config";
import { Accordion, AccordionItem } from "@/ui/Accordion";

import "./LanguageAccordion.scss";

@inject("languageShell", "common")
@observer
class LanguageAccordion extends React.Component {
  changeLanguage = (language) => {
    this.props.languageShell.changeLanguageTo(language);
  };
  render() {
    return (
      <Accordion className="lang-accordion" activeKey="language-1">
        <AccordionItem data-key="language-1" className="lang-accordion-item">
          <div className="lang-accordion-header">
            <div className="lang-header-wrap">
              {/* <NationFlag className="lang-nav-icon" type="2" lang={this.props.languageShell.currentLanguage} /> */}
              <Icon className="icon-lang" type={require("!svg-sprite-loader!@/assets/images/common/icon-lang.svg")} />
              <div className="language-name">
                {this.props.languageShell.t(this.props.languageShell.currentLanguage)}
              </div>
            </div>

            <Icon className="arrow-down" type={require("!svg-sprite-loader!@/assets/images/common/arrow-down.svg")} />
          </div>
          <div className="lang-nav-list">
            {deploy.supportLanguages.map((item) => {
              return (
                <div
                  className={cz("lang-nav-item", { on: this.props.languageShell.currentLanguage === item })}
                  key={`lang_${item}`}
                  onClick={() => this.changeLanguage(item)}
                >
                  {/* <NationFlag className="lang-nav-icon" type="2" lang={item} /> */}
                  <span className="lang-nav-name">{this.props.languageShell.t(item)}</span>
                  {this.props.languageShell.currentLanguage === item && <i className="icon-correct" />}
                </div>
              );
            })}
          </div>
        </AccordionItem>
      </Accordion>
    );
  }
}

export default LanguageAccordion;
