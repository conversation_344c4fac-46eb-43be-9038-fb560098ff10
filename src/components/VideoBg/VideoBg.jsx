import React from "react";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import { isAndroid } from "../../utils/navigatorUtil";

import "./VideoBg.scss";

@inject("common")
@observer
class VideoBG extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      show: false,
    };
    this.supportVideoTag = this.getShowVideoTag();
  }
  componentDidMount() {
    setTimeout(() => {
      this.setState({ show: true });
    }, 500);
  }
  componentWillUnmount() {
    this.videoEl = null;
  }
  getShowVideoTag() {
    // some android video tag not support inline
    const check = isAndroid() || window.Android;
    return !check;
  }
  setRef = (c) => {
    this.videoEl = c;
    if (this.videoEl) {
      this.videoEl.setAttribute("x5-video-player-type", "h5");
      this.videoEl.setAttribute("webkit-playsinline", true);
    }
  };
  loadStart = () => {
    this.videoEl.currentTime = this.props.common.video[this.props.videoKey] || 0;
  };
  loadEnd = () => {};
  timeUpdate = () => {
    this.props.common.setVideo(this.props.videoKey, Math.ceil(this.videoEl.currentTime));
  };
  render() {
    return (
      <div className="video-wrap" style={{ backgroundImage: `url(${this.props.bg})` }}>
        {this.supportVideoTag && (
          <video
            className={cz({ hide: !this.state.show })}
            ref={this.setRef}
            onLoadStart={this.loadStart}
            onCanPlay={this.loadEnd}
            onTimeUpdate={this.timeUpdate}
            preload="auto"
            poster={this.props.bg}
            autoPlay
            muted
            playsInline
            loop
          >
            <source src={this.props.video} type="video/mp4" />
          </video>
        )}
      </div>
    );
  }
}

VideoBG.propTypes = {
  bg: PropTypes.element.isRequired,
  videoKey: PropTypes.string.isRequired,
  video: PropTypes.element.isRequired,
};

export default VideoBG;
