import React from "react";
import { withRouter } from "react-router-dom";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";

import "./GoBack.scss";

@inject("languageShell", "gameCenter")
@withRouter
@observer
class GoBack extends React.Component {
  goBack = () => {
    const { history } = this.props;
    history.push("/m/home");
    // if (history.action === "POP" || history.action === "PUSH") {
    //   history.goBack();
    // } else {
    //   history.push("/m/home");
    // }
  };
  handleReturn = () => {
    if (typeof this.props.handleReturn === "function") {
      return this.props.handleReturn();
    }
    this.props.history.goBack();
  };
  render() {
    if (this.props.type === "left") {
      return (
        <div className="page-goback" onClick={this.handleReturn}>
          <Icon className="icon-return" type={require("!svg-sprite-loader!@/assets/images/common/icon-return.svg")} />
        </div>
      );
    }
    return (
      <div className="btn-goback" onClick={this.handleReturn}>
        <img className="icon-close" src={require("@/assets/images/common/close.png")} />
      </div>
    );
  }
}

GoBack.defaultProps = {
  type: "right",
};

export default GoBack;
