import React from "react";
import { inject, observer } from "mobx-react";

import ScrollGameList from "@/components/ScrollGameList/ScrollGameList";
import { coverVassalage } from "@/config/game.config";

import "./GameProvider.scss";

@inject("gameCenter", "common", "languageShell")
@observer
class GameProvider extends React.Component {
  // get allGameVendor() {
  //   const games = this.props.gameCenter.gameVendor.sorting.reduce((prev, next) => {
  //     return [...prev, ...next.vendorNames];
  //   }, []);
  //   const seen = new Set();
  //   const result = games.filter((item) => {
  //     if (seen.has(item.vassalage)) return false;
  //     seen.add(item.vassalage);
  //     return true;
  //   });
  //   return result || [];
  // }
  get rngVendor() {
    return this.props.gameCenter.gameVendor.mapping["RNG"] || [];
  }
  render() {
    if (this.rngVendor?.length < 1) {
      return null;
    }
    return (
      <ScrollGameList className="game-provider" title={this.props.languageShell.t("providers")} rows={1}>
        {this.rngVendor.map((item, index) => {
          return (
            <div className="provider-item" key={`game_provider_${item.id}_${index}`}>
              <img
                className="provider-icon"
                src={`${this.props.common.vendorIconCDN}/${coverVassalage(item.vassalage)}-COLOR.png`}
                alt=""
              />
            </div>
          );
        })}
      </ScrollGameList>
    );
  }
}

export default GameProvider;
