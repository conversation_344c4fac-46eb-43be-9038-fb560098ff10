import React from "react";
import dayjs from "dayjs";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import "./LoginTime.scss";

const LOGIN_TIME = "LOGIN_TIME";

@inject("auth", "common", "mcCommon")
@observer
class LoginTime extends React.Component {
  state = { loginTime: "" };

  componentDidMount() {
    if (!!this.props.token) {
      this.startTimer();
    }
  }
  componentDidUpdate(prevProps) {
    if (!prevProps.token && !!this.props.token) {
      this.startTimer();
    }
  }
  componentWillUnmount() {
    this.stopTimer(false); // 停止计时器但不清理 localStorage
  }

  startTimer = () => {
    clearInterval(this.timer);
    let loginTime = localStorage.getItem(LOGIN_TIME);
    if (!loginTime) {
      loginTime = dayjs().toISOString();
      localStorage.setItem(LOGIN_TIME, loginTime);
    }
    const startTime = dayjs(loginTime);
    this.timer = setInterval(() => {
      const now = dayjs();
      const duration = now.diff(startTime, "second");
      const hours = Math.floor(duration / 3600)
        .toString()
        .padStart(2, "0");
      const minutes = Math.floor((duration % 3600) / 60)
        .toString()
        .padStart(2, "0");
      const seconds = (duration % 60).toString().padStart(2, "0");

      this.setState({
        loginTime: `${hours}:${minutes}:${seconds}`,
      });
    }, 1000);
  };

  stopTimer(clearLocalStorage) {
    clearInterval(this.timer);
    this.setState({
      loginTime: "",
    });
    if (clearLocalStorage) {
      localStorage.removeItem(LOGIN_TIME);
    }
  }

  render() {
    // if (!this.props.token) {
    //   return null;
    // }
    return (
      <div className="login-time-wrap">
        <span>{this.state.loginTime}</span>
      </div>
    );
  }
}

LoginTime.propTypes = {
  token: PropTypes.string,
};

LoginTime.defaultProps = {
  token: "",
};

export default LoginTime;
