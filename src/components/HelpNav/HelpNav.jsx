import React, { Component } from "react";
import { withRouter } from "react-router";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import "./HelpNav.scss";

@inject("languageShell", "common")
@withRouter
@observer
class HelpNav extends Component {
  get helpList() {
    return this.props.common.helpList;
  }
  navClick = (i) => {
    this.props.history.push(`/m/helpDetail?i=${i}`);
  };
  render() {
    return (
      <div className={cz("help-nav-wrap", this.props.className)}>
        <div className="footer-title">{this.props.languageShell.t("help_center")}</div>
        <div className="help-list-nav footer-list-nav">
          {this.helpList.map((item, index) => {
            return (
              <div className="nav-item" key={`help-item-${index}`} onClick={() => this.navClick(index)}>
                {item.title}
              </div>
            );
          })}
        </div>
      </div>
    );
  }
}

HelpNav.propTypes = {
  className: PropTypes.string,
};

HelpNav.defaultProps = {
  className: "",
};

export default HelpNav;
