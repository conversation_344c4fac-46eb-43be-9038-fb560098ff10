import React from "react";
import cz from "classnames";
import PropTypes from "prop-types";
import Qs from "qs";

const transformCloudflareStreamUrl = (url, { autoplay, controls, loop }) => {
  if (!url.includes("cloudflarestream.com")) return null;

  const [baseUrl] = url.split("?");

  let streamUrl = baseUrl.endsWith("/watch") ? baseUrl.replace("/watch", "/iframe") : baseUrl;

  const params = {
    controls,
    loop,
  };

  if (autoplay) {
    params.autoplay = true;
    params.muted = true;
  }

  streamUrl += "?" + Qs.stringify(params);

  return streamUrl;
};

const CloudflareStreamVideo = ({ className, src, autoplay, controls, loop, backgroundImage, style }) => {
  if (!src) {
    return null;
  }

  const iframeSrc = transformCloudflareStreamUrl(src, { autoplay, controls, loop });

  const rootStyle = {
    position: "relative",
    width: "100%",
    aspectRatio: "16/9",
  };

  if (backgroundImage) {
    rootStyle.backgroundRepeat = "no-repeat";
    rootStyle.backgroundPosition = "center";
    rootStyle.backgroundSize = "cover";
  }

  return (
    <div
      className={cz("cloudflare-iframe-wrap", className)}
      style={{ backgroundImage: backgroundImage ? `url(${backgroundImage})` : undefined, ...rootStyle, ...style }}
    >
      <iframe
        title="cloudflare stream"
        src={iframeSrc}
        style={{ display: "block", width: "100%", height: "100%", pointerEvents: !controls ? "none" : undefined }}
        allow="autoplay; encrypted-media; fullscreen; picture-in-picture"
      />
    </div>
  );
};

CloudflareStreamVideo.propTypes = {
  className: PropTypes.string,
  src: PropTypes.string.isRequired,
  autoplay: PropTypes.bool,
  controls: PropTypes.bool,
  loop: PropTypes.bool,
  backgroundImage: PropTypes.string,
};

CloudflareStreamVideo.defaultProps = {
  className: "",
  autoplay: false,
  controls: true,
  loop: false,
};

export default CloudflareStreamVideo;
