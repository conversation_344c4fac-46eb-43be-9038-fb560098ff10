import React from "react";
import cz from "classnames";
import { isEqual } from "lodash";
import { inject, observer } from "mobx-react";

import GameListItem from "@/components/GameListItem/GameListItem";
import deploy from "@/config/deploy.config";

import JackPotNum from "./JackpotNum";

import "./Jackpot.scss";
@inject("languageShell", "wallet", "tcgCommon", "gameCenter")
@observer
class JackPot extends React.Component {
  get hotGames() {
    return this.props.gameCenter.hotGames;
  }
  get repeatedGames() {
    const gamesCount = this.hotGames.length;
    if (gamesCount === 0) return [];
    const repeatTimes = Math.ceil(50 / gamesCount);
    const repeated = [];
    for (let i = 0; i < repeatTimes; i++) {
      repeated.push(...this.hotGames);
    }
    repeated.push(this.hotGames[0]);
    return repeated || [];
  }
  constructor(props) {
    super(props);
    this.state = {
      gameList: [],
      slots: new Array(3).fill(0),
      liHeight: (262 + 12) / 100, // (height + margin) /rem
      spinning: false,
    };
    this.timer = null;
    this.scrollTimer = null;
    this.scrollEl = {};
  }
  componentDidMount() {
    // this.getGameList();
    if (this.hotGames?.length > 0) {
      this.setInitialPositions();
    }
  }
  componentDidUpdate(prevProps, prevState) {
    const prevGames = prevProps.gameCenter.hotGames || [];
    const currentGames = this.props.gameCenter.hotGames || [];

    if (!isEqual(prevGames, currentGames) && currentGames?.length > 0) {
      this.setInitialPositions();
    }
  }
  getGameList = () => {
    this.props.gameCenter
      .getGameList({
        merchant: deploy.merchant,
        platform: "html5",
        gameName: "",
        gameType: "RNG",
        pageNo: 1,
        pageSize: 18,
        vassalage: "",
        gameClassify: "JP",
        minBet: "",
        minLine: "",
        language: this.props.languageShell.language,
      })
      .then((res) => {
        this.setState({
          gameList: res.games,
        });
      });
  };
  setInitialPositions = () => {
    const gamesCount = this.repeatedGames.length;
    if (gamesCount === 0) return;
    const slots = Array.from({ length: 3 }, () => Math.floor(Math.random() * gamesCount));
    this.setState({ slots }, () => {
      this.autoSpin();
    });
  };
  spin = () => {
    if (this.state.spinning) return;
    const slots = this.state.slots.map(() => {
      return Math.floor(Math.random() * this.repeatedGames.length);
    });
    this.setState({ spinning: true, slots });

    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      this.setState({ spinning: false });
    }, 5000);
  };
  updatePositions = () => {
    const slots = this.state.slots.map((pos) => {
      return (pos + 1) % this.repeatedGames.length;
    });
    this.setState({ slots });
  };
  autoSpin = () => {
    this.scrollTimer = setInterval(() => {
      if (!this.state.spinning) {
        this.updatePositions();
      }
    }, 2010);
  };
  setStyle = (index) => {
    return {
      transform: this.setTranslateY(index),
      transition: this.setTransition(index),
    };
  };
  setTranslateY = (index) => {
    const { slots, liHeight } = this.state;
    return `translateY(-${slots[index] * liHeight}rem)`;
  };
  setTransition = (index) => {
    if (this.state.slots[index] === 0) return "unset";
    return this.state.spinning ? `transform ${1 + index * 0.5}s ease-out` : `transform 2s linear`;
  };
  render() {
    const { slots } = this.state;
    return (
      <div className={cz("jackpot-wrap", this.props.className)}>
        {/* <div className="jackpot-title">{this.props.languageShell.t("in_accumulative_prize")}</div> */}
        <div className="jackpot-content">
          <img className="jackpot-bg" src={require("./images/jackpot-bg.png")} alt="" />
          <img className="control-stick up" src={require("./images/control-stick-up.png")} alt="" />
          <img className="control-stick down" src={require("./images/control-stick-down.png")} alt="" />

          <div className={cz("slot-machine-wrap")}>
            {slots.map((item, index) => {
              return (
                <div
                  className="slot-machine-list"
                  key={`list_${index}`}
                  style={this.setStyle(index)}
                  ref={(c) => (this.scrollEl[index] = c)}
                >
                  {this.repeatedGames.map((item, i) => {
                    return <GameListItem key={`${item.gameId}_${i}`} item={item} />;
                  })}
                </div>
              );
            })}
          </div>

          <div className="spin-btn" onClick={this.spin} />

          <div className="jackpot-box">
            <div className="jackpot-num">
              {/* <span className="symbol">{this.props.tcgCommon.currencySymbol}</span> */}
              <JackPotNum minNum={10000000} maxNum={15000000} decimal={0} numHeight={100} animationType="translate" />
            </div>
          </div>
          {/* <div className="jackpot-game-wrap">
            <div className="jackpot-game-swiper" ref={(c) => (this.gameSwiper = c)}>
              <div className="jackpot-game-list swiper-wrapper">
                {this.state.gameList.map((item, index) => {
                  return (
                    <div className="swiper-slide" key={`jp_${index}`}>
                      <div className={`jp-game-item`} onClick={() => this.launchGame(item)}>
                        <div className="game-background">
                          <LazyLoadImage
                            className="img-loading"
                            src={item.showIcon}
                            errorSrc={this.props.common.defaultImage}
                          />
                        </div>
                        <div className="game-item-info">
                          <div className="game-amount">
                            <span className="symbol">{this.props.tcgCommon.currencySymbol}</span>
                            <span>{this.getAmount()}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div> */}
        </div>
      </div>
    );
  }
}

export default JackPot;
