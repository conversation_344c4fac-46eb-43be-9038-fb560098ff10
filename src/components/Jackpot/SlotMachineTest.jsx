import React, { Component } from "react";
import SlotMachine from "./SlotMachine";

// 测试组件，用于验证 CSS 变量是否正常工作
class SlotMachineTest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      itemHeight: 150,
      spaceBetween: 20,
      games: [
        {
          gameId: "1",
          gameName: "Test Game 1",
          showIcon: "https://via.placeholder.com/150x150/ff6b35/ffffff?text=Game1",
          nodeId: 1,
          gameType: "RNG",
        },
        {
          gameId: "2",
          gameName: "Test Game 2", 
          showIcon: "https://via.placeholder.com/150x150/4CAF50/ffffff?text=Game2",
          nodeId: 2,
          gameType: "RNG",
        },
        {
          gameId: "3",
          gameName: "Test Game 3",
          showIcon: "https://via.placeholder.com/150x150/2196F3/ffffff?text=Game3", 
          nodeId: 3,
          gameType: "RNG",
        },
      ],
    };
  }

  handleItemHeightChange = (e) => {
    this.setState({ itemHeight: parseInt(e.target.value) });
  };

  handleSpaceBetweenChange = (e) => {
    this.setState({ spaceBetween: parseInt(e.target.value) });
  };

  render() {
    const { itemHeight, spaceBetween, games } = this.state;

    return (
      <div style={{ padding: "20px" }}>
        <h2>SlotMachine CSS 变量测试</h2>
        
        <div style={{ marginBottom: "20px" }}>
          <label style={{ display: "block", marginBottom: "10px" }}>
            Item Height: {itemHeight}px
            <input
              type="range"
              min="100"
              max="300"
              value={itemHeight}
              onChange={this.handleItemHeightChange}
              style={{ marginLeft: "10px" }}
            />
          </label>
          
          <label style={{ display: "block", marginBottom: "10px" }}>
            Space Between: {spaceBetween}px
            <input
              type="range"
              min="5"
              max="50"
              value={spaceBetween}
              onChange={this.handleSpaceBetweenChange}
              style={{ marginLeft: "10px" }}
            />
          </label>
        </div>

        <div style={{ border: "2px solid #ccc", padding: "20px", borderRadius: "10px" }}>
          <SlotMachine
            games={games}
            itemHeight={itemHeight}
            spaceBetween={spaceBetween}
            showSpinButton={true}
            onSpin={() => console.log("Test spin!")}
          />
        </div>

        <div style={{ marginTop: "20px", fontSize: "12px", color: "#666" }}>
          <p>打开浏览器开发者工具查看控制台输出和元素样式</p>
          <p>检查 .slot-machine-container 元素的 style 属性是否包含 CSS 变量</p>
        </div>
      </div>
    );
  }
}

export default SlotMachineTest;
