# SlotMachine 组件

一个独立的老虎机组件，从原来的 Jackpot 组件中提取出来，支持外部传入游戏数据和自定义尺寸。

## 功能特性

- 🎰 3列老虎机滚动效果
- 🎮 支持外部传入游戏数据
- 🎯 可控制的旋转按钮显示/隐藏
- 🎨 支持自定义样式
- 📏 支持自定义项目高度和间距
- 🔄 自动循环滚动
- ⚡ 平滑的动画效果

## Props

| 属性名 | 类型 | 默认值 | 必填 | 描述 |
|--------|------|--------|------|------|
| games | Array | [] | ✅ | 游戏数据数组 |
| className | String | "" | ❌ | 自定义CSS类名 |
| showSpinButton | Boolean | true | ❌ | 是否显示旋转按钮 |
| onSpin | Function | null | ❌ | 旋转时的回调函数 |
| itemHeight | Number | 210 | ❌ | 每个游戏项的高度(px) |
| spaceBetween | Number | 12 | ❌ | 游戏项之间的间距(px) |

## 游戏数据格式

```javascript
const games = [
  {
    gameId: "1",           // 游戏ID
    gameName: "Game 1",    // 游戏名称
    showIcon: "url",       // 游戏图标URL
    nodeId: 1,             // 节点ID
    gameType: "RNG",       // 游戏类型
    // ... 其他GameListItem需要的属性
  }
];
```

## 基本用法

```jsx
import SlotMachine from "@/components/Jackpot/SlotMachine";

// 在组件中使用
<SlotMachine 
  games={gameList}
  onSpin={() => console.log("旋转开始!")}
/>
```

## 高级用法

### 不显示旋转按钮
```jsx
<SlotMachine 
  games={gameList}
  showSpinButton={false}
/>
```

### 自定义尺寸
```jsx
<SlotMachine 
  games={gameList}
  itemHeight={180}
  spaceBetween={8}
/>
```

### 自定义样式
```jsx
<SlotMachine 
  games={gameList}
  className="my-custom-slot-machine"
/>
```

### 带回调函数
```jsx
<SlotMachine 
  games={gameList}
  onSpin={() => {
    // 播放音效
    playSpinSound();
    // 记录统计
    trackSpinEvent();
  }}
/>
```

### 通过 ref 控制旋转 (React 15 语法)
```jsx
class MyComponent extends Component {
  constructor(props) {
    super(props);
    this.slotMachineRef = null;
  }

  handleExternalSpin = () => {
    if (this.slotMachineRef) {
      this.slotMachineRef.spin();
    }
  };

  render() {
    return (
      <div>
        <SlotMachine 
          ref={(ref) => this.slotMachineRef = ref}
          games={gameList}
          showSpinButton={false}
          itemHeight={200}
          spaceBetween={10}
        />
        <button onClick={this.handleExternalSpin}>
          外部旋转按钮
        </button>
      </div>
    );
  }
}
```

## 公开方法

通过 ref 可以调用以下方法：

| 方法名 | 参数 | 返回值 | 描述 |
|--------|------|--------|------|
| spin() | 无 | 无 | 手动触发旋转 |
| isSpinning() | 无 | Boolean | 检查是否正在旋转 |

## 样式定制

组件提供了以下CSS类名供自定义：

- `.slot-machine-container` - 容器
- `.slot-machine-wrap` - 老虎机包装器
- `.slot-machine-list` - 单列滚动容器
- `.spin-btn` - 旋转按钮

### 自定义样式示例

```scss
.my-custom-slot-machine {
  .slot-machine-wrap {
    border: 2px solid #gold;
    border-radius: 10px;
  }
  
  .spin-btn {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
  }
}
```

## 尺寸说明

- `itemHeight`: 控制每个游戏项的高度，单位为像素
- `spaceBetween`: 控制游戏项之间的间距，单位为像素
- 组件会自动计算滚动距离，确保动画效果正确

## 注意事项

1. 确保传入的 `games` 数组不为空，否则组件不会正常工作
2. 游戏数据需要包含 `GameListItem` 组件所需的必要字段
3. 组件会自动重复游戏数据以确保滚动效果的连续性
4. 旋转动画持续5秒，期间不能再次触发旋转
5. 本组件兼容 React 15，使用传统的 ref 回调函数语法
6. `itemHeight` 和 `spaceBetween` 的变化会实时影响滚动计算

## 在原有项目中的集成

原来的 Jackpot 组件已经更新为使用新的 SlotMachine 组件：

```jsx
// Jackpot.jsx 中的使用
<SlotMachine 
  ref={(ref) => this.slotMachineRef = ref}
  games={this.hotGames} 
  className="jackpot-slot-machine"
  showSpinButton={false}
  itemHeight={210}
  spaceBetween={12}
/>
```

这样既保持了原有功能，又提供了更好的组件复用性和自定义能力。
