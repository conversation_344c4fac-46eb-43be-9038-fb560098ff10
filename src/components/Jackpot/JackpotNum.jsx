import React from "react";
import { floor, random } from "lodash";
import PropTypes from "prop-types";
import { currencyFormat } from "tcg-mobile-common";

import ScrollNum from "./ScrollNum";

import "./JackpotNum.scss";

class JackPotNum extends React.Component {
  constructor(props) {
    super(props);
    const initialAmount = floor(random(this.props.minNum, this.props.maxNum, true), this.props.decimal);
    this.state = {
      amount: initialAmount,
      displayAmount: initialAmount,
      prevAmount: initialAmount,
    };
    this.count = 0;
    this.timeArr = [3000, 4000, 5000];
    this.incrementSteps = 20;
  }
  componentDidMount() {
    this.incrementNum();
  }
  componentWillUnmount() {
    clearTimeout(this.timer);
  }
  incrementNum = () => {
    this.timer = setTimeout(
      () => {
        this.count = floor(random(100, 200, true), this.props.decimal);

        if (this.props.animationType === "increment") {
          const stepSize = this.count / this.incrementSteps;
          let currentStep = 0;

          const stepInterval = setInterval(() => {
            currentStep++;
            this.setState((state) => ({
              amount: state.amount + stepSize,
              prevAmount: state.displayAmount,
              displayAmount: state.amount + stepSize,
            }));

            if (currentStep >= this.incrementSteps) {
              clearInterval(stepInterval);
            }
          }, 50);
        } else {
          this.setState((state) => ({
            amount: state.amount + this.count,
            prevAmount: state.displayAmount,
            displayAmount: state.amount + this.count,
          }));
        }

        clearTimeout(this.timer);
        this.incrementNum();
      },
      this.timeArr[Math.floor(Math.random() * this.timeArr.length)]
    );
  };
  get formatAmount() {
    const amount = this.props.animationType === "increment" ? this.state.displayAmount : this.state.amount;
    return currencyFormat.format({ value: amount, fixed: this.props.decimal });
  }
  render() {
    return (
      <div className="jackpot-num-list" style={{ height: `${this.props.numHeight / 100}rem` }}>
        {this.formatAmount.split("").map((item, index) => {
          return [",", "."].includes(item) ? (
            <div className="point" key={index}>
              {item}
            </div>
          ) : (
            <ScrollNum
              num={item}
              numHeight={this.props.numHeight}
              key={index}
              animationType={this.props.animationType}
            />
          );
        })}
      </div>
    );
  }
}

JackPotNum.propTypes = {
  maxNum: PropTypes.number,
  minNum: PropTypes.number,
  decimal: PropTypes.number,
  numHeight: PropTypes.number,
  animationType: PropTypes.oneOf(["translate", "increment"]),
};

JackPotNum.defaultProps = {
  maxNum: 1300000000,
  minNum: 1200000000,
  decimal: 0,
  numHeight: 62,
  animationType: "translate",
};

export default JackPotNum;
