import React from "react";

import "./ScrollNum.scss";

class ScrollNum extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      currentNum: props.num,
    };
  }

  componentDidUpdate(prevProps) {
    if (this.props.animationType === "increment" && prevProps.num !== this.props.num) {
      this.animateNumber(prevProps.num, this.props.num);
    }
  }

  animateNumber = (start, end) => {
    const duration = 1000; // 缩短动画时间，让变化更快速流畅
    const startTime = performance.now();
    const startNum = parseFloat(start);
    const endNum = parseFloat(end);

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用简单的 easeOut 缓动函数，效果更自然
      const easeOut = (t) => (t === 1 ? 1 : 1 - Math.pow(2, -10 * t));
      const currentValue = startNum + (endNum - startNum) * easeOut(progress);

      this.setState({
        currentNum: Math.round(currentValue),
      });

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  };

  get translateY() {
    return `-${(this.props.num * this.props.numHeight) / 100}rem`;
  }

  get itemHeight() {
    return `${this.props.numHeight / 100}rem`;
  }

  render() {
    const { animationType } = this.props;
    const { currentNum } = this.state;

    if (animationType === "increment") {
      return (
        <div className="scroll-num static-num" style={{ height: this.itemHeight }}>
          {currentNum}
        </div>
      );
    }

    return (
      <div className="scroll-num">
        <ul className="animate-list" style={{ transform: `translateY(${this.translateY})` }}>
          {Array.from(new Array(10).keys()).map((item) => (
            <li style={{ height: this.itemHeight }} key={item}>
              {item}
            </li>
          ))}
        </ul>
      </div>
    );
  }
}

ScrollNum.defaultProps = {
  num: 0,
  numHeight: 0,
  animationType: "translate",
};

export default ScrollNum;
