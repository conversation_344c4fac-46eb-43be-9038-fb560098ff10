import React from "react";
import { inject, observer } from "mobx-react";

import WinnerBoard from "../WinnerBoard/WinnerBoard";

import JackPotNum from "./JackpotNum";

import "./JackpotWinner.scss";

@inject("languageShell", "tcgCommon")
@observer
class JackpotWinner extends React.Component {
  render() {
    return (
      <div className="jackpot-winner-wrap">
        <div className="jackpot-bg">
          <img className="jackpot-banner" src={require("./images/jackpot-bg2.png")} alt="" />
          <div className="jackpot-adorn">
            <img className="adorn-left" src={require("./images/adorn-1.png")} alt="" />
            <img className="jackpot-title" src={require("./images/jackpot-title.png")} alt="" />
            <img className="adorn-right" src={require("./images/adorn-2.png")} alt="" />
          </div>
          <img className="adorn-bottom" src={require("./images/adorn-3.png")} alt="" />
          <div className="jackpot-num">
            <span className="symbol">{this.props.tcgCommon.currencySymbol}</span>
            <JackPotNum minNum={10000000} maxNum={15000000} decimal={2} numHeight={50} />
          </div>
        </div>

        <WinnerBoard />
      </div>
    );
  }
}

export default JackpotWinner;
