import React, { Component } from "react";
import cz from "classnames";
import PropTypes from "prop-types";

import GameListItem from "@/components/GameListItem/GameListItem";

import "./SlotMachine.scss";

class SlotMachine extends Component {
  constructor(props) {
    super(props);
    this.state = {
      slots: new Array(3).fill(0),
      spinning: false,
    };
    this.timer = null;
    this.scrollTimer = null;
    this.scrollEl = {};
    this.containerRef = null;
  }

  get liHeight() {
    const { itemHeight = 210, spaceBetween = 12 } = this.props;
    return (itemHeight + spaceBetween) / 100;
  }

  get repeatedGames() {
    const { games } = this.props;
    const gamesCount = games.length;
    if (gamesCount === 0) return [];
    const repeatTimes = Math.ceil(50 / gamesCount);
    const repeated = [];
    for (let i = 0; i < repeatTimes; i++) {
      repeated.push(...games);
    }
    repeated.push(games[0]);
    return repeated || [];
  }

  componentDidMount() {
    if (this.props.games?.length > 0) {
      this.setInitialPositions();
    }
    this.updateCSSVariables();
  }

  componentDidUpdate(prevProps) {
    const prevGames = prevProps.games || [];
    const currentGames = this.props.games || [];

    if (prevGames !== currentGames && currentGames?.length > 0) {
      this.setInitialPositions();
    }

    // 如果尺寸参数发生变化，更新 CSS 变量
    if (prevProps.itemHeight !== this.props.itemHeight || prevProps.spaceBetween !== this.props.spaceBetween) {
      this.updateCSSVariables();
    }
  }

  componentWillUnmount() {
    clearInterval(this.scrollTimer);
    clearTimeout(this.timer);
  }

  updateCSSVariables = () => {
    if (this.containerRef) {
      const { itemHeight = 210, spaceBetween = 12 } = this.props;
      this.containerRef.style.setProperty("--item-height", `${itemHeight}px`);
      this.containerRef.style.setProperty("--space-between", `${spaceBetween}px`);
      console.log("CSS variables updated via DOM:", { itemHeight, spaceBetween });
    }
  };

  setInitialPositions = () => {
    const gamesCount = this.repeatedGames.length;
    if (gamesCount === 0) return;
    const slots = Array.from({ length: 3 }, () => Math.floor(Math.random() * gamesCount));
    this.setState({ slots }, () => {
      this.autoSpin();
    });
  };

  spin = () => {
    if (this.state.spinning) return;
    const slots = this.state.slots.map(() => {
      return Math.floor(Math.random() * this.repeatedGames.length);
    });
    this.setState({ spinning: true, slots });

    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      this.setState({ spinning: false });
    }, 5000);

    if (this.props.onSpin) {
      this.props.onSpin();
    }
  };

  isSpinning = () => {
    return this.state.spinning;
  };

  updatePositions = () => {
    const slots = this.state.slots.map((pos) => {
      return (pos + 1) % this.repeatedGames.length;
    });
    this.setState({ slots });
  };

  autoSpin = () => {
    this.scrollTimer = setInterval(() => {
      if (!this.state.spinning) {
        this.updatePositions();
      }
    }, 2010);
  };

  setStyle = (index) => {
    return {
      transform: this.setTranslateY(index),
      transition: this.setTransition(index),
    };
  };

  setTranslateY = (index) => {
    const { slots } = this.state;
    return `translateY(-${slots[index] * this.liHeight}rem)`;
  };

  setTransition = (index) => {
    if (this.state.slots[index] === 0) return "unset";
    return this.state.spinning ? `transform ${1 + index * 0.5}s ease-out` : `transform 2s linear`;
  };

  render() {
    const { slots } = this.state;
    const { className, showSpinButton = true, spaceBetween = 12, itemHeight = 210 } = this.props;

    // 调试信息
    console.log("SlotMachine props:", { itemHeight, spaceBetween });

    const containerStyle = {
      "--space-between": `${spaceBetween}px`,
      "--item-height": `${itemHeight}px`,
    };

    // 调试信息
    console.log("containerStyle:", containerStyle);

    return (
      <div
        className={cz("slot-machine-container", className)}
        style={containerStyle}
        ref={(ref) => (this.containerRef = ref)}
      >
        <div className={cz("slot-machine-wrap")}>
          {slots.map((_, index) => {
            return (
              <div
                className="slot-machine-list"
                key={`list_${index}`}
                style={this.setStyle(index)}
                ref={(c) => (this.scrollEl[index] = c)}
              >
                {this.repeatedGames.map((item, i) => {
                  return <GameListItem key={`${item.gameId}_${i}`} item={item} visibleByDefault={true} />;
                })}
              </div>
            );
          })}
        </div>

        {showSpinButton && <div className="spin-btn" onClick={this.spin} />}
      </div>
    );
  }
}

SlotMachine.propTypes = {
  games: PropTypes.array.isRequired,
  className: PropTypes.string,
  showSpinButton: PropTypes.bool,
  onSpin: PropTypes.func,
  itemHeight: PropTypes.number,
  spaceBetween: PropTypes.number,
};

SlotMachine.defaultProps = {
  games: [],
  className: "",
  showSpinButton: true,
  onSpin: null,
  itemHeight: 210,
  spaceBetween: 12,
};

export default SlotMachine;
