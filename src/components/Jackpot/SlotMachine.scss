.slot-machine-container {
  position: relative;

  --space-between: 12px; /* 默认值，可以通过 style 属性覆盖 */
  --item-height: 210px; /* 默认值，可以通过 style 属性覆盖 */

  .slot-machine-wrap {
    position: relative;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-between);
    width: 100%;
    height: 210px;
    overflow: hidden;

    .slot-machine-list {
      display: flex;
      flex-direction: column;
      gap: var(--space-between);
      min-width: 0;
    }

    .game-list-item {
      height: var(--item-height);

      .game-background {
        height: 100%;
        border-radius: 12px;
      }

      .game-item-info,
      .game-tag,
      .game-fav {
        display: none;
      }
    }
  }

  .spin-btn {
    position: absolute;
    bottom: -50px;
    left: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 40px;
    font-weight: bold;
    color: white;
    cursor: pointer;
    background: #ff6b35;
    border-radius: 20px;
    transform: translateX(-50%);

    &:hover {
      background: #e55a2b;
    }

    &:active {
      transform: translateX(-50%) scale(0.95);
    }

    &::after {
      content: "SPIN";
    }
  }
}
