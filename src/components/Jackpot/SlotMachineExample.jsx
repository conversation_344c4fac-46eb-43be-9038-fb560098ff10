import React, { Component } from "react";
import SlotMachine from "./SlotMachine";

// 使用示例组件 (React 15 兼容)
class SlotMachineExample extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // 示例游戏数据
      games: [
        {
          gameId: "1",
          gameName: "Game 1",
          showIcon: "https://example.com/game1.jpg",
          nodeId: 1,
          gameType: "RNG",
        },
        {
          gameId: "2", 
          gameName: "Game 2",
          showIcon: "https://example.com/game2.jpg",
          nodeId: 2,
          gameType: "RNG",
        },
        {
          gameId: "3",
          gameName: "Game 3", 
          showIcon: "https://example.com/game3.jpg",
          nodeId: 3,
          gameType: "RNG",
        },
      ],
    };
    // React 15 兼容的 ref 方式
    this.slotMachineRef = null;
  }

  handleSpin = () => {
    console.log("Slot machine is spinning!");
    // 可以在这里添加自定义逻辑，比如播放音效、更新状态等
  };

  handleExternalSpin = () => {
    if (this.slotMachineRef) {
      this.slotMachineRef.spin();
    }
  };

  render() {
    return (
      <div style={{ padding: "20px" }}>
        <h2>SlotMachine 组件使用示例 (React 15)</h2>
        
        {/* 基本用法 */}
        <div style={{ marginBottom: "40px" }}>
          <h3>基本用法（带旋转按钮）</h3>
          <SlotMachine 
            games={this.state.games}
            onSpin={this.handleSpin}
          />
        </div>

        {/* 自定义尺寸 */}
        <div style={{ marginBottom: "40px" }}>
          <h3>自定义尺寸</h3>
          <SlotMachine 
            games={this.state.games}
            itemHeight={180}
            spaceBetween={8}
            onSpin={this.handleSpin}
          />
        </div>

        {/* 不显示旋转按钮 */}
        <div style={{ marginBottom: "40px" }}>
          <h3>不显示旋转按钮</h3>
          <SlotMachine 
            games={this.state.games}
            showSpinButton={false}
            itemHeight={150}
            spaceBetween={6}
            onSpin={this.handleSpin}
          />
        </div>

        {/* 通过 ref 控制 */}
        <div style={{ marginBottom: "40px" }}>
          <h3>通过 ref 控制旋转</h3>
          <SlotMachine 
            ref={(ref) => this.slotMachineRef = ref}
            games={this.state.games}
            showSpinButton={false}
            itemHeight={200}
            spaceBetween={10}
            onSpin={this.handleSpin}
          />
          <button 
            onClick={this.handleExternalSpin}
            style={{ 
              marginTop: "10px", 
              padding: "10px 20px",
              backgroundColor: "#ff6b35",
              color: "white",
              border: "none",
              borderRadius: "5px",
              cursor: "pointer"
            }}
          >
            外部旋转按钮
          </button>
        </div>

        {/* 自定义样式 */}
        <div style={{ marginBottom: "40px" }}>
          <h3>自定义样式</h3>
          <SlotMachine 
            games={this.state.games}
            className="custom-slot-machine"
            itemHeight={220}
            spaceBetween={15}
            onSpin={this.handleSpin}
          />
        </div>
      </div>
    );
  }
}

export default SlotMachineExample;
