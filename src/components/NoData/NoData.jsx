import React from "react";
import { inject, observer } from "mobx-react";

import "./NoData.scss";

const NoData = (props) => {
  return (
    <div className="no-data-wrap">
      <div className="no-data-content">
        <img className="data-icon" src={require("./images/no-record.png")} alt="" />
        <p>{props.title || props.languageShell.t("in_no_data")}</p>
      </div>
    </div>
  );
};
export default inject("languageShell")(observer(NoData));
