import React from "react";
import { with<PERSON>out<PERSON> } from "react-router";
import { inject, observer } from "mobx-react";

import { gamePath, navConfig, pathGameType } from "@/config/game.config";
import { SimpleOption, SimpleSelect } from "@/ui/SimpleSelect";

import "./GameCategorySelect.scss";

@inject("common", "languageShell", "gameCenter", "auth")
@withRouter
@observer
class GameCategorySelect extends React.Component {
  get isLogin() {
    return this.props.auth.isLogin;
  }
  get sorting() {
    return this.props.gameCenter.gameVendor.sorting || [];
  }
  get menus() {
    return [{ gameCategory: "HOT" }, ...(this.isLogin ? [{ gameCategory: "FAV" }] : []), ...this.sorting];
  }
  handleClean = () => {
    this.props.history.push("/m/home");
  };
  getSelected = () => {
    const gameType = pathGameType[location.pathname];
    if (gameType) {
      return (
        <div className="game-selected">
          <img className="category-icon" src={navConfig[gameType]?.img} />
          <span className="category-name">{this.props.languageShell.t(navConfig[gameType]?.name)}</span>
        </div>
      );
    }
    return (
      <div className="select-holder">
        <span>{this.props.languageShell.t("choose_category")}</span>
      </div>
    );
  };
  onChange = (gameCategory) => {
    const path = gamePath[gameCategory];
    if (path) {
      return this.props.history.push(path);
    }
  };
  render() {
    if (this.sorting?.length < 1) {
      return null;
    }
    return (
      <SimpleSelect
        className="game-category-select"
        value={pathGameType[location.pathname]}
        selected={this.getSelected()}
        onChange={this.onChange}
        hasArrow
      >
        {this.menus.map((item, index) => {
          return (
            <SimpleOption className="category-item" key={`category_${index}`} value={item.gameCategory}>
              <img className="category-icon" src={navConfig[item.gameCategory]?.img} />
              <span className="category-name">{this.props.languageShell.t(navConfig[item.gameCategory]?.name)}</span>
            </SimpleOption>
          );
        })}
      </SimpleSelect>
    );
  }
}

export default GameCategorySelect;
