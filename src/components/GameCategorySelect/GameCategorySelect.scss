.game-category-select {
  flex-shrink: 0;
  height: 100%;

  .select-wrap {
    height: 100%;
    font-size: 38px;
    font-weight: 700;
    line-height: normal;
    color: #e2e6e9;
  }

  .category-icon {
    display: block;
    width: 40px;
    height: 40px;
    object-fit: contain;
  }

  .game-selected {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .option-bg {
    width: 344px;
    background: #141515;
    border-radius: 6px;
    box-shadow: 0 0 15px 0 rgb(0 0 0 / 50%);

    .option-item {
      width: 100%;
      height: 104px;
      font-size: 28px;
      line-height: normal;
      color: #e2e6e9;
    }

    .option-item.selected {
      background: #303232;
    }

    .category-item {
      display: flex;
      gap: 30px;
      align-items: center;
      width: 100%;
      height: 100%;
      padding: 0 32px;
    }
  }
}
