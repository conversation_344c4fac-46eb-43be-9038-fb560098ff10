import React from "react";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";

import { coverVassalage } from "@/config/game.config";
import { SimpleSelect } from "@/ui/SimpleSelect";

import "./VendorSelect.scss";

@inject("languageShell", "tcgCommon", "gameCenter", "common")
@observer
class VendorSelect extends React.Component {
  get newVendors() {
    return [{ displayName: "all_providers", vassalage: "" }, ...this.props.vendors];
  }

  get allGameCount() {
    return this.props.tcgCommon.gameVendor.mapping[this.props.gameType]?.gameCount;
  }
  get activeName() {
    const game = this.props.vendors.find((item) => item.vassalage === this.props.vassalage);
    return game?.displayName;
  }
  get activeVendorIcon() {
    return `${this.props.common.vendorIconSmall}${this.props.vassalage}-DEFAULT.png`;
  }
  state = { gameName: "" };
  onChange = (value) => {
    this.props.handleSelect(value);
  };
  handleClean = () => {
    this.props.handleSelect("");
  };
  selected = () => {
    if (this.props.vassalage) {
      return (
        <div className="game-selected">
          <div className="selected-name">
            {/* <img className="selected-vendor" src={this.activeVendorIcon} alt="" /> */}
            <span>{this.activeName}</span>
          </div>
          {/* <Icon
            className="close-select"
            type={require("!svg-sprite-loader!@/assets/images/common/icon-close.svg")}
            onClick={this.handleClean}
          /> */}
        </div>
      );
    }
    return (
      <div className="game-selected">
        <div className="selected-name">
          <span>{this.props.languageShell.t("all_providers")}</span>
        </div>
      </div>
    );
  };
  setGameName = (e) => {
    const name = e.target.value;
    this.setState({ gameName: name });
  };
  render() {
    return (
      <div className={cz("game-select-wrap game-vendor-select", { "value-selected": this.props.vassalage })}>
        <SimpleSelect
          value={this.props.vassalage}
          selected={this.selected()}
          onChange={() => {}}
          setRef={(c) => (this.gameSelect = c)}
          hasArrow
        >
          {/* <div className="game-search-box">
            <Icon
              className="search-icon"
              type={require("!svg-sprite-loader!@/assets/images/common/icon-search.svg")}
            />
            <input
              className="search-input"
              type="text"
              autoComplete="off"
              placeholder={this.props.languageShell.t("providers")}
              value={this.state.gameName}
              onInput={this.setGameName}
            />
          </div> */}
          {this.newVendors.map((item, index) => {
            return (
              <div
                className="provider-item"
                onClick={() => this.onChange(item.vassalage)}
                key={`${item.gameType}-${item.vassalage}-${index}`}
                value={item.vassalage}
              >
                {item.vassalage ? (
                  <img
                    className="provider-icon"
                    src={`${this.props.common.vendorIconCDN}/${coverVassalage(item.vassalage)}-COLOR2.png`}
                    alt=""
                  />
                ) : (
                  <span>{this.props.languageShell.t(item.displayName)}</span>
                )}
                {/* <span className="game-count">{item.gameCount}</span> */}
              </div>
            );
          })}
        </SimpleSelect>
      </div>
    );
  }
}

VendorSelect.propTypes = {
  vendors: PropTypes.array.isRequired,
  vassalage: PropTypes.string.isRequired,
  handleSelect: PropTypes.func.isRequired,
};

VendorSelect.defaultProps = {
  vendors: [],
};

export default VendorSelect;
