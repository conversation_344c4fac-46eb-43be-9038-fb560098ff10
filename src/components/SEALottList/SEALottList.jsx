import React from "react";
import { withRouter } from "react-router";
import dayjs from "dayjs";
import { isEmpty } from "lodash";
import { inject, observer } from "mobx-react";
import { withGame } from "tcg-mobile-common";

import VNLotteryItem from "@/components/Lottery/VNLottItem";
import { get } from "@/utils/storage";

import "./SEALottList.scss";

@inject("common", "languageShell", "gameCenter")
@withGame
@withRouter
@observer
class SEALottList extends React.Component {
  state = {
    hotLott: [],
    lottList: [],
  };
  componentDidMount() {
    this.getLottMenus();
  }
  componentWillUnmount() {
    window.clearTimeout(this.timer);
    this.timer = null;
  }
  launchSEA = () => {
    if (!get("MC_SESSION_INFO")) {
      return this.props.push("/m/login");
    }
    this.props.launchLott({
      vendor: "TCG_SEA",
      gameCode: "lobby",
      prizeMode: "SEA",
    });
  };
  launchGame = (code) => {
    if (!get("MC_SESSION_INFO")) {
      return this.props.history.push("/m/login");
    }
    this.props.launchLott({
      vendor: "TCG_SEA",
      gameCode: code,
      prizeMode: "SEA",
    });
  };
  getCountTime = (item) => {
    return Math.max(item.remainTime - item.lockTime * 1000, 0);
  };
  getLottMenus = () => {
    const query = {
      prizeMode: "SEA",
    };
    const firstTime = new Date().getTime();
    this.props.gameCenter.getGameMenus(query).then((res) => {
      if (res.length) {
        let hotGame = [];
        // const allGames = res.reduce((prev, next) => {
        //   hotGame = hotGame.concat(next.gameMenus.filter((item) => item.tag.includes("HOT")));
        //   return [...prev, ...next.gameMenus];
        // }, []);
        const allGames = res[0]?.gameMenus || [];
        const gameList = allGames
          .filter((item) => this.getCountTime(item) > 1)
          .sort((prev, next) => {
            return this.getCountTime(prev) - this.getCountTime(next);
          });
        this.setState({
          hotLott: hotGame,
          lottList: allGames,
        });
        if (allGames.length) {
          this.handleLottGameReturn(firstTime, allGames);
        }
      }
    });
  };
  handleLottGameReturn = (firstTime, res) => {
    const data = res || [];
    let lastTime = new Date().getTime();
    let counts = { firstTime: firstTime, lastTime: lastTime };
    this.setState({
      lotteryTime: Object.values(counts),
    });
    let minRemain;
    if (!isEmpty(data)) {
      const dataList = data.filter((game) => !isEmpty(game.numero));
      const validGamesRemainTime = dataList.map(({ remainTime }) => Math.max(remainTime, 0));
      minRemain = Math.min.apply("Math", validGamesRemainTime);
      if (minRemain <= 0) {
        minRemain = 7000;
      } else {
        minRemain = Math.min(30000, minRemain);
      }
    }
    window.clearTimeout(this.timer);
    this.timer = setTimeout(this.getLottMenus, minRemain);
  };
  renderNumber = (num) => {
    if (!num) {
      return null;
    }
    let arr = [];
    if (num.indexOf(",") > 0 && num.indexOf("~") > 0) {
      arr = num.split("~")[0].split("");
    } else if (num.indexOf(",") > 0 && num.split(",")[0].split("").length > 3) {
      arr = num.split(",")[0].split("");
    } else {
      arr = num.indexOf(",") > 0 ? num.split(",") : num.split("");
    }
    return (
      <div className="num-list">
        {arr.map((item, index) => {
          return (
            <div className="num-item" key={`num_${index}`}>
              {item}
            </div>
          );
        })}
      </div>
    );
  };
  formatWinningTime = (time) => {
    return time ? dayjs(time).format("YYYY.MM.DD") : "";
  };

  render() {
    return (
      <div className="lottery-list-wrap hide-scrollbar">
        <div className="lottery-list">
          {this.state.lottList.map((item) => {
            return (
              <VNLotteryItem
                lotteryTime={this.state.lotteryTime}
                key={`${item.remark}-${item.gameId}`}
                lott={item}
                hasFav={false}
                gameClick={this.launchGame}
              />
            );
          })}
        </div>
      </div>
    );
  }
}

export default SEALottList;
