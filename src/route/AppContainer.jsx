import React from "react";
import { withRouter } from "react-router-dom";
import { CSSTransition, TransitionGroup } from "react-transition-group";

import history from "../history";

import AppRoute from "./AppRoute";
import Root from "./Root";

const pageTransition = {
  startPosition: 0,
  distance: false,
  bindEvents() {
    const body = document.body;
    body.addEventListener("touchstart", function (e) {
      pageTransition.startPosition = e.targetTouches[0].clientX;
      pageTransition.distance = false;
    });
    body.addEventListener("touchmove", function (e) {
      pageTransition.distance = e.touches[0].clientX < 0 || e.touches[0].clientX - pageTransition.startPosition > 100;
    });
    body.addEventListener("touchend", function (e) {
      if (pageTransition.distance) {
        setTimeout(() => {
          pageTransition.distance = false;
        }, 500);
      }
    });
  },
  childFactory(child) {
    const action = history.action === "POP";
    return React.cloneElement(child, {
      key: child.key,
      classNames: action && pageTransition.distance ? "none" : "fade",
    });
  },
};
pageTransition.bindEvents();

const AppContainer = ({ location, filterRouter }) => {
  return (
    <TransitionGroup style={{ display: "contents" }} childFactory={pageTransition.childFactory}>
      <CSSTransition timeout={300} key={location.key}>
        <AppRoute
          render={() => {
            return <Root>{filterRouter(location, history)}</Root>;
          }}
        />
      </CSSTransition>
    </TransitionGroup>
  );
};

export default withRouter(AppContainer);
