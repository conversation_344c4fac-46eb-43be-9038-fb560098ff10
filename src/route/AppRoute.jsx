import React from "react";
import { Route } from "react-router-dom";
import { inject, observer } from "mobx-react";
import { ErrorIP, ErrorService } from "tcg-mobile-common";

const AppRoute = ({ common, render }) => {
  if (common.systemStatus === 0) {
    return render();
  }
  if (common.systemStatus === 1) {
    return <Route component={ErrorService} />;
  }
  if (common.systemStatus === 2) {
    return <Route component={ErrorIP} />;
  }
  return null;
};

export default inject("common")(observer(AppRoute));
