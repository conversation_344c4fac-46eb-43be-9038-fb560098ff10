import React from "react";
import ReactDOM from "react-dom";
import { Redirect, Route, Router } from "react-router-dom";
import mobx from "mobx";
import { Provider } from "mobx-react";
import {
  AppUpgradeModal,
  appUtil,
  DownloadBar,
  eventTracker,
  PrivateRoute,
  PublicLogic,
  pwaHelper,
  setMerchant,
} from "tcg-mobile-common";
import { Notification } from "tcgmodulemc";

import FormPopupGroup from "@/components/FormPopupGroup/FormPopupGroup";
import SvgGradients from "@/components/SvgGradients/SvgGradients";

import ShellDownloadPopup from "./components/DownloadPopup/ShellDownloadPopup";
import SideMenu from "./components/SideMenu/SideMenu";
import deployConfig from "./config/deploy.config";
import { gamePath } from "./config/game.config";
import { downloadBarPath } from "./config/game.config";
import { routeMap } from "./config/router.config";
import Activity from "./containers/Activity/Activity";
import ApplyReward from "./containers/ApplyReward/ApplyReward";
import ChangePsw from "./containers/ChangePsw/ChangePsw";
import ForgetPsw from "./containers/Forget/Forget";
import GameListCenter from "./containers/GameListCenter/GameListCenter";
import HelpCenter from "./containers/HelpCenter/HelpDetail";
import Login from "./containers/Login/Login";
import Notice from "./containers/Notice/Notice";
import Home from "./containers/OutterApp";
import RecentGame from "./containers/RecentGame/RecentGame";
import Register from "./containers/Register/Register";
import SearchGame from "./containers/SearchGame/SearchGame";
import Service from "./containers/Service";
import AppContainer from "./route/AppContainer";
// Import our Stores Here
import store from "./stores/createAppState";
import { loadDayjsLang } from "./utils/isoLang";
// import { initTheme } from "./utils/theme";
// Import app real entrance component here.
import AsyncComponent from "./AsyncComponent";
import history from "./history";

import "tcg-mobile-common/dist/tcg-mobile-common.css";
// Import theme.
import "./stylesheets/common.scss";
// downloadBar
import "./components/DownloadBar/DownloadBar.scss";

// Enable MobX Strict Functionality
mobx.useStrict(true);

// theme
// initTheme();

// dayjs
loadDayjsLang();

const $root = document.querySelector("#root");

// 过滤不需要验证的路由.
const FilterRouter = (locations, history) => {
  let component = {};
  switch (locations.pathname) {
    case "/m/login":
      component = router(Login, locations, history);
      break;
    case "/m/register":
    case "/m/register.html":
      component = router(Register, locations, history);
      break;
    case "/m/changePsw":
      component = router(ChangePsw, locations, history);
      break;
    case "/m/forget":
      component = router(ForgetPsw, locations, history);
      break;
    case "/":
    case "/m":
    case "/m/":
    case "/m/index":
    case "/m/index.html":
    case "/m/home":
      component = router(Home, locations, history);
      break;
    case "/m/help":
    case "/m/helpDetail":
      component = router(HelpCenter, locations, history);
      break;
    case "/m/agent":
      component = router(AsyncComponent("Agent/Agent"), locations, history);
      break;
    case "/m/agent/home":
      component = router(AsyncComponent("Agent/AgentHome"), locations, history);
      break;
    case "/m/download":
      component = router(AsyncComponent("Download/Download"), locations, history);
      break;
    case "/m/set":
      component = router(AsyncComponent("Setting/Setting"), locations, history);
      break;
    case "/m/notice":
    case "/m/noticeDetail":
      component = router(Notice, locations, history);
      break;
    case "/m/activity":
    case "/m/activityDetail":
      component = router(Activity, locations, history);
      break;
    case "/m/applyReward":
      component = router(ApplyReward, locations, history);
      break;
    case "/m/service":
      component = router(Service, locations, history);
      break;
    case gamePath.RECENT:
      component = router(RecentGame, locations, history);
      break;
    case gamePath.SEARCH:
      component = router(SearchGame, locations, history);
      break;
    case gamePath.PVP:
    case gamePath.RNG:
    case gamePath.FISH:
    case gamePath.LIVE:
    case gamePath.JL:
    case gamePath.RTG:
    case gamePath.BINGO:
    case gamePath.JACKPOT:
    case gamePath.HOT:
    case gamePath.NEW:
    case gamePath.FAV:
    case gamePath.SPORTS:
    case gamePath.ESPORTS:
    case gamePath.COCKFIGHT:
    case gamePath.LOTT:
    case gamePath.ELOTT:
      component = router(GameListCenter, locations, history);
      break;

    default:
      let findRoute = routeMap[locations.pathname];
      if (findRoute) {
        component = (
          <PrivateRoute
            exact
            path={locations.pathname}
            component={findRoute.component}
            location={locations}
            key={locations.key}
            {...history}
          />
        );
      } else {
        component = (
          <Route path="*">
            <Redirect to="/m/home" />
          </Route>
        );
      }
  }
  function router(routeComponent, locationObj, props) {
    const wrapper = (Component, props) => {
      return () => (
        <div className="_container_box">
          {downloadBarPath.includes(locationObj.pathname) && (
            <DownloadBar {...props} onClickDownload={() => history.push("/m/download")} />
          )}
          <Component {...history} />
          <SideMenu />
          <ShellDownloadPopup />
          <FormPopupGroup {...props} />
        </div>
      );
    };
    return (
      <Route
        exact
        path={locationObj.pathname}
        component={wrapper(routeComponent, props)}
        location={locationObj}
        key={locationObj.key}
        {...props}
      />
    );
  }
  return component;
};

async function main() {
  setMerchant(deployConfig.merchant);
  if (/App\S*?Ver/.test(window.navigator.userAgent)) {
    $root.classList.add("app");
  }

  await store.languageShell.changeLanguageTo();
  const res = await store.common.getStatus();

  AppUpgradeModal.setup({ store });

  const router = (
    <Provider {...store}>
      <Router history={history}>
        <div style={{ display: "contents" }}>
          <SvgGradients />
          <AppContainer filterRouter={FilterRouter} />
          <Notification />
          <PublicLogic />
        </div>
      </Router>
    </Provider>
  );

  ReactDOM.render(router, $root, appUtil.setSplashOff);
}

pwaHelper.load(history, store)(main);
eventTracker.init();

if (module.hot) {
  module.hot.accept();
}
