import address from "../config/address.config";
import deploy from "../config/deploy.config";

import apiUtils from "./apiUtils";
const gameCenter = {
  getVassGameType: () =>
    apiUtils.get(address.game.vassgameType, {
      query: {
        typeId: "1",
        merchant_code: deploy.merchant,
      },
      moduleId: "COMM3",
    }),
  getGameList: (formData) =>
    apiUtils.get(address.game.gamelist, {
      query: {
        merchant: deploy.merchant,
        clientType: 2,
        ...formData,
        imgSize: deploy.gameImgSize,
      },
      merchant: deploy.merchant,
      moduleId: "GAMELO3",
    }),
  addFavGame: (formData) =>
    apiUtils.post(address.game.addFavGame, {
      data: formData,
      moduleId: "GAMELOFAV3",
    }),
  removeFavGame: (formData) =>
    apiUtils.del(address.game.removeFavGame, {
      data: formData,
      moduleId: "GAMELOFAV3",
    }),
  getFavGames: (formData) =>
    apiUtils.get(address.game.getFavGames, {
      query: {
        ...formData,
      },
      moduleId: "GAMELOFAV3",
    }),
  getFavGameList: (formData) =>
    apiUtils.get(address.game.getFavGameList, {
      query: {
        ...formData,
        imgSize: deploy.gameImgSize,
      },
      moduleId: "GAMELOFAV3",
    }),
  addRecentGame: (formData) =>
    apiUtils.get(address.game.addRecentGame, {
      query: {
        ...formData,
      },
      moduleId: "GAMELOREC3",
    }),
  getRecentGameList: (formData) =>
    apiUtils.get(address.game.getRecentGameList, {
      query: {
        ...formData,
        imgSize: deploy.gameImgSize,
      },
      moduleId: "GAMELOREC3",
    }),
  getNumero: (formData) =>
    apiUtils.get(address.game.numero, {
      query: {
        ...formData,
      },
      moduleId: "COMMON",
    }),
  getLottList: () => apiUtils.get(address.game.getLottList, {}),
  getGameHot: (formData) =>
    apiUtils.get(address.game.gameHot, {
      query: {
        merchantCode: deploy.merchant,
        clientType: 2,
        ...formData,
        imgSize: deploy.gameImgSize,
      },
      cache: true,
      moduleId: "GAMELOHOT3",
    }),
  getLottMenu: (query) => apiUtils.get(address.game.lottMenu, { query }),
  getRecentVNLott: (query) => apiUtils.get(address.game.recentVNLott, { query }),
  getVnNumero: (query) => apiUtils.get(address.game.vnNumero, { query }),
  launchGame: (query) => apiUtils.get(address.game.launchGame, { query, moduleId: "GAMELO3" }),
  getWinnerBoard: (query) =>
    apiUtils.get(address.game.winnerBoard, {
      query: {
        imgSize: deploy.gameImgSize,
        ...query,
      },
    }),
  getGameMenus: (query) => {
    return apiUtils.get(address.game.gameMenus, { query });
  },
};
export default gameCenter;
