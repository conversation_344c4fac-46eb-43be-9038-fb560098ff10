import { throttle } from "lodash";

import address from "../config/address.config";
import deploy from "../config/deploy.config";

import apiUtils from "./apiUtils";

const account = {
  login: (payload) => {
    let Device = undefined;
    if ("standalone" in navigator && navigator.standalone) {
      Device = "OTHERS";
    }
    return apiUtils.post(address.account.login, {
      data: payload.DES,
      options: {
        headers: {
          Encryption: payload.RSA,
          "Content-Type": "application/json",
          Device,
        },
      },
    });
  },
  testPlay: () => {
    return apiUtils.post(address.account.testPlay, {
      moduleId: deploy.ModuleId.FREEPLAY3,
    });
  },
  register: (payload, regist_method) => {
    // console.log(deploy.REGISTER_REE3);
    return apiUtils.put(regist_method === "username" ? address.account.register : address.account.registerMobile, {
      data: payload.DES,
      moduleId: regist_method === "username" ? deploy.ModuleId.REGISTER_REE3 : "MOBILENUMREG3",
      options: {
        headers: {
          Encryption: payload.RSA,
          // Merchant: deploy.merchant,
          "Content-Type": "application/json",
        },
      },
    });
  },
  verification: (query) => {
    return apiUtils.get(address.account.verification, {
      query,
    });
  },
  regValidatas: () => {
    return apiUtils.get(address.account.regValidata, {});
  },
  changePassword: (pwdFormData) => {
    apiUtils.post(address.account.password, pwdFormData);
  },
  getSetPasswordPhone: (payload) => {
    return apiUtils.post(address.account.setPasswordPhone, {
      data: payload,
      options: {
        headers: {
          "Content-Type": "application/json",
        },
      },
      encrypt: true,
    });
  },
  annount: (query, flush = false) => {
    return apiUtils.get(address.announcements.bannerNew, {
      query,
      alert: false,
      cache: true,
      flush,
    });
  },
  annountMarkAsRead: throttle((id) => {
    return apiUtils.post(address.announcements.markAsRead, {
      data: {
        announcementId: id,
        read: true,
      },
      alert: false,
    });
  }, 150),
  mnotice: (query) => {
    return apiUtils.get(address.announcements.noticeNew, {
      query,
    });
  },
  sysStatus: (query) => {
    return apiUtils.get(address.whitelabel.systemStatus, { query });
  },
  captchas: () => {
    return apiUtils.get(address.account.captcha, {
      moduleId: deploy.ModuleId.VALID_CAPTCHA3,
    });
  },
  populars: (query) => {
    return apiUtils.get(address.announcements.populor, { query });
  },
  getLott: (query) => {
    return apiUtils.get(address.game.getLottList, { query });
  },
  email: (emailUser) => {
    return apiUtils.post(address.account.foxmail, {
      data: emailUser,
      options: {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      },
    });
  },
  vlideUsername: (query, moduleId) => {
    return apiUtils.get(address.account.username, { query, moduleId });
  },
  affiliate: (query, moduleId = "AFFMAN3") => {
    return apiUtils.get(address.account.affiliate, { query, moduleId });
  },
  createTrailAccount: () => {
    return apiUtils.get(address.account.trial);
  },
  sysConsolidated: (query) => {
    return apiUtils.get(address.account.setTemplate, { query });
  },
  setHeadIcon: (data) => {
    return apiUtils.post(address.account.headIcon, {
      data: data,
      options: {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        },
      },
    });
  },
  getSetRegister: () => {
    return apiUtils.get(address.account.setRegister);
  },
  getHeadIcon: () => {
    return apiUtils.get(address.account.headIcon, {
      moduleId: "PROFPIC3",
    });
  },
  getVassGame: () => {
    return apiUtils.get(address.game.vassgameType, {
      query: {
        typeId: "1",
        merchant_code: deploy.merchant,
      },
      moduleId: "COMM3",
    });
  },
  oService: () => {
    return apiUtils.get(address.getService.service);
  },
  logout: (auths) => {
    return apiUtils.del(address.account.logout, {
      data: "",
    });
  },
  getContracDailySalReq: () =>
    apiUtils.get(address.agentSalary.getContracDailySalReq, {
      moduleId: "COMM3",
    }),
  getContractDividendRequest: () =>
    apiUtils.get(address.agentDividend.getContractDividendRequest, {
      moduleId: "COMM3",
    }),
  sendVerificationCode: (data) => {
    return apiUtils.post(address.account.sendVerificationCode, {
      data,
      moduleId: "REGMOBVERF3",
      options: {
        headers: {
          "Content-Type": "application/json",
        },
      },
    });
  },
  getSystemCountry: () => {
    return apiUtils.get(address.account.systemCountry);
  },
  getInboxUnreadCount: throttle(() => {
    return apiUtils.get(address.account.inboxUnreadCount, {
      moduleId: "INBOX3",
      alert: false,
    });
  }, 250),
  getTeamKPIC: (query) => {
    return apiUtils.get(address.agent.teamKPIC, {
      query,
      moduleId: "AGEINFO3",
    });
  },
  helpCenter: (query) => {
    return apiUtils.get(address.account.helpCenter, {
      moduleId: "COMM3",
      query: {
        device: "MOBILE",
        ...query,
      },
    });
  },
  feedback: (formData) => {
    return apiUtils.put(address.account.feedback, {
      data: {
        ...formData,
      },
      moduleId: "FEEDBACK3",
    });
  },
  getPlayerRankProgress: (formData) => {
    return apiUtils.get(address.account.getPlayerRankProgress, {
      data: {
        ...formData,
      },
    });
  },
  signUpPromotionJoin: (data) => {
    return apiUtils.post(address.announcements.signUpPromotionJoin, {
      data,
      moduleId: "REWCEN3",
    });
  },
  signUpRequestReward: (data) => {
    return apiUtils.post(address.announcements.signUpRequestReward, {
      data,
      moduleId: "REWCEN3",
    });
  },
  getDomainRoute: () => {
    return apiUtils.get(address.account.getDomainRoute, {
      query: {
        device: 0,
      },
    });
  },
};
export default account;
