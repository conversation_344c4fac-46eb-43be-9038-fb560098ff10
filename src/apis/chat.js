import { throttle } from "lodash";

import address from "../config/address.config";

import apiUtils from "./apiUtils";

export function launchChat(query) {
  let language = (window.localStorage.getItem("language") || "CN").toUpperCase();
  language = language.includes("CN") ? "CN" : language;
  return apiUtils.get(address.chat.launchChat, {
    query: { b2c: 1, language, backUrl: location.href, headerColor: "#fff", title: document.title, ...query },
    moduleId: "CBS3",
  });
}

export const chatUnreadCount = throttle(
  () =>
    apiUtils.get(address.chat.unreadCount, {
      moduleId: "CBS3",
      alert: false,
    }),
  250
);

export const getModelList = (query) => apiUtils.get(address.live.modelList, { moduleId: "CBS3", alert: false, query });
