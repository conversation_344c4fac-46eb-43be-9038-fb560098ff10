import address from "../config/address.config";

import apiUtils from "./apiUtils";

const live = {
  getAchorList: (query) => apiUtils.get(address.chat.anchorList, { query, moduleId: "CBS3", cache: false }),
  getFocusList: () => apiUtils.get(address.chat.focusList),
  getUnReadCount: () => apiUtils.get(address.chat.unReadCount, { moduleId: "CBS3" }),
  getLaunchChat: (query = {}) => {
    const backUrl = `${window.location.origin + window.location.pathname}?splash=0`;
    return apiUtils.get(address.chat.launchChat, {
      query: {
        ...query,
        b2c: 1,
        backUrl,
        headerColor: "#fff",
        title: document.title,
        template: "v2",
      },
      moduleId: "CBS3",
    });
  },
};
export default live;
