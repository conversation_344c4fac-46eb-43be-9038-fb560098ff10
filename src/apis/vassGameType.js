import address from "../config/address.config";
import deploy from "../config/deploy.config";

import apiUtils from "./apiUtils";

const vassGameType = {
  getVassGameType: (query) =>
    apiUtils.get(address.whitelabel.vassgameType, {
      query: {
        typeId: "1",
        merchant_code: deploy.merchant,
      },
      moduleId: deploy.ModuleId.GAME_BASIC,
    }),
};

export default vassGameType;
