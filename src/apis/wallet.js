import address from "../config/address.config";
import deploy from "../config/deploy.config";

import apiUtils from "./apiUtils";

const wallet = {
  getBanlanceInfo: () => apiUtils.get(address.wallet.balance),
  getTransferDetails: (query) =>
    apiUtils.get(address.wallet.transferDetails, {
      query,
      moduleId: deploy.ModuleId.Member_Transaction_Report,
    }),
  getTxCodes: () => apiUtils.get(address.wallet.txCode, { query: { txType: "ALL" } }),
  transfer: async (payload) => {
    try {
      const res = await apiUtils.post(address.wallet.transfer, {
        data: payload,
        moduleId: deploy.ModuleId.FUNDTRANSFER,
      });
      if (res.data.success) {
        return true;
      }
    } catch (err) {
      return false;
    }
  },
};

export default wallet;
