import address from "../config/address.config";
import deploy from "../config/deploy.config";

import apiUtils from "./apiUtils";

const security = {
  getSecurityConfig: (modId) => apiUtils.get(address.securityList.securityConfig, modId),
  changePassword: (formData, modId) =>
    apiUtils.post(address.securityList.passWord, {
      data: formData.data,
      moduleId: modId,
      options: { headers: { Encryption: formData.Encryption } },
    }),
  _hasPaymentPassword: (modId) => apiUtils.get(address.securityList.hasPassword, modId),
  setPaymentPassword: (formData, modId) =>
    apiUtils.put(address.securityList.fundsPassword, {
      data: formData.data,
      moduleId: modId,
      options: { headers: { Encryption: formData.Encryption } },
    }),
  postPaymentPassword: (formData, modId) =>
    apiUtils.post(address.securityList.fundsPassword1, {
      data: formData.data,
      moduleId: modId,
      options: { headers: { Encryption: formData.Encryption } },
    }),
  bindBankCard: (formData, modId) =>
    apiUtils.put(address.securityList.bindCardBase, {
      data: formData.data,
      moduleId: modId,
      options: { headers: { Encryption: formData.Encryption } },
    }),
  getCard: (token, modId) => apiUtils.get(address.securityList.getCardList, token, modId),
  _getCardList: () =>
    apiUtils.get(address.securityList.bankCardList, {
      query: { merchantCode: deploy.merchant },
      moduleId: "BINDCARD3",
    }),
  _getSecQuestions: (modId) => apiUtils.get(address.securityList.getSecurityQuestions, modId),
  _firstSecQuestionSet: (formData, modId) =>
    apiUtils.put(address.securityList.firstPasswordSet, {
      data: formData.data,
      moduleId: modId,
      options: { headers: { Encryption: formData.Encryption } },
    }),
  _getPersonalSecQuetion: (modId) => apiUtils.get(address.securityList.customerSecQuestions, modId),
  _changeSecQuestionSet: (formData, modId) =>
    apiUtils.post(address.securityList.resetfundsPassword, {
      data: formData.data,
      moduleId: modId,
      options: { headers: { Encryption: formData.Encryption } },
    }),
  _bindEmail: (formData, modId) =>
    apiUtils.post(address.securityList.bindEmails, {
      data: formData.data,
      moduleId: modId,
      options: { headers: { Encryption: formData.Encryption } },
    }),
  _bindPayee: (formData, modId) =>
    apiUtils.post(address.securityList.bindPayee, {
      data: formData.data,
      moduleId: modId,
      options: { headers: { Encryption: formData.Encryption } },
    }),
};

export default security;
