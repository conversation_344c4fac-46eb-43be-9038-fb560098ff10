import { types } from "mobx-state-tree";

import vassGameType from "../apis/vassGameType";
import whitelabel from "../apis/whitelabel";

const getClassMenu = (classMenu) => {
  let menuList = {};
  for (let i = 0; i < classMenu.length; i++) {
    menuList[classMenu[i].modId] = classMenu[i];
  }
  return menuList;
};
const getMainMenu = (res) => {
  let menu = {
      SECPRIV: {
        name: "安全中心",
        link: "/securityCenter",
      },
      MYACC: {
        name: "我的账户",
        link: "/myAccount/index",
      },
      PERSREP: {
        name: "个人报表",
        link: "/profitandloss",
      },
      DEPOSITBEHALF: {
        name: "代充",
        link: "/agencyPay",
      },
      MAILCEN: {
        name: "站内信",
        classname: "",
        link: "",
      },
      TRANSBETREPMEM3: {
        name: "游戏记录",
        classname: "",
        link: "/gameRecord",
      },
      AGEPAG: {
        name: "团队总览",
        classname: "",
        link: "/agent/team/overview",
      },
      REGCEN: {
        name: "开户中心",
        classname: "",
        link: "/agentRegister",
      },
      AGEREP: {
        name: "团队管理",
        classname: "",
        link: "/mc/team",
      },
      PROFITSHARE: {
        name: "代理佣金",
        classname: "",
        link: "",
      },
      DIVIDEND: {
        name: "代理分红",
        classname: "",
        link: "/mc/agent/agentDivident",
      },
      GAMSER: {
        name: "代理返点",
        classname: "",
        link: "/mc/agent/agentRebeat",
      },
      DAYSAL: {
        name: "代理日工资",
        classname: "",
        link: "/mc/agent/dailySalary",
      },
    },
    mainMenu = [],
    key = 0,
    surplus = 0;
  for (let i = 0; i < res.length; i++) {
    if (menu[res[i].modId]) {
      mainMenu[key] = {};
      mainMenu[key] = menu[res[i].modId];
      mainMenu[key].modId = res[i].modId;
      mainMenu[key].enabled = res[i].enabled;
      mainMenu[key].className = key % 4 === 3 ? "mc-main-bottom" : "mc-main-bottom-right";
      key++;
    }
  }
  surplus = key % 4 === 0 ? 4 : key % 4;
  for (let j = surplus; j > 0; j--) {
    mainMenu[key - j].className = surplus === 4 && j === 1 ? "" : "mc-main-right";
  }
  return mainMenu;
};
const classifyMenu = (menu, calss, type) => {
  let memberCenter = {},
    agentCenter = {},
    main = {},
    menuList = {};
  for (let i = 0; i < menu.length; i++) {
    if (menu[i].modId === "MEMCEN") {
      main.mc = true;
      memberCenter = menu[i].childrenNodes;
    } else if (menu[i].modId === "AGECEN") {
      main.ac = true;
      agentCenter = menu[i].childrenNodes;
    }
  }
  if (calss === "mc" && memberCenter) {
    let mcMenuList = getClassMenu(memberCenter);
    menuList["list"] = mcMenuList[type].childrenNodes;
  } else if (calss === "ac" && agentCenter) {
    let acMenuList = getClassMenu(agentCenter);
    menuList["list"] = acMenuList[type].childrenNodes;
  } else if (calss === "main") {
    main.mcMenuList = getMainMenu(memberCenter);
    main.acMenuList = getMainMenu(agentCenter);
    menuList["list"] = main;
    menuList["modID"] = type;
  }
  menuList["modID"] = type;
  return menuList;
};

const MenuStore = types.model(
  "MenuStore",
  {
    modelmenu: types.frozen,
    depositmenu: types.frozen,
    withdrawmenu: types.frozen,
    securitymenu: types.frozen,
    accountmenu: types.frozen,
    persrepmenu: types.frozen,
    depositbehalemenu: types.frozen,
    messagemenu: types.frozen,
    gametypemenu: types.frozen,
    agentpagemenu: types.frozen,
    agregistermenu: types.frozen,
    get totalSafeMenu() {
      let total = 0;
      if (this.securitymenu && this.securitymenu.list && this.securitymenu.list.length > 0) {
        this.securitymenu.list.forEach((list) => {
          total += parseInt(list.enabled, 10);
        });
        return total;
      }
    },
  },
  {
    getVassGameType(query) {
      let gameType = JSON.parse(window.sessionStorage.getItem("getVassGameType"));
      if (!gameType) {
        return vassGameType.getVassGameType(query).then((ret) => {
          window.sessionStorage.setItem("getVassGameType", JSON.stringify(ret.data.value));
          this.setVassGameType(ret.data.value.content ? ret.data.value.content : {});
        });
      } else {
        return new Promise((res, rej) => {
          this.setVassGameType(gameType ? gameType : {});
          res(gameType.content);
        });
      }
    },
    setData(menu, type) {
      this[type] = menu;
    },
    setVassGameType(menu) {
      this.gametypemenu = menu;
    },
    getData(type, key, vl) {
      let menuList = JSON.parse(window.sessionStorage.getItem("menu"));
      let menu;
      if (!menuList) {
        whitelabel.getMenu().then((ret) => {
          window.sessionStorage.setItem("menu", JSON.stringify(ret.data.value));
          menu = ret.data.value;
          this.setData(classifyMenu(menu.childrenNodes, key, vl), type);
        });
      } else {
        this.setData(classifyMenu(menuList.childrenNodes, key, vl), type);
      }
    },
    getMain() {
      //首页
      this.getData("modelmenu", "main", "");
    },
    getDeposit() {
      //充值中心
      this.getData("depositmenu", "mc", "DEPOSIT");
    },
    getWithdraw() {
      //充值中心
      this.getData("withdrawmenu", "mc", "WITHDRAW");
    },
    getSecurity() {
      //安全中心
      this.getData("securitymenu", "mc", "SECPRIV");
    },
    getMyAccount() {
      // 我的账户
      this.getData("accountmenu", "mc", "MYACC");
    },
    getPersrep() {
      // 盈亏报表
      this.getData("persrepmenu", "mc", "PERSREP");
    },
    getDepositBehale() {
      // 代充
      this.getData("depositbehalemenu", "mc", "DEPOSITBEHALF");
    },
    getMessage() {
      // 站内信
      this.getData("messagemenu", "mc", "MAILCEN");
    },
    getGameHistory() {
      // 游戏记录
      // this.getData("gametypemenu", "mc", "TRANSBETREPMEM3");
      this.setVassGameType();
    },
    getAgentPage() {
      // 团队总览
      this.getData("agentpagemenu", "ac", "AGEPAG");
    },
    getAgentRegister() {
      // 开户中心
      this.getData("agregistermenu", "ac", "REGCEN");
    },
  }
);

export default MenuStore;
