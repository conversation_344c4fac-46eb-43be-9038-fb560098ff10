import { types } from "mobx-state-tree";
import { getSystemStatus } from "tcg-mobile-common";

import accountAgent from "../apis/account";
import { chatUnreadCount, getModelList } from "../apis/chat";
import gameCenter from "../apis/gameCenter";
import memberAgent from "../apis/member";
import deploy from "../config/deploy.config";
import { get, set } from "../utils/storage";

import root from "./createAppState";

const defaultImageCDNs = ["https://images.b298100.com:42666"];

const CommonStore = types.model(
  "CommonStore",
  {
    memberInfo: types.frozen,
    ant: types.frozen,
    act: types.frozen,
    cat: types.frozen,
    banners: types.optional(types.frozen, []),
    notice: types.frozen,
    icons: types.frozen,
    loginPus: types.frozen, //登录后弹窗信息
    aff: types.frozen,
    vassGame: types.frozen,
    link: types.frozen,
    lotts: types.frozen,
    cataList: types.frozen,
    rValidata: types.frozen,
    registerCount: types.frozen,
    downLink: types.frozen,
    downLinkObj: types.optional(types.frozen, {}),
    customerService: types.optional(types.frozen, []),
    domainRoute: types.optional(types.frozen, []),
    numero: types.frozen,
    timeCount: types.frozen,
    // count: types.frozen,
    video: types.optional(types.frozen, {}),
    hideBalance: types.optional(types.frozen, false),
    popup: types.frozen,
    chatUrl: types.frozen,
    hotMatch: types.optional(types.frozen, []),
    helpList: types.optional(types.frozen, []),
    template: types.frozen,
    userPageOpen: types.optional(types.frozen, false),
    systemStatus: types.optional(types.frozen, 0), // 0 正常, 1 維護, 2 IP
    ip: types.optional(types.frozen, ""),
    imageCDNs: types.optional(types.frozen),
    showDownPopup: types.optional(types.frozen, false),
    chatUnreadCount: types.optional(types.frozen, 0),
    inboxUnreadCount: types.optional(types.frozen, 0),
    hasModel: types.optional(types.frozen, false),
    sideMenuOpen: types.optional(types.frozen, false),
    rightMenuOpen: types.optional(types.frozen, false),
    hasModal: types.optional(types.frozen, false),
    hotMatchStatus: types.optional(types.frozen, false),
    downloadBarStatus: types.optional(types.frozen, true),
    agentModalStatus: types.optional(types.frozen, false), // 代理中心弹窗
    gameFilterStatus: types.optional(types.frozen, false), // 游戏筛选弹窗
    providerStatus: types.optional(types.frozen, false), // 厂商筛选弹窗
    helpPopupStatus: types.optional(types.frozen, false), // 帮助中心弹窗
    languageModalStatus: types.optional(types.frozen, false), // 语言弹窗
    iosTutorialStatus: types.optional(types.frozen, false), // ios下载教程弹窗
    loginModalStatus: types.optional(types.frozen, false), // 登录弹窗
    registerModalStatus: types.optional(types.frozen, false), // 注册弹窗
    forgetModalStatus: types.optional(types.frozen, false), // 忘记密码弹窗
    changePswModalStatus: types.optional(types.frozen, false), // 修改密码弹窗
    searchPopStatus: types.optional(types.frozen, false),
    formPopupStatus: types.optional(types.frozen, false),
    footerMenuStatus: types.optional(types.frozen, false),
    categoryPopupStatus: types.optional(types.frozen, false),
    policyPopupStatus: types.optional(types.frozen, false),
    symbolLangOpen: types.optional(types.frozen, false),
    channelPopupStatus: types.optional(types.frozen, false),
    get customerId() {
      return this.memberInfo ? this.memberInfo.id : "";
    },
    get banner() {
      let ant = this.ant;
      return ant ? ant : "";
    },
    get numeros() {
      return this.numero ? this.numero : [];
    },
    get count() {
      let registerCount = this.registerCount;
      return registerCount ? registerCount : "";
    },
    get loginPopUpInfo() {
      let loginPu = get("ANNOUNT") && get("ANNOUNT").popup ? get("ANNOUNT").popup : this.loginPus;
      if (loginPu && loginPu.length > 0) {
        loginPu[0].content = loginPu[0].content.replace(/[0-9]*px/g, function (str) {
          var num = str.replace(/[^0-9]/gi, "");
          return num / 100 + "rem";
        });
      }
      return loginPu ? loginPu : [];
    },
    get activity() {
      let act = this.act;
      return act ? act : [];
    },
    get affs() {
      let aff = "";
      if (!this.aff) {
        return aff;
      } else {
        aff = this.aff;
        return get("fflite") ? get("fflite") : aff;
      }
    },
    get catalog() {
      let cat = this.cat;
      return cat ? cat : "";
    },
    get cataLists() {
      let cataList = this.cataList;
      return cataList ? cataList : get("catasList");
    },
    get not() {
      return this.notice ? this.notice : [];
    },
    get icos() {
      let data = this.icons;
      if (data) {
        return Object.keys(this.icons).map((key) => this.icons[key]);
      } else {
        return "";
      }
    },
    get vassStore() {
      let data = this.vassGame;
      return data ? data : get("getVassGameType");
    },
    get lottsCenter() {
      let data = this.lotts;
      if (data) {
        let str = Object.keys(data).map((key) => data[key]);
        return str.map((items, idx) => {
          return items.games;
        });
      } else {
        return "";
      }
    },
    get InputValidata() {
      let data = this.rValidata;
      if (data) {
        return data;
      } else {
        return "";
      }
    },
    get imageCDNOne() {
      if (this.imageCDNs && this.imageCDNs.length > 0) {
        return this.imageCDNs[Math.floor(Math.random() * this.imageCDNs.length)];
      }
      return defaultImageCDNs[Math.floor(Math.random() * defaultImageCDNs.length)];
    },
    get gameIconCDN() {
      return `${this.imageCDNOne}/TCG_PROD_IMAGES/B2C/${deploy.merchant.toUpperCase()}/VENDOR/MOBILE/`;
    },
    get vendorIconCDN() {
      return `${this.imageCDNOne}/TCG_PROD_IMAGES/RNG_LIST_VENDOR/`;
    },
    get vendorIconSmall() {
      return `${this.imageCDNOne}/TCG_PROD_IMAGES/VENDOR_ICON_SMALL/`;
    },
    get countryIconCDN() {
      return `${this.imageCDNOne}/TCG_PROD_IMAGES/COUNTRY_FLAG/CIRCLE/`; //CIRCLE RECT
    },
    get defaultImage() {
      return `${this.imageCDNOne}/TCG_GAME_ICONS/default.png`;
    },
    get mobileprovisionCDN() {
      return `${this.imageCDNOne}/TCG_PROD_IMAGES/B2C/libs/setup.mobileprovision`;
    },
    get announcementUnreadCount() {
      return (this.notice || []).reduce((acc, curr) => {
        return acc + (curr.read ? 0 : 1);
      }, 0);
    },
    get totalUnreadCount() {
      return this.announcementUnreadCount + this.chatUnreadCount + this.inboxUnreadCount;
    },
  },
  {
    anData(ant) {
      this.banners = ant;
    },
    anActivity(act) {
      this.act = act;
    },
    anCatalog(cat) {
      this.cat = cat;
    },
    catasList(cataList) {
      this.cataList = cataList;
    },
    anNotice(notice) {
      this.notice = notice;
    },
    setModelStatus(modelOnline) {
      this.hasModel = modelOnline;
    },
    setPopup(popup) {
      this.popup = popup;
    },
    setHotMatch(match) {
      this.hotMatch = match;
    },
    lottIcon(icons) {
      this.icons = icons;
    },
    setMemberInfo(memberInfo) {
      this.memberInfo = memberInfo;
    },
    setAfflite(aff) {
      this.aff = aff;
    },
    setAffCount(count) {
      this.registerCount = count;
    },
    setLoginPopUpInfo(loginPu) {
      this.loginPus = loginPu;
    },
    setVassGame(vassGame) {
      this.vassGame = vassGame;
    },
    setService(link) {
      this.link = link;
      set("service_link", link);
    },
    setLottCenter(lotts) {
      this.lotts = lotts;
    },
    regValidatas(rValidata) {
      this.rValidata = rValidata;
    },
    setCount(timeCount) {
      this.timeCount = timeCount;
    },
    setNumero(numero) {
      this.numero = numero;
    },
    setDownLinkObj(resources) {
      const results = resources.reduce((prev, curr) => {
        return { ...prev, [curr.resourceType]: curr };
      }, {});
      this.downLinkObj = results;
    },
    setIp(ip) {
      this.ip = ip;
    },
    async getStatus() {
      const applySettings = (data) => {
        const { value } = data;
        const cons = value.info;
        this.setConsSetting(cons, value);
        this.setIp(value.location.ip);
        if (value.maintenance) {
          this.setSystemStatus(1);
        }
        if (value.regionRestriction && !value.maintenance) {
          this.setSystemStatus(2);
        }
        if (value.regionRestriction && value.maintenance) {
          this.setSystemStatus(1);
        }
        if (!value.regionRestriction && !value.maintenance) {
          this.setSystemStatus(0);
        }
      };
      const fetch = async () => {
        const status = await getSystemStatus();
        window.localStorage.setItem("status", JSON.stringify(status));
        applySettings(status);
        return status;
      };

      let cache = window.localStorage.getItem("status");
      if (cache) {
        try {
          cache = JSON.parse(cache);
          applySettings(cache);
          fetch();
          return Promise.resolve(cache);
        } catch (e) {
          console.error(e);
        }
      }
      return fetch();
    },
    setConsSetting(cons, value) {
      const {
        customerService,
        domainRoute,
        mobileTemplate,
        paths,
        register,
        resources,
        whitelabel,
        mainDomain,
        imageCDNs,
      } = cons;
      root.mcCommon.setConsolidated(cons);
      if (root.tcgCommon) {
        root.tcgCommon.setStatus(value);
      }
      this.setDownLink(resources);
      this.setDownLinkObj(resources);
      this.setImagesCDNs(imageCDNs);

      if (customerService.length) {
        const links = customerService.map((item) => {
          const key = item.path.substr(item.path.length - 8);
          const path = item.path.slice(0, item.path.length - 8);
          const cs_link = window.desDecrypt(path, key);
          return { ...item, path: cs_link };
        });
        this.setCustomerService(links);
      }
      this.setTemplate(cons);
    },
    setCustomerService(links) {
      const [firstLink] = links;
      if (firstLink) {
        this.setService(firstLink["path"]);
      }
      this.customerService = links;
    },
    setSystemStatus(status) {
      this.systemStatus = status;
    },
    setTemplate(cons) {
      const defaultData = {
        version: "base",
        detailVersion: "base",
      };
      if (cons.mobileTemplate.mem_cen === "1") {
        defaultData.version = "new";
      }
      if (cons.mobileTemplate.dep_func === "1") {
        defaultData.detailVersion = "new";
      }
      set("setTemplate", defaultData);

      let cz = defaultData.version === "new" ? "v-new" : "v-base";
      cz = `${cz} ${defaultData.detailVersion === "new" ? "v-detail-new" : "v-detail-base"}`;
      this.template = cz;
    },
    setDownLink(downLink) {
      this.downLink = downLink;
    },
    annount(query, flush = false) {
      return accountAgent.annount(query, flush).then((rs) => {
        if (rs.data.value) {
          const { promotion = [], banners = [], player = [], popup = [], hot = [] } = rs.data.value;
          promotion.length && this.anActivity(promotion);
          banners.length && this.anData(banners);
          player.length && this.anNotice(player);
          popup.length && this.setLoginPopUpInfo(popup);
          hot.length && this.setHotMatch(hot);
        }
        return rs;
      });
    },
    annountMarkRead(updateId, query) {
      const update = [...this.notice];
      const find = update.find(({ id }) => id === updateId);
      if (!find.read) {
        find.read = true;
        this.anNotice(update);
        accountAgent.annountMarkAsRead(updateId).then(() => {
          this.annount(query, true);
        });
      }
    },
    clearBanner() {
      this.anData([]);
    },
    registerValidate() {
      return accountAgent.regValidatas().then((rs) => {
        let data = rs.data.value;
        this.regValidatas(data);
      });
    },

    affiliates(query) {
      return accountAgent.affiliate(query).then((rs) => {
        let code = rs.data.value ? rs.data.value.code : "";
        this.setAfflite(code);
        this.setAffCount(1);
        return rs;
      });
    },

    homePopulor(query) {
      return new Promise((resolve) => {
        const firstTime = new Date().getTime();
        let lastTime = null;
        let counts = {};
        accountAgent.populars(query).then((res) => {
          lastTime = new Date().getTime();
          counts = { firstTime: firstTime, lastTime: lastTime };
          this.setCount(Object.values(counts));
          resolve(Object.values(res.data));
        });
      });
    },
    getVassGameType(query) {
      let sessionVassGame = get("getVassGameType");
      if (!sessionVassGame) {
        return accountAgent.getVassGame(query).then((rs) => {
          let data = rs.data.value.content;
          set("getVassGameType", data);
          this.setVassGame(data);
        });
      } else {
        this.setVassGame(sessionVassGame);
      }
    },

    getLotts(query) {
      let sessionGameLotts = get("getGameLotts");
      if (!sessionGameLotts) {
        return accountAgent.getLott(query).then((rs) => {
          let data = rs.data;
          set("getGameLotts", data);
          this.setLottCenter(data);
        });
      } else {
        this.setLottCenter(sessionGameLotts);
      }
    },
    renew(force = false) {
      const cache = get("memberInfo");
      if (!force && cache) {
        this.setMemberInfo(cache);
        return Promise.resolve({
          data: {
            success: true,
            value: cache,
          },
        });
      }

      return memberAgent.getMemberInfo().then((res) => {
        this.setMemberInfo(res.data.value);
        set("memberInfo", res.data.value);
        return res;
      });
    },
    getService() {
      return accountAgent.oService().then((res) => {
        let list = res.data.value.version.pathList;
        for (let i = 0; i < list.length; i++) {
          if (list[i].type === "3") {
            var key = list[i].path.substr(list[i].path.length - 8);
            var path = list[i].path.slice(0, list[i].path.length - 8);
            var cs_link = window.desDecrypt(path, key);
            this.setService(cs_link);
            set("service_link", cs_link);
          }
        }
      });
    },
    getNumero(query) {
      const firstTime = new Date().getTime();
      let lastTime = null;
      let counts = {};
      return gameCenter.getNumero(query).then((res) => {
        lastTime = new Date().getTime();
        counts = { firstTime: firstTime, lastTime: lastTime };
        this.setCount(Object.values(counts));
        this.setNumero(Object.values(res.data));
        return Object.values(res.data);
      });
    },
    getChatUnreadCount() {
      return chatUnreadCount().then((res) => {
        if (res.data && res.data.value) {
          this.setChatUnreadCount(res.data.value.unReadCount);
        }
        return res;
      });
    },
    setChatUnreadCount(c) {
      this.chatUnreadCount = +c;
    },
    getInboxUnreadCount() {
      return accountAgent.getInboxUnreadCount().then((res) => {
        this.setInboxUnreadCount(+res.data.value);
        return res;
      });
    },
    setInboxUnreadCount(c) {
      this.inboxUnreadCount = +c;
    },
    getHelpCenter(query) {
      return accountAgent.helpCenter(query).then((res) => {
        this.setHelpCenter(res.data.value);
        return res.data.value;
      });
    },
    setHelpCenter(value) {
      this.helpList = value || [];
    },
    getDomainRoute() {
      return accountAgent.getDomainRoute().then((res) => {
        this.setDomainRoute(res.data.value.domainList);
        return res.data.value.domainList;
      });
    },
    setDomainRoute(value) {
      this.domainRoute = value;
    },
    openUserPageStatus() {
      this.userPageOpen = true;
    },
    closeUserPageStatus() {
      this.userPageOpen = false;
    },
    setDownPopup(value) {
      this.showDownPopup = value;
    },
    getModels(active, streaming) {
      return getModelList({ active, streaming }).then((payload) => {
        const modelList = payload.data.value;
        const hasModel = modelList.length > 0;
        this.setModelStatus(hasModel);
        return modelList;
      });
    },
    setImagesCDNs(value) {
      this.imageCDNs = value;
    },
    showSideMenu(value) {
      this.sideMenuOpen = value;
      this.rightMenuOpen = false;
    },
    showRightMenu(value) {
      this.rightMenuOpen = value;
      this.sideMenuOpen = false;
    },
    showHotMatch(value) {
      this.hotMatchStatus = value;
    },
    hideDownloadBar() {
      this.downloadBarStatus = false;
    },
    showAgentModal(value) {
      this.agentModalStatus = value;
    },
    showGameFilter(value) {
      this.gameFilterStatus = value;
    },
    showProvider(value) {
      this.providerStatus = value;
    },
    showBalance(value) {
      this.hideBalance = value;
    },
    toggleBalance() {
      this.hideBalance = !this.hideBalance;
    },
    setVideo(key, time) {
      this.video = { ...this.video, [key]: time };
    },
    showLanguage(value) {
      this.languageModalStatus = value;
    },
    showIosTutorial(value) {
      this.iosTutorialStatus = value;
    },
    showLogin(value) {
      this.loginModalStatus = value;
      this.registerModalStatus = false;
      this.forgetModalStatus = false;
      this.showFormPopup(value);
    },
    showRegister(value) {
      this.registerModalStatus = value;
      this.loginModalStatus = false;
      this.forgetModalStatus = false;
      this.showFormPopup(value);
    },
    showForget(value) {
      this.forgetModalStatus = value;
      this.loginModalStatus = false;
      this.registerModalStatus = false;
      this.showFormPopup(value);
    },
    showChangePsw(value) {
      this.changePswModalStatus = value;
    },
    showFormPopup(value) {
      this.formPopupStatus = value;
    },
    showSearchPop(value) {
      this.searchPopStatus = value;
    },
    showHelpPopup(value) {
      this.helpPopupStatus = value;
    },
    showFooterMenu(value) {
      this.footerMenuStatus = value;
    },
    showCategoryPopup(value) {
      this.categoryPopupStatus = value;
    },
    showPolicyPopup(value) {
      this.policyPopupStatus = value;
    },
    showSymbolLang(value) {
      this.symbolLangOpen = value;
    },
    showChannelPopup() {
      this.channelPopupStatus = !this.channelPopupStatus;
    },
  }
);

export default CommonStore;
