import { types } from "mobx-state-tree";

import gameCenter from "../apis/gameCenter";
import deploy from "../config/deploy.config";
import { get, set } from "../utils/storage";

const GameCenterStore = types.model(
  "GameCenterStore",
  {
    VassGameType: types.frozen,
    GameList: types.optional(types.frozen, []),
    hotGames: types.optional(types.frozen, []),
    favGameList: types.optional(types.frozen, { content: [], pageNum: 1, totalPages: 0 }),
    favGames: types.optional(types.frozen, []), // 游戏收藏id
    gameVendor: types.optional(types.frozen, { sorting: [], mapping: {} }),
    RecentGameList: types.frozen,
    LottList: types.frozen,
    ElottList: types.frozen,
    lotteryTime: types.frozen,
    prizeMode: types.optional(types.frozen, ""), // 彩票玩法，传统/盘口
    lottMenus: types.optional(types.frozen, []), // 彩票分类列表
    winnerList: types.optional(types.frozen, []), // 中奖排行榜
    currentGameCategory: types.optional(types.frozen, "HOME"),
    currentVassalage: types.optional(types.frozen, ""),
    currentDataType: types.optional(types.frozen, "ALL"),
    get getLottType() {
      let lottType = [];
      for (const key in this.LottList) {
        lottType[key] = this.LottList[key].displayName;
      }
      return lottType;
    },
    get getElottClass() {
      let classBox = {};
      if (this.ElottList) {
        this.ElottList.games.forEach((item, idx) => {
          classBox[item.gameClassifyNew] = true;
        });
      }
      return Object.keys(classBox).map((item, idx) => {
        return { code: item };
      });
    },
  },
  {
    getWinnerBoard(query) {
      return gameCenter.getWinnerBoard(query).then((res) => {
        const results = res.data.value.list;
        this.setWinnerList(results);
        return res.data.value;
      });
    },
    getGameList(formData, refresh) {
      return gameCenter.getGameList(formData).then((res) => {
        this.setGameList(res.data.value, refresh);
        return res.data.value;
      });
    },
    clearGameList() {
      this.GameList = { games: [] };
    },
    setGameList(GameList, refresh) {
      if (!refresh) {
        this.GameList = GameList;
      } else if (this.GameList && this.GameList.games && this.GameList.games.length > 0) {
        const newTrans = this.GameList.games.concat(GameList.games);
        const newDetails = {
          games: newTrans,
          pageNum: GameList.pageNum,
          totalPages: GameList.totalPages,
        };
        this.GameList = newDetails;
      }
    },
    addFavGame(formData) {
      const nodeId = `${formData.nodeId}`;
      if (!this.favGames.includes(nodeId)) {
        this.favGames = [...this.favGames, nodeId];
        return gameCenter.addFavGame(formData);
      }
      return null;
    },
    removeFavGame(formData) {
      this.favGames = this.favGames.filter((item) => +item !== +formData.nodeId);
      this.favGameList = {
        ...this.favGameList,
        content: this.favGameList.content.filter((item) => +item.nodeId !== +formData.nodeId),
      };
      return gameCenter.removeFavGame(formData);
    },
    getFavGames(formData) {
      return gameCenter.getFavGames(formData).then((res) => {
        this.setFavGames(res.data.value);
        return res.data.value;
      });
    },
    setFavGames(favGames) {
      this.favGames = favGames;
    },
    getFavGameList(formData, loadMore) {
      return gameCenter.getFavGameList(formData).then((res) => {
        this.setFavGameList(res.data.value, loadMore);
        return res.data.value;
      });
    },
    setFavGameList(games, loadMore) {
      if (!loadMore) {
        this.favGameList = games;
      } else {
        const newDetails = {
          content: this.favGameList.content.concat(games.content),
          pageNum: games.pageNum,
          totalPages: games.totalPages,
        };
        this.favGameList = newDetails;
      }
    },
    addRecentGame(formData) {
      return gameCenter.addRecentGame(formData);
    },
    getRecentGameList(formData) {
      return new Promise((resolve) => {
        gameCenter.getRecentGameList(formData).then((res) => {
          this.setRecentGameList(res.data.value);
          resolve(res.data.value);
        });
      });
    },
    setRecentGameList(RecentGameList) {
      this.RecentGameList = RecentGameList;
    },
    async getGameHot(query) {
      const response = await gameCenter.getGameHot(query);
      const data = response?.data?.value?.list;
      this.setGamesHot(data);
      return data;
    },
    setGamesHot(gameHot) {
      this.hotGames = gameHot;
    },
    getGameMenus(query) {
      return gameCenter.getGameMenus(query).then((res) => {
        const results = res?.data || [];
        this.setLottMenus(results);
        return results;
      });
    },
    setLottMenus(menu) {
      this.lottMenus = menu;
    },
    getLottList() {
      if (get("lottList")) {
        this.setLottList(get("lottList"));
      } else {
        return gameCenter.getLottList().then((res) => {
          this.setLottList(Object.values(res.data));
          set("lottList", Object.values(res.data));
        });
      }
    },
    setLottList(LottList) {
      this.LottList = LottList;
    },
    getElottList(formData) {
      if (get("elottList")) {
        this.setElottList(get("elottList"));
      } else {
        return gameCenter.getGameList(formData).then((res) => {
          this.setElottList(res.data.value);
          set("elottList", res.data.value);
        });
      }
    },
    setElottList(ElottList) {
      this.ElottList = ElottList;
    },
    setPrizeMode(mapping) {
      let gameType = "LOTT";
      if (["TCG_LOTTO_VN", "LOTT"].some((item) => !!mapping?.ELOTT?.[item])) {
        gameType = "ELOTT";
      }
      const lottMode = {
        LOTT: deploy.prizeModes.ModeLott,
        ELOTT: deploy.prizeModes.ModeElott,
      };
      this.prizeMode = lottMode[gameType];
    },
    setGameVendor(res) {
      const { sorting = [], mapping = {} } = JSON.parse(JSON.stringify(res));
      this.setPrizeMode(mapping);
      let esportGame = [];
      let cockfightGame = [];
      let inHouse = [];

      let sortingVendor = sorting.reduce((acc, item) => {
        if (item.gameCategory === "SPORTS") {
          esportGame = item["vendorNames"]
            .filter((item) => deploy.esportGames.includes(item.accountTypeName))
            .map((el) => item[el["accountTypeName"]]);
          cockfightGame = item["vendorNames"]
            .filter((item) => deploy.cockfightGames.includes(item.accountTypeName))
            .map((el) => item[el["accountTypeName"]]);
          const sportGame = item["vendorNames"].filter(
            (item) => ![...deploy.esportGames, ...deploy.cockfightGames].includes(item.accountTypeName)
          );
          item.vendorNames = sportGame;
          if (!sportGame.length) {
            return acc;
          }
        }
        return [
          ...acc,
          {
            gameCategory: item.gameCategory,
            vendorNames: item.vendorNames.map((subItem) => {
              return item[subItem["accountTypeName"]];
            }),
          },
        ];
      }, []);

      const sportIndex = sortingVendor.findIndex((item) => item.gameCategory === "SPORTS");

      if (esportGame?.length > 0) {
        sortingVendor.splice(sportIndex + 1, 0, {
          gameCategory: "ESPORTS",
          vendorNames: esportGame,
        });
      }

      if (cockfightGame?.length > 0) {
        sortingVendor.splice(sportIndex + 2, 0, {
          gameCategory: "COCKFIGHT",
          vendorNames: cockfightGame,
        });
      }

      if (inHouse?.length > 0) {
        sortingVendor.push({
          gameCategory: "INHOUSE",
          vendorNames: inHouse,
        });
      }

      const mappingVendor = Object.fromEntries(sortingVendor.map((item) => [item.gameCategory, item.vendorNames]));
      this.gameVendor = {
        sorting: sortingVendor,
        mapping: mappingVendor,
      };
    },
    setWinnerList(data = []) {
      const arr = data.map((item) => item.vo);
      arr.sort((a, b) => b.winAmount - a.winAmount);
      this.winnerList = arr;
    },
    setCurrentGameCategory(gameType) {
      this.currentGameCategory = gameType;
    },
    setCurrentVassalage(vassalage) {
      this.currentVassalage = vassalage;
    },
    setCurrentDataType(dataType) {
      this.currentDataType = dataType;
    },
  }
);

export default GameCenterStore;
