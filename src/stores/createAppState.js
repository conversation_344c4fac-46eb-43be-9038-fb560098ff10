import { tcgCommon } from "tcg-mobile-common";
// Member Center's Store.
import memberCenter from "tcgmodulemc";

import config from "../config/deploy.config";
import deploy from "../config/deploy.config";

import AuthStore from "./auth";
import CommonStore from "./common";
import GameCenter from "./gameCenter";
import LanguageShellStore from "./language";
import LiveStore from "./live";
import LoginStore from "./login";
import RegisterStore from "./register";

const init = window.localStorage.getItem("language") || deploy.language;
const store = Object.assign(
  {
    common: CommonStore.create({ aff: "" }),
    languageShell: LanguageShellStore.create({
      language: init || config.language,
    }),
    auth: AuthStore.create({ token: "", trail: "" }),
    login: LoginStore.create(),
    register: RegisterStore.create(),
    gameCenter: GameCenter.create(),
    anchor: LiveStore.create({}),
  },
  memberCenter.memberCenterStore,
  tcgCommon
);

export default store;
