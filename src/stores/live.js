import { types } from "mobx-state-tree";

import live from "../apis/live";
import { get } from "../utils/storage";

const LiveStore = types.model(
  "CommonStore",
  {
    modelList: types.optional(types.frozen, []),
    hotAnchorList: types.optional(types.frozen, []),
    focusList: types.optional(types.frozen, []),
    recommendAnchorList: types.optional(types.frozen, []),
    isRecommendAnchorListLoading: types.optional(types.frozen, true),
    isHotAnchorListLoading: types.optional(types.frozen, true),
    unReadCount: types.frozen,
    launchChat: types.frozen,
    get loginPopUpInfo() {
      let loginPu = get("ANNOUNT") && get("ANNOUNT").popup ? get("ANNOUNT").popup : this.loginPus;
      if (loginPu && loginPu.length > 0) {
        loginPu[0].content = loginPu[0].content.replace(/[0-9]*px/g, function (str) {
          var num = str.replace(/[^0-9]/gi, "");
          return num / 100 + "rem";
        });
      }
      return loginPu ? loginPu : [];
    },
  },
  {
    getHotAnchorList() {
      return live.getAchorList({ type: "hot" }).then((res) => {
        this.setHotAnchorList(res.data.value);
        this.setIsHotAnchorListLoading(false);
      });
    },
    getRecommendAnchorList() {
      return live.getAchorList({ type: "promo" }).then((res) => {
        this.setRecommendAnchorList(res.data.value);
        this.setIsRecommendAnchorListLoading(false);
      });
    },
    getModelList(query) {
      return live.getAchorList(query).then((res) => {
        this.setModelList(res?.data?.value || []);
        return res.data.value;
      });
    },
    getFocusList() {
      return live.getFocusList().then((res) => {
        this.setFocusList(res?.data?.value || []);
        return res.data.value;
      });
    },
    setHotAnchorList(list) {
      this.hotAnchorList = list;
    },
    setRecommendAnchorList(list) {
      this.recommendAnchorList = list;
    },
    setModelList(list) {
      this.modelList = list;
    },
    setFocusList(list) {
      this.focusList = list;
    },
    setIsRecommendAnchorListLoading(loading) {
      this.isRecommendAnchorListLoading = loading;
    },
    setIsHotAnchorListLoading(loading) {
      this.isHotAnchorListLoading = loading;
    },
    getUnRead(query) {
      return live.getUnReadCount(query).then((res) => {
        this.setUnReadCount(res.data.value.unReadCount);
      });
    },
    setUnReadCount(count) {
      this.unReadCount = count;
    },
    getLaunchChat(query) {
      return live.getLaunchChat(query).then((res) => {
        this.setLaunchChat(res.data.value.chatUrl);
        return res.data.value.chatUrl;
      });
    },
    setLaunchChat(count) {
      this.launchChat = count;
    },
  }
);

export default LiveStore;
