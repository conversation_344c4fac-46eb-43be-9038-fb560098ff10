import { types } from "mobx-state-tree";

import accountAgent from "../apis/account";
import deploy from "../config/deploy.config";
import { get, set } from "../utils/storage";

import root from "./createAppState";

const AuthStore = types.model(
  "AuthStore",
  {
    token: types.string,
    trail: types.frozen, // 试玩账号
    psw: types.frozen,
    cap: types.frozen,
    vipProgress: types.optional(types.frozen, 0),
    loginTime: types.optional(types.frozen, ""),
    get currentToken() {
      let sessionMetadata;
      if (this.token) {
        return this.token;
      }
      sessionMetadata = get("MC_SESSION_INFO");
      return sessionMetadata ? sessionMetadata.token : "";
    },
    get merchantCode() {
      const sessionMetadata = get("MC_SESSION_INFO");
      return sessionMetadata ? sessionMetadata.merchantCode : "";
    },
    get trailUser() {
      let sessionTestUsername = sessionStorage.getItem("trialName");
      return this.trail ? this.trail : sessionTestUsername;
    },
    get catpchat() {
      return this.cap ? this.cap : "";
    },
    get isLogin() {
      return !!this.currentToken;
    },
    get isAgent() {
      const memberInfo = root.common.memberInfo;
      if (memberInfo) {
        return deploy.agentType.includes(memberInfo?.type);
      }
      const session = get("MC_SESSION_INFO");
      if (!session) return false;
      return deploy.agentType.includes(session?.type);
    },
  },
  {
    setToken(token) {
      this.token = token;
    },
    setUser(user) {
      this.user = user;
    },
    setTrailUser(user) {
      this.trail = user;
    },
    setChangePsw(psw) {
      this.psw = psw;
    },
    setCaptcha(cap) {
      this.cap = cap;
    },
    login(payload) {
      return accountAgent.login(payload).then((ret) => {
        set(
          "MC_SESSION_INFO",
          JSON.stringify({
            token: ret.data.value.token,
            merchantCode: deploy.merchant,
            userName: ret.data.value.userName,
            type: ret.data.value.type,
            firstTimeLogin: ret.data.value.firstTimeLogin,
          })
        );
        this.setToken(ret.data.value.token);
        return ret;
      });
    },
    getHeadIcon() {
      return accountAgent.getHeadIcon();
    },
    setHeadIcon(data) {
      return accountAgent.setHeadIcon(data);
    },
    setRegisterList() {
      return accountAgent.getSetRegister();
    },
    testplays() {
      return accountAgent.testPlay().then((ret) => {
        set(
          "MC_SESSION_INFO",
          JSON.stringify({
            token: ret.data.value.token,
            merchantCode: deploy.merchant,
            userName: ret.data.value.userName,
            type: ret.data.value.customerType,
          })
        );
        this.setTrailUser(ret.data.value);
      });
    },
    register(payload, regist_method) {
      return accountAgent.register(payload, regist_method);
    },
    trialLogins(payload, callback) {
      return accountAgent.login(payload).then((ret) => {
        set(
          "MC_SESSION_INFO",
          JSON.stringify({
            token: ret.data.value.token,
            merchantCode: deploy.trailMerchant,
          })
        );
        this.setToken(ret.data.value.token);
        callback.apply(this, [ret]);
      });
    },
    trialRegisters(payload) {
      return accountAgent.register(payload);
    },
    createTrailUser() {
      return accountAgent.createTrailAccount().then((rs) => {
        this.setTrailUser(rs.data.value);
        set("trialName", rs.data.value);
      });
    },
    captchas() {
      return new Promise((resolve) => {
        accountAgent.captchas().then((rs) => {
          let captchaImg = rs.data.value;
          this.setCaptcha(captchaImg);
          resolve(rs.data.value);
        });
      });
    },
    email(payload) {
      return accountAgent.email(payload);
    },
    username(payload, moduleId) {
      return accountAgent.vlideUsername(payload, moduleId);
    },
    logout(auths) {
      return accountAgent.logout(auths).then((rs) => {
        window.sessionStorage.clear();
        this.setToken("");
        this.setTrailUser(undefined);
        root.common.setMemberInfo(undefined);
        root.mcCommon.setMemberInfo(undefined);
      });
    },
    getPlayerRankProgress() {
      return accountAgent.getPlayerRankProgress().then((res) => {
        const completedConditions = res.data?.value?.currentProgress?.completedConditions;
        const totalExpectedConditions = res.data?.value?.currentProgress?.totalExpectedConditions;
        const labelId = res.data?.value?.playerRankProgress?.labelId;
        this.setVipProgress({ completedConditions, totalExpectedConditions, labelId });
      });
    },
    setVipProgress(value) {
      let vipProgress = 0;
      const { completedConditions, totalExpectedConditions, labelId } = value;
      if (!labelId) vipProgress = 1;
      else {
        try {
          vipProgress = completedConditions / totalExpectedConditions;
        } catch {}
      }
      this.vipProgress = vipProgress;
    },
    setLoginTime(value) {
      this.loginTime = value;
    },
  }
);

export default AuthStore;
