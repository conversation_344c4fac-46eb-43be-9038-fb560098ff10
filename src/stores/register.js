import { types } from "mobx-state-tree";

import store from "./createAppState";

const RegisterStore = types.model(
  "RegisterStore",
  {
    showRegister: types.frozen,
    registerSuccess: types.frozen,
    isHideRegister: types.frozen, // aff code success or failed
    get isShowRegister() {
      return this.showRegister;
    },
    get isRegisterSuccess() {
      return this.registerSuccess;
    },
    get hideRegister() {
      return this.isHideRegister;
    },
  },
  {
    showRegisterModal() {
      store.auth.captchas();
      this.showRegister = true;
    },
    closeRegisterModal() {
      this.showRegister = false;
    },
    setRegisterFlag(flag) {
      this.registerSuccess = flag;
    },
    setHideRegister(flag) {
      this.isHideRegister = flag;
    },
  }
);

export default RegisterStore;
