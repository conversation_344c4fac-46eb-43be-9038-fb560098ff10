import dayjs from "dayjs";
import { get } from "lodash";
import { types } from "mobx-state-tree";
import PubSub from "pubsub-js";
import { updateLanguage } from "tcg-mobile-common";

import { isoLangMap } from "@/utils/isoLang";

import deployConfig from "../config/deploy.config";
import i18n from "../language";

import store from "./createAppState";

const LanguageShellStore = types.model(
  "LanguageShellStore",
  {
    language: types.frozen,
    get currentLanguage() {
      return this.language;
    },
    get t() {
      return (key, ...values) => `${get(this.resource, key, key) || ""}`.format(...values);
    },
    get resource() {
      return i18n[this.language] || i18n[deployConfig.language] || {};
    },
  },
  {
    async changeLanguageTo(language) {
      const changeTo = await updateLanguage(deployConfig.supportLanguages, language);
      document.documentElement.setAttribute("lang", changeTo.toLowerCase());
      this.setLanguage(changeTo);
      store.mcLanguage.changeLanguageTo(changeTo);

      dayjs.locale(isoLangMap[changeTo]?.code);
    },
    setLanguage(language) {
      const old = this.language;
      this.language = language;
      if (old !== language) {
        PubSub.publish("changeLanguage", language);
      }
    },
  }
);
export default LanguageShellStore;
