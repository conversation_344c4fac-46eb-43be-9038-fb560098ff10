import React, { Component } from "react";
import { withRout<PERSON> } from "react-router-dom";
import cz from "classnames";
import { computed, reaction } from "mobx";
import { inject, observer } from "mobx-react";

import apiUtils from "@/apis/apiUtils";
import SpinnerLoading from "@/components/SpinnerLoading";
import api from "@/config/address.config.js";
import deploy from "@/config/deploy.config";
import { get } from "@/utils/storage";

import "./FBGame.scss";

async function sleep(ms) {
  return new Promise((resolve) => setTimeout(() => resolve(), ms));
}

@inject("common", "mcCommon", "auth", "tcgCommon")
@withRouter
@observer
class FbGame extends Component {
  @computed get isLogin() {
    return this.props.auth.isLogin;
  }

  // constructor(props) {
  //   super(props);
  //   props.cacheLifecycles.didRecover(this.componentDidRecover);
  // }

  state = { gameSrc: "about:blank", loading: true };
  pending = false;

  get showIframe() {
    return this.state.gameSrc && !this.state.loading;
  }

  get hasDownloadBar() {
    return this.props.tcgCommon.isShowDownloadBar;
  }

  componentDidMount() {
    this.launchGame();
    window.addEventListener("message", this.handleLogin);

    this.reactionLogin = reaction(
      () => this.isLogin,
      () => {
        if (this.isLogin) {
          console.log("reactionLogin");
          this.launchGame();
        }
      }
    );
  }

  componentWillUnmount() {
    this.reactionLogin();
    window.removeEventListener("message", this.handleLogin);
  }

  // componentDidRecover = () => {
  //   if (!this.state.gameSrc || !this.state.gameSrc.startsWith("http")) {
  //     this.launchGame();
  //   }
  // };

  handleLogin = (e) => {
    if (e?.data?.relogin) {
      this.props.history.push("/m/login");
    }
  };

  launchGame = async () => {
    if (this.pending) return;
    this.pending = true;
    try {
      const session = get("MC_SESSION_INFO");
      if (!session) {
        const res = await apiUtils.get(api.game.gameLaunchFB, { query: { platform: "html5" } });
        this.loadGameSrc(res.data?.value?.content?.game_url);
      } else {
        await this.props.tcgCommon.getGameVendor();
        const game = this.props.tcgCommon.gameVendor?.mapping?.SPORTS?.FB;
        if (!game) {
          return;
        }
        const res = await apiUtils.get(api.game.launchGame, {
          query: {
            confirmTrans: 0,
            launchMode: "GLS",
            platform: "html5",
            clientType: "2",
            gameId: game.gameId,
            nodeId: game.nodeId,
            accountType: 1,
            vassalage: "FB",
            merchantCode: deploy.merchant,
            language: window.localStorage.getItem("language"),
            webView: false,
          },
        });
        this.loadGameSrc(res.data?.value?.content?.game_url);
      }
    } catch (error) {
      console.error("Error launching game:", error);
    } finally {
      this.pending = false;
    }
  };

  loadGameSrc = (gameSrc) => {
    this.setState({ loading: true, gameSrc: "about:blank" }, async () => {
      await sleep(150); // 等待iframe reset
      this.setState({ gameSrc });
      this.iframeWrapper?.addEventListener(
        "load",
        async () => {
          console.log("iframe loaded");
          await sleep(100);
          this.setState({ loading: false });
        },
        { once: true }
      );
    });
  };

  setIframeRef = (c) => {
    this.iframeWrapper = c;
    if (!this.iframeWrapper) {
      // hot reload issues
      return;
    }
    this.iframeWrapper.setAttribute("allowfullscreen", true);
    this.iframeWrapper.setAttribute("webkitallowfullscreen", true);
    this.iframeWrapper.setAttribute("oallowfullscreen", true);
    this.iframeWrapper.setAttribute("msallowfullscreen", true);
    this.iframeWrapper.setAttribute("mozallowfullscreen", true);
  };

  render() {
    const containerCz = cz(`fb-game-iframe-container`, {
      fullscreen: this.props.common.fullscreen,
      fullheight: !this.hasDownloadBar,
    });
    const iframeCz = cz(`js-iframe game-iframe`, { hide: !this.showIframe });
    return (
      <div className={containerCz}>
        {this.state.loading && <SpinnerLoading />}
        <iframe
          id="third-game"
          src={this.state.gameSrc}
          className={iframeCz}
          title="third-game"
          scrolling="yes"
          ref={this.setIframeRef}
        />
      </div>
    );
  }
}

export default FbGame;
