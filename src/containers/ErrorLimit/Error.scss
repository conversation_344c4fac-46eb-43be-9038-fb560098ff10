.error-ip,
.error-service {
  position: fixed;
  width: 100%;
  height: 100%;
  padding: 30px;
}

.error-ip {
  background: url("../../assets/images/error/ip.jpg") no-repeat center center fixed;
  background-size: cover;
}

.error-service {
  background: url("../../assets/images/error/service.jpg") no-repeat center center fixed;
  background-size: cover;
}

.errr-logo {
  width: 200px;
}

.error-contact-us {
  position: absolute;
  bottom: 76px;
  left: 50%;
  width: 60%;
  overflow: hidden;
  font-size: 30px !important;
  color: #fff !important;
  text-align: center;
  background: linear-gradient(to right, #fe6e74, #f3077f);
  border: none !important;
  border-radius: 50px !important;
  transform: translateX(-50%);
}

.error-icon {
  width: 30px;
  margin-right: 10px;
}

.error-text {
  position: absolute;
  bottom: 20%;
  left: 50%;
  width: 80%;
  transform: translateX(-50%);
}

.error-content {
  position: absolute;
  bottom: 20%;
  left: 50%;
  width: 80%;
  color: #fff;
  text-align: center;
  transform: translateX(-50%);

  .title {
    margin-bottom: 10px;
    font-size: 50px;
  }

  .ip-address {
    margin-bottom: 20px;
    font-size: 30px;
  }

  .error-info {
    margin-top: 30px;
  }

  p {
    font-size: 30px;
    line-height: 40px;
    white-space: pre-wrap;
  }
}
