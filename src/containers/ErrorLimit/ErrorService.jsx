import React from "react";
import { Button } from "antd-mobile";
import { inject, observer } from "mobx-react";
import { CommonLogo, visibilityChangeReload } from "tcg-mobile-common";
import { utils } from "tcgmodulemc";

import "./Error.scss";

@visibilityChangeReload
@inject("common")
@observer
class ErrorService extends React.Component {
  handleCSLink = () => {
    utils.openWinUtils.handleWindowPage(this.props.common.link, 1);
  };
  render() {
    return (
      <div className="error-service">
        <CommonLogo className="errr-logo" src={require("@/assets/images/logo/logo.png")} />
        <div className="error-content">
          <h6 className="title">Bảo Trì Hệ Thống</h6>
          <h6 className="title">Maintance</h6>
          <div className="error-info">
            <p>
              <PERSON>uý khách hàng thân mến: <PERSON><PERSON> thống chúng tôi đang bảo trì để nâng cấp, <PERSON><PERSON><PERSON> tôi chân thành cáo lỗi về sự
              bất tiện này! Nếu Quý khách có những vấn đề khác cần hỗ trợ, vui lòng liên hệ Chăm sóc khách hàng của
              chúng tôi.
            </p>
          </div>
        </div>
        <Button className="btn error-contact-us" onClick={this.handleCSLink}>
          <img className="error-icon" alt="button" src={require("../../assets/images/error/icon-mob.png")} />
          Liên lạc với chúng tôi
        </Button>
      </div>
    );
  }
}

export default ErrorService;
