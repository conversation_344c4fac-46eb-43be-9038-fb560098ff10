import React from "react";
import { Button } from "antd-mobile";
import { inject, observer } from "mobx-react";
import { CommonLogo, visibilityChangeReload } from "tcg-mobile-common";
import { utils } from "tcgmodulemc";

import "./Error.scss";

@visibilityChangeReload
@inject("common")
@observer
class ErrorIp extends React.Component {
  handleCSLink = () => {
    utils.openWinUtils.handleWindowPage(this.props.common.link, 1);
  };
  render() {
    return (
      <div className="error-ip">
        <CommonLogo className="errr-logo" src={require("@/assets/images/logo/logo.png")} />
        <div className="error-content">
          <h6 className="title">Truy Cập Bị Từ Chối</h6>
          <h6 className="title">Access Denied</h6>
          <div className="error-info">
            <div className="ip-address">{`Địa chỉ IP của bạn：${this.props.common.ip}`}</div>
            <p>
              <PERSON><PERSON><PERSON> khách hàng thân mến: IP vị trí hiện tại hoặc IP bạn đang sử dụng bị giới hạn, bạn không thể truy cập
              trang chúng tôi. Mong bạn thông cảm!
            </p>
          </div>
        </div>
        <Button className="btn error-contact-us" onClick={this.handleCSLink}>
          <img className="error-icon" alt="button" src={require("../../assets/images/error/icon-mob.png")} />
          Liên lạc với chúng tôi
        </Button>
      </div>
    );
  }
}

export default ErrorIp;
