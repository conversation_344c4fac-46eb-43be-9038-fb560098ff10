import React from "react";
import { inject, observer } from "mobx-react";
import propTypes from "prop-types";
import { wysiwygUtil } from "tcg-mobile-common";

import CommonHeader from "../../components/OutterHeader/CommonHeader";

import "./ActivityDetail.scss";

@inject("common", "languageShell")
@observer
class ActivityDetail extends React.Component {
  getImage(item) {
    const { announcementImages } = item;
    return ((announcementImages || []).find(({ sequence }) => sequence === 1) || {})?.url;
  }
  render() {
    const { item } = this.props;
    return (
      <div className="activity-detail-container">
        <CommonHeader
          title={
            <div className="header-title">
              <span>{this.props.languageShell.t("details")}</span>
            </div>
          }
        />
        <div className="activity-detail-content">
          <div className="detail-content-bg">
            <div className="activity-info">
              <div className="activity-img">
                <img
                  src={this.getImage(item)}
                  alt=""
                  onError={(e) => {
                    e.target.classList.add("hide");
                  }}
                />
              </div>
              {/* <div className="activity-title">
              <h5>{item.title}</h5>
            </div> */}
            </div>
            <div className="activity-desc-wrap">
              <div className="activity-desc">
                <div
                  dangerouslySetInnerHTML={{
                    __html: wysiwygUtil.convertContent(item.content),
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

ActivityDetail.propTypes = {
  item: propTypes.object.isRequired,
};

ActivityDetail.defaultProps = {
  item: {},
};

export default ActivityDetail;
