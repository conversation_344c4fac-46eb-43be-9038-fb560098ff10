import React, { Component } from "react";
import { inject, observer } from "mobx-react";

import Header from "@/components/OutterHeader/Header";
import withAuth from "@/hoc/withAuth";
import { get } from "@/utils/storage";

import "./Setting.scss";

@inject("auth", "common", "languageShell")
@withAuth
@observer
class Setting extends Component {
  componentDidMount() {
    if (!get("MC_SESSION_INFO")) {
      this.props.push("/m/login");
    }
  }
  handleLogout = () => {
    this.props.logout({ confirm: false });
  };
  changeRoute = (path) => {
    if (get("MC_SESSION_INFO")) {
      this.props.push(path);
    } else {
      this.props.push("/m/login");
    }
  };
  render() {
    return (
      <div className="setting-container app-main">
        <Header />
        <div className="page-content">
          <ul className="setting-list">
            <li onClick={() => this.changeRoute("/m/changePsw")}>
              <span>{this.props.languageShell.t("change_password")}</span>
            </li>
            {/* <li onClick={() => this.changeRoute("/m/feedback")}>
              <span>意见反馈</span>
            </li> */}
          </ul>
          <div className="logout-btn" onClick={this.handleLogout}>
            {this.props.languageShell.t("in_sign_out")}
          </div>
        </div>
      </div>
    );
  }
}

export default Setting;
