import React, { Component } from "react";
import cz from "classnames";
import { inject, observer } from "mobx-react";

import SpinnerLoading from "../../components/SpinnerLoading";
import deploy from "../../config/deploy.config";
import { get } from "../../utils/storage";

import "./CustomerTask.scss";

async function sleep(ms) {
  return new Promise((resolve) => setTimeout(() => resolve(), ms));
}

@inject("common", "wallet")
@observer
class FbGame extends Component {
  state = { gameSrc: "about:blank", loading: true };

  get showIframe() {
    return this.state.gameSrc && !this.state.loading;
  }

  componentDidMount() {
    this.launchGame();

    const session = get("MC_SESSION_INFO");
    let data = {
      types: "B,PL,PU,H",
      groupName: "",
      platform: "M",
      merchantCode: deploy.merchant,
      token: session ? session.token : "",
    };
    this.props.common.annount(data);
    if (session) {
      this.props.wallet.getWallet();
    }
  }

  launchGame = () => {
    const url = this.props.common.downLinkObj?.TASK?.url;
    const session = get("MC_SESSION_INFO");
    if (!!url) {
      const urlInfo = new URL(url);
      if (session) {
        urlInfo.searchParams.set("token", session?.token);
      }
      this.loadGameSrc(urlInfo.toString());
    } else {
      this.props.replace("/m/home");
    }
  };

  loadGameSrc = (gameSrc) => {
    this.setState({ loading: true, gameSrc: "about:blank" }, async () => {
      await sleep(150); // 等待iframe reset
      this.setState({ gameSrc });
      this.iframeWrapper.addEventListener(
        "load",
        async () => {
          console.log("iframe loaded");
          await sleep(100);
          this.setState({ loading: false });
        },
        { once: true }
      );
    });
  };

  setIframeRef = (c) => {
    this.iframeWrapper = c;
    if (!this.iframeWrapper) {
      // hot reload issues
      return;
    }
    this.iframeWrapper.setAttribute("allowfullscreen", true);
    this.iframeWrapper.setAttribute("webkitallowfullscreen", true);
    this.iframeWrapper.setAttribute("oallowfullscreen", true);
    this.iframeWrapper.setAttribute("msallowfullscreen", true);
    this.iframeWrapper.setAttribute("mozallowfullscreen", true);
  };

  render() {
    const containerCz = cz(`task-iframe-container`, { fullscreen: this.props.common.fullscreen });
    const iframeCz = cz(`js-iframe task-iframe`, { hide: !this.showIframe });
    return (
      <div className={containerCz}>
        {this.state.loading && <SpinnerLoading />}
        <iframe
          id="webWidget"
          src={this.state.gameSrc}
          className={iframeCz}
          title="task-game"
          frameborder="0"
          allowFullScreen="true"
          ref={this.setIframeRef}
        />
      </div>
    );
  }
}

export default FbGame;
