.notice-container {
  .notice-content {
    overflow: hidden;
  }

  .notice-preview {
  }

  .notice-title {
    padding: 16px;
    font-size: 40px;
    font-weight: 700;
    line-height: 60px;
    color: #36f38d;
  }

  .notice-item-header {
    position: relative;
    display: flex;
    align-items: flex-start;
    width: 100%;
    padding: 32px;
    overflow: hidden;
    border-bottom: 1px solid var(--border-color-surface);

    // &.unread {
    //   .notice-item-title::after {
    //     position: absolute;
    //     top: 50%;
    //     left: 0;
    //     width: 20px;
    //     height: 20px;
    //     content: "";
    //     background: #07ddd5;
    //     border-radius: 10px;
    //     transform: translateY(-50%);
    //   }
    // }

    .notice-icon {
      @include flex-center;
      flex-shrink: 0;
      width: 64px;
      height: 64px;
      margin: 0 32px 0 0;
      background: var(--bg-color-surface);
      border-radius: 100px;

      .am-icon,
      img {
        display: block;
        width: 40px;
        height: 40px;
        object-fit: contain;
      }
    }

    .notice-item-info {
      position: relative;
      flex: 1;
      overflow: hidden;
    }

    .notice-item-date {
      flex-shrink: 0;
    }

    .notice-item-preview {
      @include trim(5);
      margin-top: 16px;
      font-size: 20px;
      font-weight: 400;
      line-height: normal;
      color: var(--text-color-primary);
    }
  }

  .accordion-arrow {
    @include flex-center;
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    margin-left: 10px;
    transition: all 0.2s;

    .am-icon {
      display: block;
      width: 20px;
      height: 32px;
    }
  }

  .notice-item {
    position: relative;

    .accordion-item-bg {
    }

    &.accordion-active {
      .accordion-arrow {
        transform: rotate(-90deg);
      }
    }
  }

  .notice-item-title {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    font-size: 24px;
    font-weight: 600;
    line-height: normal;
    color: var(--text-color-primary);

    .item-title {
      @include trim(1);
      flex: 1;
    }

    .item-date {
      font-size: 20px;
      font-weight: 400;
      text-align: center;
    }
  }

  .notice-item-content {
    padding: 32px 32px 32px 128px;
    background: var(--bg-color-tooltip);
    border-bottom: 1px solid #2d2a39;

    .wysiwyg {
      padding: 0;
      font-size: 20px;
      line-height: normal;
      color: var(--text-color-primary);

      img {
        max-width: 100%;
      }
    }
  }

  .notice-detail-wrap {
    .notice-detail-content {
      position: relative;
      padding: 32px;
      font-size: 24px;
      line-height: normal;
      color: #fff;
    }
  }
}
