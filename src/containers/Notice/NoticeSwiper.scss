.notice-container {
  width: 100%;
  height: 100vh;
  height: calc(100 * var(--vh, 1vh));

  .notice-header {
    @include flex-center;
    position: relative;
    padding: 48px 64px 16px;
    font-size: 40px;
    font-weight: 600;
    line-height: normal;
    color: #eff4f3;

    .popup-logo {
      display: block;
      height: 150px;
    }

    .popup-banner {
      position: absolute;
      right: 0;
      bottom: 0;
      display: block;
      height: 296px;
    }
  }

  .notice-content {
    width: 100%;
    height: 100%;
    padding: 20px 32px;
    overflow: hidden;
  }

  .notice-slide-content {
    font-size: 28px;
    line-height: 42px;
    color: #eff4f3;

    img {
      display: block;
      width: 100%;
    }
  }

  .notice-game-swiper {
    width: 100%;
    height: calc(100% - 320px) !important;
    overflow: hidden auto;
  }

  .notice-pagination {
    @include flex-center;
    padding: 20px 0;

    .swiper-pagination-bullet {
      width: 20px;
      height: 20px;
      margin: 0 10px;
      background: #9b5659;
      border-radius: 20px;
    }

    .swiper-pagination-bullet-active {
      background: #f7d462;
    }
  }

  .btn-confirm {
    @include flex-center;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 112px;
    font-size: 36px;
    line-height: 54px;
    color: #6b000c;
    text-align: center;
    background: linear-gradient(270deg, #ebab51 0%, #fbe38c 100%);
  }
}
