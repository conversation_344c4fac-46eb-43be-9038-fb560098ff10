import React from "react";
import { Icon } from "antd-mobile";
import dayjs from "dayjs";
import { inject, observer } from "mobx-react";
import Qs from "qs";
import { wysiwygUtil } from "tcg-mobile-common";

import Header from "@/components/OutterHeader/Header";
import deploy from "@/config/deploy.config";
import { get } from "@/utils/storage";

// import Item from "./NoticeItem";
import "./Notice.scss";

@inject("common")
@observer
class Notice extends React.Component {
  state = { activeKey: "", activeItem: {}, showDetail: false };
  componentDidMount() {
    window.scrollTo(0, 0);
    this.initData();
  }
  initData = () => {
    const { id } = Qs.parse(window.location.search, {
      ignoreQueryPrefix: true,
    });
    this.props.common.annount(this.getAnnountQuery()).then((res) => {
      if (location.pathname === "/m/noticeDetail" && id) {
        const promo = this.props.common.not.find((item) => {
          return item.id === Number(id);
        });
        this.setState({
          activeItem: promo || {},
          showDetail: true,
        });
      }
    });
  };
  getAnnountQuery() {
    const session = get("MC_SESSION_INFO");
    return {
      types: "B,PL,PU",
      groupName: "",
      platform: "M",
      merchantCode: deploy.merchant,
      token: session ? session.token : "",
    };
  }
  getContent = (content) => {
    const temp = document.createElement("div");
    temp.style.display = "none";
    temp.innerHTML = content;
    const convert = temp.textContent || temp.innerText;
    temp.remove();
    return convert;
  };
  handleRead = (id) => {
    const session = get("MC_SESSION_INFO");
    if (session && session.type !== 4) {
      this.props.common.annountMarkRead(id, this.getAnnountQuery());
    }
    this.checkDetail(id);
  };
  checkDetail = (id) => {
    this.props.push(`/m/noticeDetail?id=${id}`);
  };
  render() {
    return (
      <div className="notice-container app-main">
        <Header />
        <div className="notice-content">
          {this.state.showDetail ? (
            <div className="notice-detail-wrap">
              {/* <div className="notice-item-header">
                  <div className="notice-item-info">
                    <div className="notice-item-title">
                      <div className="item-title">{this.state.activeItem?.title}</div>
                      <div className="notice-item-date">
                        {dayjs(this.state.activeItem?.startDate).format("YYYY-MM-DD")}
                      </div>
                    </div>
                    <div className="notice-item-preview">{this.getContent(this.state.activeItem?.content)}</div>
                  </div>
                </div> */}
              <div className="notice-detail-content">
                <div
                  dangerouslySetInnerHTML={{
                    __html: wysiwygUtil.convertContent(this.state.activeItem?.content),
                  }}
                />
              </div>
            </div>
          ) : (
            <div className="notice-preview">
              {this.props.common.not.map((item, idx) => {
                return (
                  // <Item show={this.state.activeIndex === idx} key={`notice-${idx}`} data={item} onRead={this.handleRead} />
                  <div className="notice-item-header" onClick={() => this.handleRead(item.id)}>
                    <div className="notice-icon">
                      <Icon type={require("!svg-sprite-loader!@/assets/images/common/icon-notice.svg")} />
                    </div>
                    <div className="notice-item-info">
                      <div className="notice-item-title">
                        <div className="item-title">{item.title}</div>
                        <div className="notice-item-date">{dayjs(item.startDate).format("YYYY-MM-DD")}</div>
                      </div>
                      <div className="notice-item-preview">{this.getContent(item.content)}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    );
  }
}
export default Notice;
