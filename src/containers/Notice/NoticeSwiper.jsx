import React from "react";
import { withRouter } from "react-router-dom";
import { inject, observer } from "mobx-react";
import Qs from "qs";
import { wysiwygUtil } from "tcg-mobile-common";
import MC from "tcgmodulemc";

import GoBack from "@/components/GoBack/GoBack";
import deploy from "@/config/deploy.config";
import { get } from "@/utils/storage";

import "./NoticeSwiper.scss";

@inject("common", "languageShell")
@withRouter
@observer
class NoticeSwiper extends React.Component {
  state = { activeIndex: -1, activeKey: "", activeItem: {}, showDetail: false };
  componentDidMount() {
    this.initData();
    if (this.props.location.query) {
      this.setState({
        activeIndex: +this.props.location.query,
      });
    }
  }
  initData = () => {
    const { id } = Qs.parse(window.location.search, {
      ignoreQueryPrefix: true,
    });
    this.props.common.annount(this.getAnnountQuery()).then((res) => {
      if (location.pathname === "/m/noticeDetail" && id) {
        const promo = this.props.common.not.find((item) => {
          return item.id === Number(id);
        });
        this.setState({
          activeItem: promo || {},
          showDetail: true,
        });
      }
    });
    this.initSwiper();
  };
  getAnnountQuery() {
    const session = get("MC_SESSION_INFO");
    return {
      types: "B,PL,PU",
      groupName: "",
      platform: "M",
      merchantCode: deploy.merchant,
      token: session ? session.token : "",
    };
  }
  getContent = (content) => {
    const temp = document.createElement("div");
    temp.style.display = "none";
    temp.innerHTML = content;
    const convert = temp.textContent || temp.innerText;
    temp.remove();
    return convert;
  };
  handleRead = (id) => {
    const session = get("MC_SESSION_INFO");
    if (session && session.type !== 4) {
      this.props.common.annountMarkRead(id, this.getAnnountQuery());
    }
    // this.checkDetail(id);
  };
  checkDetail = (id) => {
    this.props.push(`/m/noticeDetail?id=${id}`);
  };
  initSwiper = () => {
    if (this.swiper) {
      this.swiper?.destroy(true, true);
    }
    this.swiper = new MC.Swiper(this.noticeSwiper, {
      observer: true,
      observeParents: true,
      pagination: {
        el: ".notice-pagination",
      },
    });
  };
  render() {
    return (
      <div className="notice-container app-main">
        <GoBack />
        <div className="notice-header">
          <span>{this.props.languageShell.t("in_increase_announcement")}</span>
        </div>
        <div className="notice-content">
          <div className="swiper-container  notice-game-swiper" ref={(c) => (this.noticeSwiper = c)}>
            <div className="notice-game-list swiper-wrapper">
              {this.props.common.not.map((item, idx) => {
                return (
                  <div className="notice-slide-content swiper-slide">
                    <div
                      dangerouslySetInnerHTML={{
                        __html: wysiwygUtil.convertContent(item?.content),
                      }}
                    />
                  </div>
                );
              })}
            </div>
          </div>
          <div className="notice-pagination" />

          {/* {this.props.showNav && (
            <div className="swiper-nav">
              <div className={`game-nav game-prev`} ref={(c) => (this.prevNav = c)}>
                <Icon type={require("!svg-sprite-loader!@/assets/images/common/arrow-left.svg")} />
              </div>
              <div className={`game-nav game-next`} ref={(c) => (this.nextNav = c)}>
                <Icon type={require("!svg-sprite-loader!@/assets/images/common/arrow-right.svg")} />
              </div>
            </div>
          )} */}
        </div>
        <div className="btn-confirm" onClick={() => this.props.history.replace("/m/home")}>
          <span>{this.props.languageShell.t("in_increase_submit")}</span>
        </div>
      </div>
    );
  }
}

NoticeSwiper.propTypes = {};

NoticeSwiper.defaultProps = {};

export default NoticeSwiper;
