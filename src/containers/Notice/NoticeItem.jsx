import React from "react";
import { Icon } from "antd-mobile";
import cz from "classnames";
import dayjs from "dayjs";
import PropTypes from "prop-types";
import { wysiwygUtil } from "tcg-mobile-common";

import { get } from "@/utils/storage";

class Item extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showContent: false,
    };
  }
  componentDidMount() {
    this.setState({
      showContent: this.props.show,
    });
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.show !== this.props.show) {
      this.setState({
        showContent: this.props.show,
      });
    }
  }
  show = () => {
    this.setState(({ showContent }) => {
      return { showContent: !showContent };
    });

    this.props.onRead(this.props.data.id);
  };
  getRead() {
    const session = get("MC_SESSION_INFO");
    if (session && session.type !== 4) {
      return this.props.data.read;
    }
    return true;
  }
  getContent(content) {
    const temp = document.createElement("div");
    temp.style.display = "none";
    temp.innerHTML = content;
    const convert = temp.textContent || temp.innerText;
    temp.remove();
    return convert;
  }
  render() {
    const data = this.props.data;
    const bubbleClass = cz("icon", { read: this.getRead(), unread: !this.getRead() });

    return (
      <div className={`notice-item ${this.state.showContent ? "on" : ""}`}>
        <div className="notice-item-header" onClick={this.show}>
          <div className="notice-icon">
            <Icon type={require("!svg-sprite-loader!@/assets/images/common/icon-notice.svg")} />
          </div>
          <div className="notice-item-info">
            <div className="notice-item-title">
              <div className="item-title">{data.title}</div>
              {/* <Icon
                className="accordion-arrow"
                type={require("!svg-sprite-loader!@/assets/images/common/arrow-right.svg")}
              /> */}
              <div className="notice-item-date">{dayjs(data.startDate).format("YYYY-MM-DD")}</div>
            </div>
            <div className="notice-item-preview">{this.getContent(data.content)}</div>
          </div>
        </div>
        <div
          className="notice-item-content"
          style={{ display: this.state.showContent ? "block" : "none" }}
          dangerouslySetInnerHTML={{ __html: wysiwygUtil.convertContent(data.content) }}
        />
      </div>
    );
  }
}

Item.propTypes = {
  data: PropTypes.shape().isRequired,
  onRead: PropTypes.func.isRequired,
  show: PropTypes.bool.isRequired,
};

export default Item;
