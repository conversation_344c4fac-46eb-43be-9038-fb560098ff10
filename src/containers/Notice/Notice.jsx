import React from "react";
import { Icon } from "antd-mobile";
import cz from "classnames";
import dayjs from "dayjs";
import { inject, observer } from "mobx-react";
import Qs from "qs";
import { wysiwygUtil } from "tcg-mobile-common";

import Header from "@/components/OutterHeader/Header";
import deploy from "@/config/deploy.config";
import { Accordion, AccordionItem } from "@/ui/Accordion";
import { get } from "@/utils/storage";

import "./Notice.scss";

@inject("common", "languageShell")
@observer
class Notice extends React.Component {
  state = { activeIndex: -1, activeKey: "", activeItem: {}, showDetail: false };
  componentDidMount() {
    window.scrollTo(0, 0);
    this.initData();
    if (this.props.location.query) {
      this.setState({
        activeIndex: +this.props.location.query,
        activeKey: `notice-${+this.props.location.query}`,
      });
    }
  }
  initData = () => {
    const { id } = Qs.parse(window.location.search, {
      ignoreQueryPrefix: true,
    });
    this.props.common.annount(this.getAnnountQuery()).then((res) => {
      if (location.pathname === "/m/noticeDetail" && id) {
        const promo = this.props.common.not.find((item) => {
          return item.id === Number(id);
        });
        this.setState({
          activeItem: promo || {},
          showDetail: true,
        });
      }
    });
  };
  getAnnountQuery() {
    const session = get("MC_SESSION_INFO");
    return {
      types: "B,PL,PU",
      groupName: "",
      platform: "M",
      merchantCode: deploy.merchant,
      token: session ? session.token : "",
    };
  }
  getContent = (content) => {
    const temp = document.createElement("div");
    temp.style.display = "none";
    temp.innerHTML = content;
    const convert = temp.textContent || temp.innerText;
    temp.remove();
    return convert;
  };
  handleRead = (id) => {
    const session = get("MC_SESSION_INFO");
    if (session && session.type !== 4) {
      this.props.common.annountMarkRead(id, this.getAnnountQuery());
    }
    // this.checkDetail(id);
  };
  checkDetail = (id) => {
    this.props.push(`/m/noticeDetail?id=${id}`);
  };
  getRead = (item) => {
    const session = get("MC_SESSION_INFO");
    if (session && session.type !== 4) {
      return item.read;
    }
    return true;
  };
  render() {
    return (
      <div className="notice-container app-main">
        <Header />

        <div className="notice-content">
          {this.state.showDetail ? (
            <div className="notice-detail-wrap">
              <div className={"notice-item-header"}>
                <div className="notice-item-info">
                  <div className="notice-item-title">
                    <div className="item-title">{this.state.activeItem?.title}</div>
                    <div className="notice-item-date">
                      {dayjs(this.state.activeItem?.startDate).format("YYYY-MM-DD")}
                    </div>
                  </div>
                  <div className="notice-detail-content">
                    <div
                      dangerouslySetInnerHTML={{
                        __html: wysiwygUtil.convertContent(this.state.activeItem?.content),
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="notice-preview">
              {/* {this.props.common.not.map((item, idx) => {
                return (
                  <Item
                    show={this.state.activeIndex === idx}
                    key={`notice-${idx}`}
                    data={item}
                    onRead={this.handleRead}
                    index={idx + 1}
                  />
                );
              })} */}
              <Accordion className="notice-accordion" activeKey={this.state.activeKey}>
                {this.props.common.not.map((ret, idx) => {
                  return (
                    <AccordionItem className="notice-item" data-key={`notice-${idx}`}>
                      <div
                        className={cz("notice-item-header", { unread: this.getRead(ret) })}
                        onClick={() => this.handleRead(ret.id)}
                      >
                        <div className="notice-icon">
                          <Icon type={require("!svg-sprite-loader!@/assets/images/home/<USER>")} />
                        </div>
                        <div className="notice-item-info">
                          <div className="notice-item-title">
                            <div className="item-title">{ret.title}</div>
                            <div className="item-date">{dayjs(ret.startDate).format("DD/MM/YYYY")}</div>
                          </div>

                          <div className="notice-item-preview">{this.getContent(ret.content)}</div>
                        </div>

                        {/* <div className="accordion-arrow">
                          <Icon type={require("!svg-sprite-loader!@/assets/images/common/arrow-right.svg")} />
                        </div> */}
                      </div>
                      <div
                        className="notice-item-content"
                        dangerouslySetInnerHTML={{ __html: wysiwygUtil.convertContent(ret.content) }}
                      />
                    </AccordionItem>
                  );
                })}
              </Accordion>
            </div>
          )}
        </div>
      </div>
    );
  }
}
export default Notice;
