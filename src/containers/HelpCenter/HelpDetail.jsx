import React from "react";
import { withRouter } from "react-router-dom";
import { isEqual } from "lodash";
import { inject, observer } from "mobx-react";
import Qs from "qs";
import { wysiwygUtil } from "tcg-mobile-common";

import Header from "@/components/OutterHeader/Header";

import "./HelpDetail.scss";

@inject("languageShell", "common")
@withRouter
@observer
class HelpDetail extends React.Component {
  get subActiveIndex() {
    return `${this.state.activeIndex}-${this.state.subIndex}`;
  }
  get helpList() {
    return this.props.common.helpList;
  }
  constructor(props) {
    super(props);
    this.state = {
      helpList: [],
      activeIndex: 0,
      activeItem: {},
    };
  }
  componentDidMount() {
    window.scrollTo(0, 0);
    if (this.props.common.helpList?.length > 0) {
      this.initData();
    }
  }
  componentDidUpdate(prevProps, prevState) {
    const prevHelp = prevProps.common.helpList || [];
    const currentHelp = this.props.common.helpList || [];

    if (!isEqual(prevHelp, currentHelp) && currentHelp.length > 0) {
      this.initData();
    }
  }
  initData() {
    const url = Qs.parse(window.location.search, {
      ignoreQueryPrefix: true,
    });
    if (this.helpList?.length > 0) {
      const { pathname } = this.props.history.location;
      let idx = 0;
      let sIdx = -1;
      let activeItem = null;
      if (url.tag) {
        idx = this.helpList.findIndex(({ tag }) => url.tag === tag);
        activeItem = this.helpList.find(({ tag }) => url.tag === tag);
      } else if (url.i) {
        idx = Number(url.i);
        activeItem = this.helpList[idx];
      }
      this.setState({
        activeIndex: idx > -1 ? idx : 0,
      });
      if (pathname === "/m/helpDetail" && activeItem) {
        this.setState({
          activeItem: activeItem,
          showDetail: true,
        });
      }
    }
  }
  menuClick = (e, index) => {
    e.target.scrollIntoView({ behavior: "smooth", block: "nearest", inline: "center" });
    this.setState({
      activeIndex: index,
    });
  };
  checkDetail = (index) => {
    return this.props.history.push(`/m/helpDetail?i=${index}`);
  };
  getContent = (content) => {
    const temp = document.createElement("div");
    temp.style.display = "none";
    temp.innerHTML = content;
    const convert = temp.textContent || temp.innerText;
    temp.remove();
    return convert;
  };
  render() {
    return (
      <div className="help-center-container app-main">
        <Header />
        {this.state.showDetail ? (
          <div className="help-detail-wrap">
            <div className="active-help-item">
              <div className="active-item-title">{this.state.activeItem?.title}</div>
              <div className="active-item-content">
                <div dangerouslySetInnerHTML={{ __html: wysiwygUtil.convertContent(this.state.activeItem?.content) }} />
              </div>
            </div>
          </div>
        ) : (
          <div className="help-view">
            <div className="help-view-content">
              {this.helpList?.map((item, idx) => {
                return (
                  <div className="help-item-header" key={`help-item-${idx}`} onClick={() => this.checkDetail(idx)}>
                    <div className="help-item-title">{item.title}</div>
                    {/* <div className="help-icon">
                    <Icon type={require("!svg-sprite-loader!@/assets/images/common/icon-help.svg")} />
                  </div> */}
                    <div className="help-item-content">
                      <div className="help-item-preview">{this.getContent(item.content)}</div>
                      <div className="read-more">
                        <span>{`${this.props.languageShell.t("read_more")} >`}</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    );
  }
}

export default HelpDetail;
