// .help-center-wrap {
//   .help-menu {
//     display: flex;
//     align-items: center;
//     width: 100%;
//     margin-bottom: 20px;
//     overflow-x: auto;

//     .menu-item {
//       position: relative;
//       flex-shrink: 0;
//       padding: 12px 16px;
//       font-size: 26px;
//       font-weight: 800;
//       line-height: normal;
//       color: #fff;
//       text-align: center;
//       white-space: nowrap;
//       background: #292d2e;
//       border-radius: 16px;

//       @include flex-center;

//       &.on {
//         color: #1e2121;
//         background: #f6cf50;
//         border-radius: 16px;
//       }

//       &:not(:last-child) {
//         margin-right: 30px;
//       }
//     }
//   }

//   .help-content-item {
//     display: none;

//     &.on {
//       display: block;
//     }
//   }

//   .help-child-item {
//     display: flex;
//     justify-content: space-between;
//     padding: 26px 30px;
//     border-bottom: 1px solid #545454;

//     .help-child-title {
//       font-size: 32px;
//       line-height: normal;
//       color: #fff;
//     }

//     .arrow-down {
//       width: 30px;
//       height: 30px;
//       transition: all 0.2s;
//     }
//   }

//   .accordion-active {
//     .help-child-item {
//       .arrow-down {
//         transform: rotate(-180deg);
//       }
//     }
//   }

//   .main-content {
//     font-size: 24px;
//     line-height: normal;
//     color: #eff4f3;

//     img {
//       display: block;
//       max-width: 100%;
//     }
//   }

//   .wysiwyg {
//     padding: 0;
//     font-size: 24px;
//     color: #fff;

//     img {
//       max-width: 100%;
//     }
//   }
// }
