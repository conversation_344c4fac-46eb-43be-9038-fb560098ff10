import React from "react";
import cz from "classnames";
import { isEqual } from "lodash";
import { inject, observer } from "mobx-react";
import Qs from "qs";
import { wysiwygUtil } from "tcg-mobile-common";

import Footer from "@/components/Footer/Footer";
import HomeHeader from "@/components/OutterHeader/HomeHeader";

import "./HelpCenter.scss";

@inject("languageShell", "common")
@observer
class HelpCenter extends React.Component {
  get helpList() {
    return this.props.common.helpList;
  }
  constructor(props) {
    super(props);
    this.state = {
      activeIndex: 0,
    };
    this.helpMenu = {};
  }
  componentDidMount() {
    window.scrollTo(0, 0);
    this.initData();
  }
  componentDidUpdate(prevProps, prevState) {
    const prevHelp = prevProps.common.helpList || [];
    const currentHelp = this.props.common.helpList || [];

    if (!isEqual(prevHelp, currentHelp) && currentHelp.length > 0) {
      this.initData();
    }
  }
  initData = () => {
    const url = Qs.parse(window.location.search, {
      ignoreQueryPrefix: true,
    });
    if (this.helpList?.length > 0) {
      let idx = 0;
      let sIdx = -1;
      if (url.tag) {
        idx = this.helpList.findIndex(({ tag }) => url.tag === tag);
      } else if (url.i) {
        idx = Number(url.i);
      } else if (url.c) {
        sIdx = Number(url.c);
      }
      this.setState(
        {
          activeIndex: idx > -1 ? idx : 0,
        },
        () => {
          this.moveToCenter(this.state.activeIndex);
        }
      );
    }
  };
  menuClick = (index) => {
    this.setState({
      activeIndex: index,
    });
    this.moveToCenter(index);
  };
  moveToCenter = (index) => {
    const el = this.helpMenu[index];
    if (el) {
      const containerWidth = this.menuRef.offsetWidth;
      const itemOffsetLeft = el.offsetLeft;
      const scrollPosition = itemOffsetLeft - containerWidth / 2 + el.offsetWidth / 2;
      setTimeout(() => {
        this.menuRef?.scrollTo({
          left: scrollPosition,
          behavior: "smooth",
        });
      }, 0);
    }
  };
  render() {
    return (
      <div className="help-container app-main avoid-footer">
        <HomeHeader />

        <div className="help-view">
          <div className="help-view-bg">
            <div className="help-menu hide-scrollbar" ref={(c) => (this.menuRef = c)}>
              {this.helpList.map((item, index) => {
                return (
                  <div
                    className={cz("menu-item", { on: this.state.activeIndex === index })}
                    ref={(e) => (this.helpMenu[index] = e)}
                  >
                    <span onClick={() => this.menuClick(index)}>{item.title}</span>
                  </div>
                );
              })}
            </div>
            <div className="help-content">
              {this.helpList.map((item, index) => {
                return (
                  <div
                    className={cz("help-item-content", { on: this.state.activeIndex === index })}
                    key={`content-item-${index}`}
                  >
                    {/* <div className="help-item-title">{item.title}</div> */}
                    <div className="main-content">
                      <div dangerouslySetInnerHTML={{ __html: wysiwygUtil.convertContent(item.content) }} />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }
}

export default HelpCenter;
