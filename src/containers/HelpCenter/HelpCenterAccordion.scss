.help-container {
  width: 100%;

  .help-content {
    display: flex;
    flex-direction: column;
    height: calc(100% - var(--mc-header-height));
    overflow: hidden;

    .help-content-bg {
      flex: 1;
      padding: 20px 40px;
      overflow: hidden auto;
    }

    .help-menu {
      flex-shrink: 0;
      width: 220px;
      height: 100%;
      padding: 0 16px;
      overflow-y: auto;

      .menu-item {
        width: 100%;
        height: 79px;
        padding: 20px;
        margin-bottom: 30px;
        font-size: 28px;
        color: #98835e;
        text-align: center;
        border: 1px solid rgb(152 131 94 / 50%);
        border-radius: 8px;

        @include flex-center;

        &.on {
          color: #342812;
          background: rgb(152 131 94 / 30%);
        }
      }
    }
  }

  .help-accordion {
    .accordion-item-bg {
      border-bottom: 1px solid #e3e3e3;
    }

    .accordion-active {
      .accordion-item-bg {
      }

      .accordion-arrow {
        transform: rotate(-90deg);
      }
    }

    .help-list {
      margin-bottom: 20px;
    }

    .help-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 77px;
    }

    .help-title {
      @include trim(1);
      flex: 1;
      font-size: 24px;
      line-height: normal;
      color: #171616;
    }

    .arrow-btn {
      width: 44px;
      height: 44px;
    }

    .accordion-arrow {
      @include flex-center;
      flex-shrink: 0;
      width: 32px;
      height: 32px;
      margin-left: 10px;
      transition: all 0.2s;

      .am-icon {
        display: block;
        width: 20px;
        height: 32px;
      }
    }

    .help-item-content {
      padding-bottom: 20px;
      font-size: 24px;
      line-height: normal;
      color: #666;
    }

    .wysiwyg {
      padding: 0;

      img {
        max-width: 100%;
      }
    }
  }
}
