import React from "react";
import { withRouter } from "react-router-dom";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";
import Qs from "qs";
import { wysiwygUtil } from "tcg-mobile-common";

import MessageNav from "@/components/MessageNav/MessageNav";
import NoData from "@/components/NoData/NoData";
import Header from "@/components/OutterHeader/Header";
import { Accordion, AccordionItem } from "@/ui/Accordion";

import "./HelpCenterAccordion.scss";

@inject("languageShell", "common")
@withRouter
@observer
class HelpCenter extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      activeKey: "",
      helpList: [],
      activeIndex: 0,
      subIndex: -1,
    };
  }
  componentDidMount() {
    window.scrollTo(0, 0);
    this.getHelp();
  }
  getHelp() {
    this.props.common
      .getHelpCenter({
        lang: this.props.languageShell.currentLanguage,
      })
      .then((res) => {
        const url = Qs.parse(window.location.search, {
          ignoreQueryPrefix: true,
        });
        if (res.length) {
          let idx = -1;
          let sIdx = -1;
          if (url.tag) {
            idx = res.findIndex(({ tag }) => url.tag === tag);
          } else if (url.i) {
            idx = Number(url.i);
          } else if (url.c) {
            sIdx = Number(url.c);
          }

          this.setState(
            {
              activeKey: idx > -1 ? `help-${idx}` : "",
              subIndex: sIdx,
            },
            () => {
              this.setState({
                helpList: res,
              });
            }
          );
        }
      });
  }
  renderHeader = (item) => {
    return (
      <div className="notice-item-header">
        <div className="notice-item-text">
          <span className="notice-item-title">{item.title}</span>
          <Icon className="notice-arrow" type={require("!svg-sprite-loader!@/assets/images/common/arrow-down.svg")} />
        </div>
      </div>
    );
  };
  render() {
    return (
      <div className="help-container app-main">
        <Header />
        <div className="help-content">
          <MessageNav />
          <div className="help-content-bg">
            {this.props.common.helpList.length > 0 ? (
              <Accordion className="help-accordion" activeKey={this.state.activeKey}>
                {this.props.common.helpList.map((ret, idx) => {
                  return (
                    <AccordionItem className="help-list" data-key={`help-${idx}`}>
                      <div className="help-item" key={ret.id}>
                        <div className="help-title">{ret.title}</div>
                        <div className="accordion-arrow">
                          <Icon type={require("!svg-sprite-loader!@/assets/images/common/arrow-right.svg")} />
                        </div>
                      </div>
                      <div className="help-item-content">
                        <div dangerouslySetInnerHTML={{ __html: wysiwygUtil.convertContent(ret.content) }} />
                      </div>
                    </AccordionItem>
                  );
                })}
              </Accordion>
            ) : (
              <NoData />
            )}
          </div>
        </div>
      </div>
    );
  }
}

export default HelpCenter;
