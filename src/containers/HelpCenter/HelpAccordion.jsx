import React from "react";
import cz from "classnames";
import PropTypes from "prop-types";
import { wysiwygUtil } from "tcg-mobile-common";

import "./HelpAccordion.scss";

class HelpAccordion extends React.Component {
  state = { activeIndex: 0 };
  componentDidMount() {
    if (this.props.activeIndex > 0) {
      this.setState({
        activeIndex: this.props.activeIndex,
      });
    }
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.activeIndex !== this.props.activeIndex) {
      this.setState({
        activeIndex: this.props.activeIndex,
      });
    }
  }
  menuClick = (e, index) => {
    e.target.scrollIntoView({ behavior: "smooth", block: "nearest", inline: "center" });
    this.setState({
      activeIndex: index,
    });
  };
  render() {
    return (
      <div className="help-center-wrap">
        <div className="help-menu hide-scrollbar">
          {this.props.helpList.map((item, index) => {
            return (
              <div className={cz("menu-item", { on: this.state.activeIndex === index })}>
                <span onClick={(e) => this.menuClick(e, index)}>{item.title}</span>
              </div>
            );
          })}
        </div>
        <div className="help-content">
          {this.props.helpList.map((item, index) => {
            return (
              <div
                className={cz("help-content-item", { on: this.state.activeIndex === index })}
                key={`content-item-${index}`}
              >
                <div className="main-content">
                  <div dangerouslySetInnerHTML={{ __html: wysiwygUtil.convertContent(item.content) }} />
                </div>
                {/* {item?.children?.length > 0 ? (
                  <Accordion className="help-accordion">
                    {item.children.map((ret, idx) => {
                      return (
                        <AccordionItem className="help-child-list" data-key={`help-child-${idx}`}>
                          <div className="help-child-item" key={ret.id}>
                            <div className="help-child-title">{ret.title}</div>
                            <Icon
                              className="arrow-down"
                              type={require("!svg-sprite-loader!@/assets/images/common/arrow-down.svg")}
                            />
                          </div>
                          <div className="activity-item-content">
                            <div dangerouslySetInnerHTML={{ __html: wysiwygUtil.convertContent(ret.content) }} />
                          </div>
                        </AccordionItem>
                      );
                    })}
                  </Accordion>
                ) : (
                  <div className="main-content">
                    <div dangerouslySetInnerHTML={{ __html: wysiwygUtil.convertContent(item.content) }} />
                  </div>
                )} */}
              </div>
            );
          })}
        </div>
      </div>
    );
  }
}

HelpAccordion.propTypes = {
  helpList: PropTypes.array.isRequired,
  activeIndex: PropTypes.number,
};

HelpAccordion.defaultProps = {
  helpList: [],
  activeIndex: 0,
};

export default HelpAccordion;
