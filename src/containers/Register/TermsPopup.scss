.common-popup-wrap.terms-popup-wrap {
  .popup-close {
    top: 23px;
    right: 23px;
    width: 24px;
    height: 24px;

    .icon-close {
      stroke: #1c6783;
    }
  }

  .terms-popup-content {
    width: 682px;
    max-height: calc(100 * var(--vh, 1vh) - 100px);
    padding: 30px 35px 45px;
    overflow-y: auto;
    background: #effbff;
    border: 3.409px solid #fff;
    border-radius: 25.568px;
    box-shadow: 0 0 18.239px 0 #8ac5da inset;

    .terms-title {
      margin-bottom: 12px;
      font-size: 27px;
      font-weight: 900;
      line-height: 131%;
      color: var(--text-color-third);
      text-align: center;
      text-transform: uppercase;
    }

    .terms-content {
      width: 100%;
      font-size: 22px;
      font-weight: 400;
      line-height: 123%;
    }
  }

  .close-terms {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: 35px;

    span {
      @include flex-center;
      width: 211px;
      height: 48px;
      font-size: 24px;
      font-weight: 500;
      line-height: normal;
      color: #fff;
      text-align: center;
    }
  }
}
