.form-page-container {
  // bday
  input[name="birthday"] {
    min-height: 1.2em;

    /* Solution 1 */
    appearance: textfield;

    &::-webkit-datetime-edit {
      display: block;
      padding: 0;
    }

    &::-webkit-inner-spin-button,
    &::-webkit-calendar-picker-indicator {
      display: none;
    }

    &::-webkit-datetime-edit-fields-wrapper {
      color: #707070;
    }

    &::-webkit-datetime-edit {
      display: inline-block;
      flex: 1;
      min-width: 0;
      overflow: hidden;
      -webkit-user-modify: read-only !important;
    }
  }
}
