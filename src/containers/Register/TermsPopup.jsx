import React from "react";
import { inject, observer } from "mobx-react";
import { wysiwygUtil } from "tcg-mobile-common";

import CloseIcon from "@/components/CloseIcon";
import CommonPopup from "@/ui/CommonPopup/CommonPopup";

import "./TermsPopup.scss";

@inject("common", "languageShell")
@observer
class TermsPopup extends React.Component {
  get regTerm() {
    return this.props.common.helpList.find((item) => item.tag === "terms") || this.props.common.helpList[0];
  }
  render() {
    return (
      <CommonPopup
        className="terms-popup-wrap"
        show={this.props.show}
        onClose={this.props.handleClose}
        closeIcon={<CloseIcon />}
      >
        <div className="terms-popup-content">
          <div className="terms-title">
            <span>{this.props.languageShell.t("user_terms")}</span>
          </div>
          <div
            className="terms-content"
            dangerouslySetInnerHTML={{ __html: wysiwygUtil.convertContent(this.regTerm?.content) }}
          />
          <div className="close-terms">
            <span onClick={this.props.handleClose}>{this.props.languageShell.t("i_understand")}</span>
          </div>
        </div>
      </CommonPopup>
    );
  }
}

TermsPopup.defaultProps = {
  show: false,
};

export default TermsPopup;
