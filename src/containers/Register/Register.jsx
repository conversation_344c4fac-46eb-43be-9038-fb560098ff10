import React from "react";
import { Date<PERSON>icker, Icon, Modal, Toast } from "antd-mobile";
import cz from "classnames";
import dayjs from "dayjs";
import { inject, observer } from "mobx-react";
import { createForm } from "rc-form";
import {
  AutoIcon,
  commonRegister,
  CountryCodeList,
  domainUtil,
  withCs,
  withFirebase,
  withGeetest,
} from "tcg-mobile-common";

import account from "@/apis/account";
import GoBack from "@/components/GoBack/GoBack";
import { formConfig } from "@/config/form.config";
import { inputScrollIntoView } from "@/utils/navigatorUtil";

import "./Register.scss";

const form_success = require("!svg-sprite-loader!@/assets/images/form/success.svg");
const form_error = require("!svg-sprite-loader!@/assets/images/form/error.svg");

@withGeetest
@withFirebase({ autoRegisterLogin: true })
@commonRegister
@createForm()
@withCs
@inject("common", "auth", "mcCommon", "mcMenu", "languageShell", "login", "tcgCommon")
@observer
class Register extends React.Component {
  get showForgetPass() {
    return this.props.mcMenu.whitelabelVal.CHNGPASSMAIL3 || this.props.mcMenu.whitelabelVal.CHNGPASSVERIF3;
  }
  get regTerm() {
    return this.props.common.helpList.find((item) => item.tag === "terms");
  }
  constructor(props) {
    super(props);
    this.alert = Modal.alert;
    this.handleRegister = this.handleRegister.bind(this);
    this.handleValidate = this.handleValidate.bind(this);
    this.handleConfirmPassword = this.handleConfirmPassword.bind(this);
    this.inputRefs = {};
    this.state = {
      disableSubmit: true,
      disableGetCode: true,
      affiliateState: true,
      domain: "",
      urlParams: {},
      countryDialingCode: "",
      regist_method: "username",
      USERNAMEREG3: true,
      MOBILENUMREG3: true,
      REGMOBVERF3: false,
      MOBILENUMPASSREG3: false,
      canSendTime: 60,
      loadingCaptcha: false,
      isAgree: true,
      showPass: false,
      showConfirmPass: false,
      showBirthdayPicker: false,
      checkDisclaimer: true,
      showDisclaimerError: false,
      affiliateStatus: false,
      loading: false,
      showTerms: false,
    };
  }
  componentDidMount() {
    window.scrollTo(0, 0);
    this.getConfig();
    this.generateDomainParams();
    this.handleCaptcha();
  }
  componentWillUnmount() {
    this.clearCountDown();
  }
  setCountryCode = (e) => {
    this.setState({
      countryDialingCode: e,
    });
  };
  clearCountDown() {
    this.setState({
      sendSuccess: false,
      canSendTime: 0,
    });
    clearTimeout(this.smsTimer);
  }
  getConfig() {
    const USERNAMEREG3 = this.props.mcMenu.whitelabelVal.USERNAMEREG3;
    const MOBILENUMREG3 = this.props.mcMenu.whitelabelVal.MOBILENUMREG3;
    const REGMOBVERF3 = this.props.mcMenu.whitelabelVal.REGMOBVERF3;
    const MOBILENUMPASSREG3 = this.props.mcMenu.whitelabelVal.MOBILENUMPASSREG3;
    this.setState({
      USERNAMEREG3: USERNAMEREG3,
      MOBILENUMREG3: MOBILENUMREG3,
      REGMOBVERF3: REGMOBVERF3,
      MOBILENUMPASSREG3: MOBILENUMPASSREG3,
      regist_method: MOBILENUMREG3 ? "mobileNum" : "username",
    });
  }
  handleCaptcha = () => {
    if (this.props.mcCommon.regExp.captcha && this.props.mcCommon.regExp.captcha.enabled) {
      this.setState({
        loadingCaptcha: true,
      });
      this.props.auth.captchas().finally((res) => {
        this.setState({
          loadingCaptcha: false,
        });
      });
      this.props.form.setFieldsValue({ identifying: "" });
    }
  };
  handleConfirmPassword = (rule, value, callback) => {
    const { getFieldValue } = this.props.form;
    if (value && value !== getFieldValue("password")) {
      callback(this.props.languageShell.resource["in_confirmation_correct"]);
      return;
    }
    callback();
  };
  handleRegister(e) {
    this.props.form.validateFields(async (error, value) => {
      if (error) {
        const [first] = Object.keys(error);
        this.props.form.getFieldInstance(first).focus();
        return;
      }
      if (!this.state.checkDisclaimer) {
        this.setState({ showDisclaimerError: true });
        return;
      }
      const params = this.dataParams(value);
      const firebase = this.props.usingFirebase && this.state.REGMOBVERF3;
      if (firebase) {
        const result = await this.props.verifyCodeByFirebase(params.verificationCode);
        if (!result) {
          return;
        }
        params.firebaseChannel = "sms";
        params.firebaseIdToken = result;
      }
      if (this.state.affiliateState) {
        this.register(params);
        return;
      }
      this.checkAffiliate({ code: value.affiliateCode, domain: value.domain }, params);
    });
  }
  checkAffiliate = (data, params) => {
    if (this.isRegEnabled("affiliateCode") && data.code) {
      this.props.common.affiliates(data).then((rs) => {
        if (!rs.data.success) {
          Toast.fail(this.props.languageShell.resource["invitation_code_error"], 1, () => {
            this.props.form.getFieldInstance("affiliateCode").focus();
          });
        } else {
          this.register(params);
        }
      });
    } else {
      this.register(params);
    }
  };
  isRegEnabled = (field) => {
    if (this.props.mcCommon.regExp) {
      const fieldObj = this.props.mcCommon.regExp[field] || {};
      return fieldObj.enabled;
    }
    return true;
  };

  register = (data) => {
    this.props
      .registerAction({
        data,
        normalRegister: this.state.regist_method !== "mobileNum",
        message: this.props.languageShell.t("in_registered_successfully"),
        geetest: true,
      })
      .then(() => {
        this.props.common.showRegister(false);
      });
  };
  affiliates(data) {
    this.props.common.affiliates(data).then((rs) => {
      this.setState({ affiliateState: rs.data.success });
    });
  }
  dataParams(value) {
    let registerMethod = /AppVer|AppShellVer/.test(navigator.userAgent) ? "APP" : "H5";
    if ("standalone" in navigator && navigator.standalone) {
      registerMethod = window.localStorage.getItem("WEBCLIP") ? "WEBCLIP" : "PWA";
    }
    return {
      countryDialingCode: this.state.countryDialingCode,
      registerUrl: window.location.hostname,
      registerMethod,
      username: value.username,
      password: value.password,
      confirmPassword: value.password,
      affiliateCode: value.affiliateCode || this.state.urlParams.code,
      paymentPassword: value.paymentPassword ? value.paymentPassword : "",
      recommenderId: value.recommenderId ? value.recommenderId : "",
      nickname: value.nickname ? value.nickname : "",
      payeeName: value.payeeName ? value.payeeName : "",
      email: value.email ? value.email : "",
      mobileNum: value.mobileNum1 || value.mobileNum2 ? (value.mobileNum1 ? value.mobileNum1 : value.mobileNum2) : "",
      idNumber: value.idNumber ? value.idNumber : "",
      qqNum: value.qqNum ? value.qqNum : "",
      facebook: value.facebook ? value.facebook : "",
      wechat: value.wechat ? value.wechat : "",
      line: value.line ? value.line : "",
      whatsapp: value.whatsapp ? value.whatsapp : "",
      zalo: value.zalo ? value.zalo : "",
      birthday: value.birthday || "",
      captcha: value.identifying ? value.identifying : "",
      type: 0,
      domain: this.state.urlParams.domain ? this.state.urlParams.domain : "",
      verificationCode:
        value.messageCode1 || value.messageCode2 ? (value.messageCode1 ? value.messageCode1 : value.messageCode2) : "",
    };
  }
  generateDomainParams() {
    const data = domainUtil.getAffiliate();
    this.setState({ urlParams: data });
    this.affiliates(data);
  }
  handleValidate = (field) => (rule, value, callback) => {
    const setting = this.props.mcCommon.regExp[field];
    if (setting && !setting.required && !value) {
      callback();
      return;
    }
    const result = this.props.mcCommon.verification(value || "", field);
    if (result.pass) {
      if (field === "password") {
        const { getFieldValue } = this.props.form;
        const curPass = getFieldValue("password");
        const curConfirmPass = getFieldValue("confimpsw");
        if (curPass && curConfirmPass && curPass !== curConfirmPass) {
          callback(this.props.languageShell.resource["in_confirmation_correct"]);
          return;
        }
      }
      callback();
      return;
    }
    callback([new Error(result.message)]);
  };
  showAffiliateCode() {
    let register = this.props.mcCommon.regExp || {};
    const { affiliateCode } = register;
    return affiliateCode.enabled && !this.state.affiliateState;
  }
  getVerificationCode = async () => {
    this.props.form.validateFields(["mobileNum1", "mobileNum2"], {}, async (error, value) => {
      if (error) {
        return true;
      }
      Toast.loading(`${this.props.languageShell.resource["in_sms_sending"]}...`, 9999);
      const geetest = await this.props.geetestVerify("sms");
      const data = {
        mobileNo: value.mobileNum1 ? value.mobileNum1 : value.mobileNum2,
        countryDialingCode: this.state.countryDialingCode,
        ...geetest,
      };
      const rs = await account.sendVerificationCode(data);
      if (this.props.usingFirebase) {
        try {
          const result = await this.props.sendCodeByFirebase(data.countryDialingCode, data.mobileNo);
          if (result) {
            Toast.hide();
            this.countDownSms(rs.data.value.expirationTime);
            Toast.success(this.props.languageShell.t("in_sms_send"), 1.5);
          }
        } catch (e) {
          Toast.hide();
        }
      } else {
        Toast.hide();
        this.countDownSms(rs.data.value.expirationTime);
        Toast.success(this.props.languageShell.t("in_sms_send"), 1.5);
      }
    });
  };
  countDownSms = (expirationTime) => {
    this.smsTimer = setTimeout(() => {
      const count = Math.floor((expirationTime - Date.now()) / 1000);
      if (count <= 0) {
        clearTimeout(this.smsTimer);
        this.setState({
          sendSuccess: false,
          canSendTime: 0,
        });
      } else {
        this.setState({
          sendSuccess: true,
          canSendTime: count,
        });
        this.countDownSms(expirationTime);
      }
    }, 500);
  };
  handleMobileNumChange = (e) => {
    const mobileNum = e.target.value || "";
    this.setState({ mobileNum });
  };
  handleTabChange = (newVal) => {
    // const newVal = this.state.regist_method === "username" ? "mobileNum" : "username";
    this.setState({ regist_method: newVal });
    setTimeout(() => {
      if (newVal) {
        this.props.form.setFieldsValue({ mobileNum1: this.state.mobileNum });
      } else {
        this.props.form.setFieldsValue({ mobileNum2: this.state.mobileNum });
      }
    }, 150);
  };
  getInputIcon(field) {
    const { getFieldError, getFieldValue } = this.props.form;
    let errors = getFieldError(field);
    let value = getFieldValue(field);
    if (!value) {
      return null;
    }
    return <Icon className="input_status" type={errors ? form_error : form_success} />;
  }
  getRequired = (key) => {
    if (this.props.mcCommon.regExp[key] && !this.props.mcCommon.regExp[key].required) {
      return <span className="field-optional">{`（${this.props.languageShell.t("field_optional")}）`}</span>;
    }
    return "";
  };
  requiredDot(key) {
    if (this.props.mcCommon.regExp[key] && this.props.mcCommon.regExp[key].required) {
      return "*";
    }
    return "";
  }
  inputFocus = (e) => {
    inputScrollIntoView(e.target);
  };
  toggleState = (name) => {
    this.setState({
      [name]: !this.state[name],
    });
  };
  handleDisclaimer = () => {
    this.setState({ checkDisclaimer: !this.state.checkDisclaimer, showDisclaimerError: false });
  };
  resetForm = () => {
    // this.props.form.validateFields();
    const result = this.props.form.getFieldsValue();
    Object.keys(result).forEach((item) => {
      this.props.form.setFieldsValue({ [item]: "" });
    });
  };
  handleFirebaseLogin = (provider) => (e) => {
    this.props.firebaseLogin(provider);
  };
  showSocialMediaLogin() {
    return (
      this.props.firebaseInit &&
      (this.props.mcMenu.whitelabelVal["FACEBOOKLOGIN3"] ||
        this.props.mcMenu.whitelabelVal["GOOGLELOGIN3"] ||
        this.props.mcMenu.whitelabelVal["LINELOGIN3"])
    );
  }
  handelChange = (value) => {
    this.setState({
      isAgree: value,
    });
  };
  removeValue = (key) => {
    this.props.form.setFieldsValue({
      [key]: "",
    });
  };
  getPatternMessage = (key) => {
    const myRegexp = this.props.mcCommon.regExp[key];
    return this.props.mcLanguage
      .t(`reg_pattern_${myRegexp.patternId}`)
      .replace("(min)", myRegexp.minLength)
      .replace("(max)", myRegexp.maxLength);
  };
  handleClear = (name) => {
    const { setFieldsValue } = this.props.form;
    setFieldsValue({ [name]: "" });
    this.inputRefs[name]?.focus();
  };
  handleInputChange = (fieldName, value) => {
    const { setFieldsValue, validateFields, getFieldValue } = this.props.form;

    setFieldsValue({ [fieldName]: value });

    if (fieldName === "password" && getFieldValue("confimpsw")) {
      validateFields(["confimpsw"], { force: true });
    }

    // validateFields((errors, values) => {
    //   this.setState({
    //     disableSubmit: !!errors,
    //   });
    // });
    // this.props.form.validateFields(["mobileNum1", "mobileNum2"], {}, async (error, value) => {
    //   this.setState({
    //     disableGetCode: !!error,
    //   });
    // });
  };
  getTerms = () => {
    const terms = `<span class="term-link">${this.props.languageShell.t("terms_service")}</span>`;
    return this.props.languageShell.t("register_term").format(terms);
  };
  handlePolicy = (e) => {
    const name = e.target.className;
    if (name === "term-link") {
      this.toggleTerms();
      // this.props.push(`/m/help?tag=terms`);
    }
  };
  toggleTerms = () => {
    // if (!this.regTerm) {
    //   return null;
    // }
    this.setState({
      showTerms: !this.state.showTerms,
    });
  };

  render() {
    let InputData = this.props.mcCommon.regExp;
    const { getFieldProps, getFieldError, getFieldValue } = this.props.form;
    const { showDisclaimerError } = this.state;
    const openTwoMethod = this.state.MOBILENUMREG3 && this.state.USERNAMEREG3;
    return InputData ? (
      <div className="form-page-container register-container app-main">
        {/* <TermsPopup show={this.state.showTerms} handleClose={this.toggleTerms} /> */}
        <GoBack type="left" />
        <div className="form-wrap">
          <form className="form-content" autoComplete="off">
            <div className="form-title">
              <h6>{this.props.languageShell.t("hd_reg_button")}</h6>
            </div>
            {openTwoMethod && (
              <div className="method-select">
                <div className="select-wrap">
                  <div
                    className={cz("method-toggle", { on: this.state.regist_method === "username" })}
                    onClick={() => this.handleTabChange("username")}
                  >
                    <span>{this.props.languageShell.t("in_increase_username")}</span>
                  </div>
                  <div
                    className={cz("method-toggle", { on: this.state.regist_method === "mobileNum" })}
                    onClick={() => this.handleTabChange("mobileNum")}
                  >
                    <span>{this.props.languageShell.t("in_increase_mobile_placeholder")}</span>
                  </div>
                </div>
              </div>
            )}
            <div className="outter-form">
              <div className="form-group-wrap">
                {this.state.regist_method === "mobileNum" ? (
                  <div
                    className={cz("form-group form-mobileNum", {
                      form_error: getFieldError("mobileNum2"),
                      form_success: getFieldValue("mobileNum2") && !getFieldError("mobileNum2"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("in_increase_mobileNum")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("mobileNum")}</span> */}
                    <div className="fixed-list">
                      <div className="mobile-box">
                        <CountryCodeList
                          show={this.props.showRegisterDialingCode}
                          setDialingCode={this.setCountryCode}
                        />
                        <input
                          type="text"
                          autoComplete="off"
                          className="form-control form-mobileNum"
                          name="mobileNum2"
                          placeholder={this.props.languageShell.t("in_increase_mobileNum")}
                          inputMode="tel"
                          ref={(c) => {
                            this.mobileNum2 = c;
                          }}
                          {...getFieldProps("mobileNum2", {
                            onChange: (e) => {
                              this.handleMobileNumChange(e);
                              const value = e.target.value;
                              this.handleInputChange("mobileNum2", value);
                            },
                            onFocus: this.inputFocus,
                            rules: [{ validator: this.handleValidate("mobileNum") }],
                          })}
                        />
                        <i className="input-icon">
                          <AutoIcon icon={formConfig.mobileNum?.icon} />
                        </i>

                        {/* <span className={`right-icon remove-btn`} onClick={() => this.removeValue("mobileNum2")}>
                          <Icon type={require("!svg-sprite-loader!@/assets/images/form/remove.svg")} />
                        </span> */}
                      </div>
                      {/* <Icon className="input_status" type={getFieldError("mobileNum2") ? form_error : form_success} /> */}
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("mobileNum2")?.join(",")}</p>
                    </div>
                  </div>
                ) : (
                  ""
                )}

                {this.state.regist_method === "mobileNum" ? (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("messageCode1"),
                      form_success: getFieldValue("messageCode1") && !getFieldError("messageCode1"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("in_sms_code")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("verificationCode")}</span> */}
                    <div className="fixed-list messageCode">
                      <input
                        type="text"
                        className="form-control message-code"
                        name="messageCode1"
                        placeholder={this.props.languageShell.t("in_sms_code")}
                        pattern="[0-9]*"
                        autoComplete="one-time-code"
                        inputMode="numeric"
                        maxLength="10"
                        ref={(c) => {
                          this.messageCode1 = c;
                        }}
                        {...getFieldProps("messageCode1", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("messageCode1", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("verificationCode") }],
                        })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.messageCode?.icon} />
                      </i>
                      {this.state.sendSuccess === true ? (
                        <span className="get-code monospace-shell">{this.state.canSendTime}s</span>
                      ) : (
                        <button
                          className="get-code"
                          type="button"
                          // disabled={this.state.disableGetCode}
                          onClick={() => this.getVerificationCode()}
                        >
                          {this.props.languageShell.t("in_fetch_verificationCode")}
                        </button>
                      )}
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("messageCode1")?.join(",")}</p>
                    </div>
                  </div>
                ) : (
                  ""
                )}

                {this.state.regist_method === "username" ? (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("username"),
                      form_success: getFieldValue("username") && !getFieldError("username"),
                    })}
                  >
                    {/* <div className="form-label">{this.props.languageShell.t("in_increase_username")}</div> */}
                    {/* <span className="input-required">{this.requiredDot("username")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className="form-control"
                        name="username"
                        placeholder={this.props.languageShell.t("in_increase_username")}
                        ref={(c) => {
                          this.username = c;
                        }}
                        {...getFieldProps("username", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("username", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("username") }],
                          ref: (c) => {
                            this.inputRefs["username"] = c;
                          },
                        })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.username?.icon} />
                      </i>
                      {/* {getFieldValue("username") && (
                        <div className="clear-field" onClick={() => this.handleClear("username")}>
                          <Icon type={require("!svg-sprite-loader!@/assets/images/common/btn-close.svg")} />
                        </div>
                      )} */}
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("username")?.join(",")}</p>
                    </div>
                  </div>
                ) : (
                  ""
                )}

                {(this.state.regist_method === "mobileNum" &&
                  this.state.MOBILENUMREG3 &&
                  this.state.MOBILENUMPASSREG3) ||
                this.state.regist_method === "username"
                  ? [
                      <div
                        className={cz("form-group", {
                          form_error: getFieldError("password"),
                          form_success: getFieldValue("password") && !getFieldError("password"),
                        })}
                      >
                        {/* <div className="form-label">{this.props.languageShell.t("in_increase_password")}</div> */}
                        {/* <span className="input-required">{this.requiredDot("password")}</span> */}
                        <div className="fixed-list">
                          <input
                            type={this.state.showPass ? "text" : "password"}
                            autoComplete="off"
                            className="form-control"
                            name="password"
                            placeholder={this.props.languageShell.t("in_increase_password")}
                            ref={(c) => {
                              this.password = c;
                            }}
                            {...getFieldProps("password", {
                              onChange: (e) => {
                                const value = e.target.value;
                                this.handleInputChange("password", value);
                              },
                              onFocus: this.inputFocus,
                              rules: [{ validator: this.handleValidate("password") }],
                            })}
                          />
                          <i className="input-icon">
                            <AutoIcon icon={formConfig.password?.icon} />
                          </i>
                          <span
                            className={`right-icon ${this.state.showPass ? "on" : "off"}`}
                            onClick={() => this.toggleState("showPass")}
                          >
                            <Icon
                              type={
                                this.state.showPass
                                  ? require("!svg-sprite-loader!@/assets/images/form/eye-open.svg")
                                  : require("!svg-sprite-loader!@/assets/images/form/eye-closed.svg")
                              }
                            />
                          </span>
                        </div>
                        <div className="panel panel-default">
                          <p>{getFieldError("password")?.join(",")}</p>
                        </div>
                      </div>,
                      <div
                        className={cz("form-group", {
                          form_error: getFieldError("confimpsw"),
                          form_success: getFieldValue("confimpsw") && !getFieldError("confimpsw"),
                        })}
                      >
                        {/* <div className="form-label">{this.props.languageShell.t("in_increase_verifyPwd")}</div> */}
                        {/* <span className="input-required">{this.requiredDot("password")}</span> */}
                        <div className="fixed-list">
                          <input
                            type={this.state.showConfirmPass ? "text" : "password"}
                            className="form-control"
                            autoComplete="off"
                            name="confimpsw"
                            placeholder={this.props.languageShell.t("in_increase_verifyPwd")}
                            ref={(c) => {
                              this.confimpsw = c;
                            }}
                            {...getFieldProps("confimpsw", {
                              onChange: (e) => {
                                const value = e.target.value;
                                this.handleInputChange("confimpsw", value);
                              },
                              onFocus: this.inputFocus,
                              rules: [
                                {
                                  required: true,
                                  message: this.props.languageShell.resource["in_incorrect_password"],
                                },
                                { validator: this.handleConfirmPassword },
                              ],
                            })}
                          />
                          <i className="input-icon">
                            <AutoIcon icon={formConfig.password?.icon} />
                          </i>
                          <span
                            className={`right-icon ${this.state.showConfirmPass ? "on" : "off"}`}
                            onClick={() => this.toggleState("showConfirmPass")}
                          >
                            <Icon
                              type={
                                this.state.showConfirmPass
                                  ? require("!svg-sprite-loader!@/assets/images/form/eye-open.svg")
                                  : require("!svg-sprite-loader!@/assets/images/form/eye-closed.svg")
                              }
                            />
                          </span>
                        </div>
                        <div className="panel panel-default">
                          <p>{getFieldError("confimpsw")?.join(",")}</p>
                        </div>
                      </div>,
                    ]
                  : ""}
                {InputData.paymentPassword !== undefined && InputData.paymentPassword.enabled ? (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("paymentPassword"),
                      form_success: getFieldValue("paymentPassword") && !getFieldError("paymentPassword"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("in_increase_paymentPassword")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("paymentPassword")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="password"
                        autoComplete="off"
                        className="form-control"
                        name="paymentPassword"
                        placeholder={this.props.languageShell.t("in_increase_paymentPassword")}
                        ref={(c) => {
                          this.paymentPassword = c;
                        }}
                        {...getFieldProps("paymentPassword", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("paymentPassword", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("paymentPassword") }],
                        })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.password?.icon} />
                      </i>
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("paymentPassword")?.join(",")}</p>
                    </div>
                  </div>
                ) : (
                  ""
                )}
                {InputData.idNumber.enabled && (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("idNumber"),
                      form_success: getFieldValue("idNumber") && !getFieldError("idNumber"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("reg_id_number")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("idNumber")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className="form-control"
                        name="idNumber"
                        placeholder={this.props.languageShell.t("reg_id_number")}
                        ref={(c) => {
                          this.idNumber = c;
                        }}
                        {...getFieldProps("idNumber", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("idNumber", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("idNumber") }],
                        })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.idNumber?.icon} />
                      </i>
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("idNumber")?.join(",")}</p>
                    </div>
                  </div>
                )}
                {InputData.qqNum !== undefined && InputData.qqNum.enabled ? (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("qqNum"),
                      form_success: getFieldValue("qqNum") && !getFieldError("qqNum"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("in_increase_qqNum")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("qqNum")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className="form-control"
                        name="qqNum"
                        placeholder={this.props.languageShell.t("in_increase_qqNum")}
                        ref={(c) => {
                          this.qqNum = c;
                        }}
                        {...getFieldProps("qqNum", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("qqNum", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("qqNum") }],
                        })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.qqNum?.icon} />
                      </i>
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("qqNum")?.join(",")}</p>
                    </div>
                  </div>
                ) : (
                  ""
                )}

                {InputData.facebook.enabled && (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("facebook"),
                      form_success: getFieldValue("facebook") && !getFieldError("facebook"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("reg_facebook")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("facebook")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className="form-control"
                        name="facebook"
                        placeholder={this.props.languageShell.t("reg_facebook")}
                        ref={(c) => {
                          this.facebook = c;
                        }}
                        {...getFieldProps("facebook", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("facebook", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("facebook") }],
                        })}
                      />
                    </div>
                    <i className="input-icon">
                      <AutoIcon icon={formConfig.facebook?.icon} />
                    </i>
                    <div className="panel panel-default">
                      <p>{getFieldError("facebook")?.join(",")}</p>
                    </div>
                  </div>
                )}
                {InputData.wechat.enabled && (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("wechat"),
                      form_success: getFieldValue("wechat") && !getFieldError("wechat"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("in_increase_wechat")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("wechat")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className="form-control"
                        name="wechat"
                        placeholder={this.props.languageShell.t("in_increase_wechat")}
                        ref={(c) => {
                          this.wechat = c;
                        }}
                        {...getFieldProps("wechat", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("wechat", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("wechat") }],
                        })}
                      />
                    </div>
                    <i className="input-icon">
                      <AutoIcon icon={formConfig.wechat?.icon} />
                    </i>
                    <div className="panel panel-default">
                      <p>{getFieldError("wechat")?.join(",")}</p>
                    </div>
                  </div>
                )}
                {InputData.line.enabled && (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("line"),
                      form_success: getFieldValue("line") && !getFieldError("line"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("reg_line")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("line")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className="form-control"
                        name="line"
                        placeholder={this.props.languageShell.t("reg_line")}
                        ref={(c) => {
                          this.line = c;
                        }}
                        {...getFieldProps("line", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("line", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("line") }],
                        })}
                      />
                    </div>
                    <i className="input-icon">
                      <AutoIcon icon={formConfig.line?.icon} />
                    </i>
                    <div className="panel panel-default">
                      <p>{getFieldError("line")?.join(",")}</p>
                    </div>
                  </div>
                )}
                {InputData.whatsapp.enabled && (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("whatsapp"),
                      form_success: getFieldValue("whatsapp") && !getFieldError("whatsapp"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("reg_whatsapp")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("whatsapp")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className="form-control"
                        name="whatsapp"
                        placeholder={this.props.languageShell.t("reg_whatsapp")}
                        ref={(c) => {
                          this.whatsapp = c;
                        }}
                        {...getFieldProps("whatsapp", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("whatsapp", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("whatsapp") }],
                        })}
                      />
                    </div>
                    <i className="input-icon">
                      <AutoIcon icon={formConfig.whatsapp?.icon} />
                    </i>
                    <div className="panel panel-default">
                      <p>{getFieldError("whatsapp")?.join(",")}</p>
                    </div>
                  </div>
                )}
                {InputData.zalo.enabled && (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("zalo"),
                      form_success: getFieldValue("zalo") && !getFieldError("zalo"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("reg_zalo_placeh")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("zalo")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className="form-control"
                        name="zalo"
                        placeholder={this.props.languageShell.t("reg_zalo_placeh")}
                        ref={(c) => {
                          this.zalo = c;
                        }}
                        {...getFieldProps("zalo", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("zalo", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("zalo") }],
                        })}
                      />
                    </div>
                    <i className="input-icon">
                      <AutoIcon icon={formConfig.zalo?.icon} />
                    </i>
                    <div className="panel panel-default">
                      <p>{getFieldError("zalo")?.join(",")}</p>
                    </div>
                  </div>
                )}
                {InputData.email !== undefined && InputData.email.enabled ? (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("email"),
                      form_success: getFieldValue("email") && !getFieldError("email"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("in_increase_email")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("email")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className="form-control"
                        name="email"
                        placeholder={this.props.languageShell.t("in_increase_email")}
                        ref={(c) => {
                          this.email = c;
                        }}
                        {...getFieldProps("email", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("email", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("email") }],
                        })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.email?.icon} />
                      </i>
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("email")?.join(",")}</p>
                    </div>
                  </div>
                ) : (
                  ""
                )}
                {InputData.nickname !== undefined && InputData.nickname.enabled ? (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("nickname"),
                      form_success: getFieldValue("nickname") && !getFieldError("nickname"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("in_increase_nickname")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("nickname")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className="form-control"
                        name="nickname"
                        placeholder={this.props.languageShell.t("in_increase_nickname")}
                        ref={(c) => {
                          this.nickname = c;
                        }}
                        {...getFieldProps("nickname", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("nickname", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("nickname") }],
                        })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.nickname?.icon} />
                      </i>
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("nickname")?.join(",")}</p>
                    </div>
                  </div>
                ) : (
                  ""
                )}
                {InputData.payeeName !== undefined && InputData.payeeName.enabled ? (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("payeeName"),
                      form_success: getFieldValue("payeeName") && !getFieldError("payeeName"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("in_increase_payeeName")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("payeeName")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className="form-control"
                        name="payeeName"
                        placeholder={this.props.languageShell.t("in_increase_payeeName")}
                        ref={(c) => {
                          this.payeeName = c;
                        }}
                        {...getFieldProps("payeeName", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("payeeName", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("payeeName") }],
                        })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.payeeName?.icon} />
                      </i>
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("payeeName")?.join(",")}</p>
                    </div>
                  </div>
                ) : (
                  ""
                )}
                {InputData.mobileNum !== undefined &&
                InputData.mobileNum.enabled &&
                this.state.regist_method === "username" ? (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("mobileNum1"),
                      form_success: getFieldValue("mobileNum1") && !getFieldError("mobileNum1"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("in_increase_mobileNum")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("mobileNum")}</span> */}
                    <div className="fixed-list">
                      <div className="mobile-box">
                        <CountryCodeList
                          show={this.props.showRegisterDialingCode}
                          setDialingCode={this.setCountryCode}
                        />

                        <input
                          type="text"
                          autoComplete="off"
                          className="form-control form-mobileNum"
                          name="mobileNum1"
                          placeholder={this.props.languageShell.t("in_increase_mobileNum")}
                          inputMode="tel"
                          ref={(c) => {
                            this.mobileNum1 = c;
                          }}
                          {...getFieldProps("mobileNum1", {
                            onChange: (e) => {
                              const value = e.target.value;
                              this.handleInputChange("mobileNum1", value);
                              this.handleMobileNumChange(e);
                            },
                            onFocus: this.inputFocus,
                            rules: [{ validator: this.handleValidate("mobileNum") }],
                          })}
                        />
                        <i className="input-icon">
                          <AutoIcon icon={formConfig.mobileNum?.icon} />
                        </i>
                      </div>
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("mobileNum1")?.join(",")}</p>
                    </div>
                  </div>
                ) : (
                  ""
                )}
                {this.state.REGMOBVERF3 && this.state.regist_method === "username" ? (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("messageCode2"),
                      form_success: getFieldValue("messageCode2") && !getFieldError("messageCode2"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("in_sms_code")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("verificationCode")}</span> */}
                    <div className="fixed-list messageCode">
                      <input
                        type="text"
                        className={`form-control message-code`}
                        name="messageCode2"
                        placeholder={this.props.languageShell.t("in_sms_code")}
                        pattern="[0-9]*"
                        autoComplete="one-time-code"
                        inputMode="numeric"
                        maxLength="10"
                        ref={(c) => {
                          this.messageCode2 = c;
                        }}
                        {...getFieldProps("messageCode2", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("messageCode2", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("verificationCode") }],
                        })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.messageCode?.icon} />
                      </i>

                      {this.state.sendSuccess === true ? (
                        <span className="get-code monospace-shell">{this.state.canSendTime}s</span>
                      ) : (
                        <span className="get-code" onClick={() => this.getVerificationCode()}>
                          {this.props.languageShell.t("in_fetch_verificationCode")}
                        </span>
                      )}
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("messageCode2")?.join(",")}</p>
                    </div>
                  </div>
                ) : (
                  ""
                )}
                {InputData.recommenderId !== undefined && InputData.recommenderId.enabled ? (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("recommenderId"),
                      form_success: getFieldValue("recommenderId") && !getFieldError("recommenderId"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("in_increase_recommenderId")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("recommenderId")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className={`form-control`}
                        name="recommenderId"
                        placeholder={this.props.languageShell.t("in_increase_recommenderId")}
                        ref={(c) => {
                          this.recommenderId = c;
                        }}
                        {...getFieldProps("recommenderId", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("recommenderId", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("recommenderId") }],
                        })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.recommenderId?.icon} />
                      </i>
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("recommenderId")?.join(",")}</p>
                    </div>
                  </div>
                ) : (
                  ""
                )}

                {InputData.birthday?.enabled && (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("birthday"),
                      form_success: getFieldValue("birthday") && !getFieldError("birthday"),
                    })}
                  >
                    {/* <div className="form-label">
                        <span>{this.props.languageShell.t("in_increase_recommenderId")}</span>
                      </div> */}
                    {/* <span className="input-required">{this.requiredDot("in_increase_birthday")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className={`form-control`}
                        name="birthday"
                        value={this.state.birthday}
                        placeholder={this.props.languageShell.t("in_increase_birthday")}
                        {...getFieldProps("birthday", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("birthday", value);
                          },
                          rules: [{ validator: this.handleValidate("birthday") }],
                        })}
                        onClick={() => this.setState({ showBirthdayPicker: true })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.birthday?.icon} />
                      </i>
                      <div className="panel panel-default">
                        <p>{getFieldError("birthday")?.join(",")}</p>
                      </div>
                      <DatePicker
                        title={this.props.languageShell.t("in_increase_birthday")}
                        visible={this.state.showBirthdayPicker}
                        // defaultDate={new Date()}
                        value={
                          this.props.form.getFieldValue("birthday")
                            ? dayjs(this.props.form.getFieldValue("birthday")).startOf("date").toDate()
                            : new Date()
                        }
                        minDate={new Date(1900, 0, 0)}
                        maxDate={dayjs(new Date()).endOf("year").toDate()}
                        mode="date"
                        onChange={(date) => {
                          this.props.form.setFieldsValue({ birthday: dayjs(date).format("YYYY-MM-DD") });
                          this.setState({ showBirthdayPicker: false });
                        }}
                        onDismiss={() => {
                          this.setState({ showBirthdayPicker: false });
                        }}
                        locale={{
                          DatePickerLocale: { year: "", month: "", day: "" },
                          okText: this.props.languageShell.t("in_increase_submit"),
                          dismissText: this.props.languageShell.t("in_more_cancel"),
                        }}
                      />
                    </div>
                  </div>
                )}

                {this.showAffiliateCode() && (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("affiliateCode"),
                      form_success: getFieldValue("affiliateCode") && !getFieldError("affiliateCode"),
                    })}
                  >
                    {/* <div className="form-label">
                          <span>{this.props.languageShell.t("invitation_code")}</span>
                        </div> */}
                    {/* <span className="input-required">{this.requiredDot("affiliateCode")}</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className="form-control"
                        name="affiliateCode"
                        placeholder={this.props.languageShell.t("invitation_code")}
                        ref={(c) => {
                          this.affiliateCode = c;
                        }}
                        {...getFieldProps("affiliateCode", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("affiliateCode", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("affiliateCode") }],
                        })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.affiliateCode?.icon} />
                      </i>
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("affiliateCode")?.join(",")}</p>
                    </div>
                  </div>
                )}
                {this.props.tcgCommon.showCaptcha ? (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("identifying"),
                      form_success: getFieldValue("identifying") && !getFieldError("identifying"),
                    })}
                  >
                    {/* <div className="form-label">{this.props.languageShell.t("in_vwrite_code")}</div> */}
                    {/* <span className="input-required">*</span> */}
                    <div className="fixed-list">
                      <input
                        type="text"
                        autoComplete="off"
                        className={`form-control captcha`}
                        name="identifying"
                        placeholder={this.props.languageShell.t("in_vwrite_code")}
                        inputMode="numeric"
                        pattern="[0-9]*"
                        maxLength="6"
                        ref={(c) => {
                          this.identifying = c;
                        }}
                        {...getFieldProps("identifying", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("identifying", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ required: true, message: this.props.languageShell.resource["in_vwrite_code"] }],
                        })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.captcha?.icon} />
                      </i>
                      {this.props.auth.cap && (
                        <img
                          alt="captcha"
                          onClick={this.handleCaptcha}
                          src={`data:image/png;base64,${this.props.auth.cap}`}
                          className="captcha-pic"
                        />
                      )}
                      {/* {this.props.auth.cap && (
                            <Icon
                              className={cz("refresh-captcha", { loading: this.state.loadingCaptcha })}
                              type={require("!svg-sprite-loader!@/assets/images/common/icon-refresh.svg")}
                              onClick={this.handleCaptcha}
                            />
                          )} */}
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("identifying")?.join(",")}</p>
                    </div>
                  </div>
                ) : (
                  ""
                )}
              </div>
              {/* <div className="register-terms">
                <CheckBox labelDisabled checked={this.state.checkDisclaimer} onChange={this.handleDisclaimer}>
                  <p>
                    {this.props.languageShell.t("in_agree_accept2").format(deploy.ageLimit)}
                    <span className="term-link" onClick={this.handlePolicy}>
                      {this.props.languageShell.t("user_terms")}
                    </span>
                  </p>
                </CheckBox>
              </div> */}
            </div>
          </form>
          <div className="form-actions">
            <div className="btn-group">
              <button
                type="button"
                disabled={!this.state.checkDisclaimer}
                onClick={this.handleRegister}
                className="submit-btn"
              >
                <span>{this.props.languageShell.t("hd_reg_button")}</span>
              </button>
            </div>

            <div className="already-account">
              <span>{this.props.languageShell.t("in_already_account")}</span>
              <span className="login-now" onClick={() => this.props.history.push("/m/login")}>
                {this.props.languageShell.t("hd_login_button")}
              </span>
            </div>

            <div className={cz("social-media-login", { hide: !this.showSocialMediaLogin() })}>
              <div className={"tips"}>
                <span>{this.props.languageShell.t("login_way")}</span>
              </div>
              <div className={cz("item-group")}>
                {this.props.mcMenu.whitelabelVal["LINELOGIN3"] && (
                  <div onClick={this.handleFirebaseLogin("line")} className={cz("item line")}>
                    <img src={require("@/assets/images/common/line.png")} />
                  </div>
                )}
                {this.props.mcMenu.whitelabelVal["FACEBOOKLOGIN3"] && (
                  <div onClick={this.handleFirebaseLogin("facebook")} className={cz("item facebook")}>
                    <img src={require("@/assets/images/common/facebook.png")} />
                  </div>
                )}
                {this.props.mcMenu.whitelabelVal["GOOGLELOGIN3"] && (
                  <div onClick={this.handleFirebaseLogin("google")} className={cz("item google")}>
                    <img src={require("@/assets/images/common/google.png")} />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    ) : (
      <div />
    );
  }
}
export default Register;
