import React from "react";
import { CommonLogo } from "tcg-mobile-common";

import LoginForm from "@/containers/Login/LoginForm";

import "./Agent.scss";

class Agent extends React.Component {
  render() {
    return (
      <div className="form-page-container agent-login-container">
        <div className="form-header">
          <CommonLogo className="form-logo" src={require("@/assets/images/logo/logo.png")} />
        </div>
        <div className="form-wrap">
          <LoginForm {...this.props} />
        </div>
      </div>
    );
  }
}

export default Agent;
