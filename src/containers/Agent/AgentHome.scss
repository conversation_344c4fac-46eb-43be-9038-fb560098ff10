.agent-home-container {
  .agent-home-content {
    transition: all 0.2s;
  }

  .msg-count {
    position: absolute;
    top: 0;
    right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    font-size: 24px;
    color: #fff;
    background-image: linear-gradient(#f55e49, #f55e49), linear-gradient(#ed6b65, #ed6b65);
    border-radius: 50%;
    border-radius: 18px;
    box-shadow: 0 3px 8px 0 rgb(235 72 97 / 50%);
  }

  .agent-home-top {
    width: 100%;
    height: 500px;
    background: $main-color;
  }

  .agent-home-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100px;
    padding: 0 34px;

    .header-left {
      position: relative;
      display: flex;
      align-items: center;
      font-size: 28px;
      line-height: 1;
      color: #fff;

      &::before {
        display: block;
        width: 37px;
        height: 43px;
        margin-right: 20px;
        content: "";
        background: url("../../assets/images/agent/user-icon.png") no-repeat center/cover;
      }

      .agent-username {
        margin-left: 10px;
      }
    }

    .agent-service {
      width: 46px;
      height: 47px;
      background: url("../../assets/images/agent/service-icon.png") no-repeat;
      background-size: 100% 100%;
    }
  }

  .agent-balance {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 40px;
    color: #fff;

    p {
      font-size: 28px;
      text-align: center;
    }

    .balance-amount {
      margin-top: 10px;
      font-size: 60px;
    }
  }

  .agent-withdrawl-deposit {
    display: flex;
    justify-content: center;
    margin-top: 40px;
    font-size: 24px;
    color: #fff;

    .am-icon {
      width: 48px;
      height: 48px;
      margin-right: 10px;
    }

    .agent-withdrawl {
      @include flex-center;
      width: 240px;
      height: 80px;
      line-height: normal;
      text-align: center;
      border: 1px solid #fff;
      border-radius: 100px;
    }

    .agent-deposit {
      @include flex-center;
      width: 240px;
      height: 80px;
      margin-left: 48px;
      line-height: normal;
      color: $main-color;
      text-align: center;
      background: #fff;
      border-radius: 100px;
    }
  }

  .agent-offline {
    position: relative;
    z-index: 1;
    width: 100%;
    height: 160px;
    padding: 0 30px;
    margin: -80px auto 0;

    ul {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: #f7f8fe;
      border-radius: 10px;
      box-shadow: 0 10px 30px 0 rgb(0 0 0 / 12%);

      li {
        position: relative;
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        height: 100%;
        padding: 30px 10px 0;
        font-size: 24px;
        color: #2c2342;
        text-align: center;

        .offline-num {
          font-size: 48px;
          line-height: 60px;

          color: #2c2342;
        }
      }

      li:not(:last-child)::after {
        position: absolute;
        top: 56px;
        right: 0;
        width: 2px;
        height: 48px;
        content: "";
        background-color: #2c2342;
      }
    }
  }

  .agent-nav {
    padding: 0 30px;
    margin-top: 40px;

    ul {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;

      li {
        display: flex;
        justify-content: center;
        max-width: 100%;
        overflow: hidden;
      }

      .agent-nav-item {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;

        .nav-icon {
          @include flex-center;
          width: 100px;
          height: 100px;
          background: $main-color;
          border-radius: 110px;

          .am-icon {
            width: 52px;
            height: 52px;
            fill: #fff;
          }
        }

        .agent-nav-name {
          margin-top: 10px;
          font-size: 26px;
          color: #fff;
          text-align: center;
        }
      }
    }
  }

  .agent-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: var(--footer-height);
    background: var(--footer-bg);
    box-shadow: 0 0 0.2rem 0 rgb(0 0 0 / 10%);

    ul {
      display: flex;
      flex-wrap: nowrap;
      height: 100%;

      li {
        display: flex;
        flex: 1;
        justify-content: center;
        height: 100%;
      }

      .footer-nav {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: $main-color;

        .am-icon {
          width: 40px;
          height: 40px;
          margin-bottom: 10px;
        }
      }
    }
  }
}

.agent-download-bar.show-bar + .agent-home-content {
  padding-top: 112px;
}

.agent-footer {
  @include iphonex {
    height: calc(98px + (var(--safe-area-inset-bottom)));
    padding-bottom: (var(--safe-area-inset-bottom));
  }
}

@include iphonex-app {
  .agent-footer {
    height: calc(98px + (var(--safe-area-inset-bottom)));
    padding-bottom: (var(--safe-area-inset-bottom));
  }
}
