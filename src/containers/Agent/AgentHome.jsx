import React, { Component } from "react";
import { I<PERSON>, Modal } from "antd-mobile";
import dayjs from "dayjs";
import { isEmpty } from "lodash";
import { inject, observer } from "mobx-react";
import { currencyFormat, withCs } from "tcg-mobile-common";

import account from "@/apis/account";
import deploy from "@/config/deploy.config";
import { get } from "@/utils/storage";

import "./AgentHome.scss";

const home = require("!svg-sprite-loader!@/assets/images/agent/home.svg");
const dividends = require("!svg-sprite-loader!@/assets/images/agent/dividends.svg");
const openAccount = require("!svg-sprite-loader!@/assets/images/agent/open-account.svg");
const announcement = require("!svg-sprite-loader!@/assets/images/agent/announcement.svg");

const agentNavs = [
  {
    name: "team_overview",
    key: "AGEPAG",
    path: "/m/agent/team/overview",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-overview.svg"),
  },
  {
    name: "transaction_record",
    key: "TRANSREC3",
    path: "/m/transaction/record?types=A",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-account-record.svg"),
  },
  {
    name: "team_manage",
    key: "DOWNMAN",
    path: "/m/member/team",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-team-management.svg"),
  },
  {
    name: "red_envelope_rain",
    key: "AGENTRANSMULTDOWN3",
    path: "/m/redRain",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-red-rain.svg"),
  },
  {
    name: "team_propfit_and_loss",
    key: "AGETEAMPNL3",
    path: "/m/agent/team/pnl",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-teampl.svg"),
  },
  {
    name: "team_bet",
    key: "TEAMBETTING3",
    path: "/m/teamBetting",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-team-betting.svg"),
  },
  {
    name: "backedit",
    key: "GAMSER",
    path: "/m/member/agent/agentRebeat",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-rebate.svg"),
  },
  {
    name: "agent_rebate",
    key: "AGEREBINC3",
    path: "/m/member/teamRebate",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-team-rebate.svg"),
  },
  {
    name: "security_center",
    key: "SECPRIV",
    path: "/m/securityCenter",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-security-center.svg"),
  },
  {
    name: "news",
    key: "MAILCEN",
    path: "/m/webEmail",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-mail.svg"),
  },
  {
    name: "daily_wage",
    key: "DAYSAL",
    path: "/m/member/agent/dailySalary",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-daysal.svg"),
  },
  {
    name: "daily_wage",
    key: "MRDAILYSAL3",
    path: "/m/member/agent/manualdailySalary",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-red-rain.svg"),
  },
  {
    name: "hour_wage",
    key: "HOURSAL3",
    path: "/m/member/agent/hourSalary",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-red-rain.svg"),
  },
  {
    name: "online_service",
    key: "service",
    path: "service",
    icon: require("!svg-sprite-loader!@/assets/images/agent/agent-service.svg"),
  },
];

@inject("common", "wallet", "dividend", "languageShell", "mcLanguage", "mcMenu", "auth")
@withCs
@observer
class AgentHome extends Component {
  get isAgent() {
    return this.props.auth.isAgent;
  }
  state = { unreadCount: 0, noticeUnreadCount: 0, teamData: {} };
  componentDidMount() {
    if (this.isAgent) {
      this.props.wallet.getWallet();
      this.getInboxUnreadCount();
      this.getAgentNotice();
      this.getTeamKPIC();
      this.props.dividend.getDividendInfo();
    } else {
      this.props.replace("/m/home");
    }
  }
  getTeamKPIC() {
    account
      .getTeamKPIC({
        startDate: dayjs().format("YYYY-MM-DD"),
        endDate: dayjs().format("YYYY-MM-DD"),
        size: 0,
      })
      .then((res) => {
        const data = res.data.value;
        this.setState({
          teamData: data.footer,
        });
      });
  }
  getInboxUnreadCount() {
    account.getInboxUnreadCount().then((res) => {
      this.setState({
        unreadCount: res.data.value,
      });
    });
  }
  getAgentNotice() {
    account
      .annount(
        {
          types: "A",
          groupName: "m_home",
          merchantCode: deploy.merchant,
        },
        false,
        false
      )
      .then((res) => {
        const result = res.data.value;
        if (result) {
          this.setState({
            noticeUnreadCount: result.footer.unreadCount,
          });
        }
      });
  }
  checkContract = async () => {
    let token = get("MC_SESSION_INFO");
    if (token && this.props.common.memberInfo) {
      const userType = this.props.common.memberInfo.type;
      const isNotVirtual = ![4, 40, 41, 42].includes(userType);
      let self = this;
      if (isNotVirtual) {
        this.props.dividend.getDividendInfo().then(() => {
          const { dailysalreqtype, divtypecompperiodreq, contracthoursalreq } = this.props.dividend.info;
          if (!isEmpty(dailysalreqtype)) {
            Modal.alert(
              this.props.languageShell.resource["in_daily_salary_agt"],
              this.props.languageShell.resource["in_daily_salary_req"],
              [
                {
                  text: this.props.languageShell.resource["in_more_cancel"],
                  onPress: () => {},
                },
                {
                  text: this.props.languageShell.resource["in_go"],
                  onPress: () => {
                    this.props.push("/m/member/agent/getSalaryContract");
                  },
                },
              ]
            );
          }
          if (!isEmpty(divtypecompperiodreq)) {
            Modal.alert(
              this.props.languageShell.resource["in_dividend_agt"],
              this.props.languageShell.resource["in_dividend_req"],
              [
                { text: this.props.languageShell.resource["in_more_cancel"], onPress: () => {} },
                {
                  text: this.props.languageShell.resource["in_go"],
                  onPress: () => {
                    self.props.push(`/m/member/agent/getDevidendContract`);
                  },
                },
              ]
            );
          }
          if (!isEmpty(contracthoursalreq)) {
            Modal.alert(
              this.props.languageShell.resource["in_hour_salary_agt"],
              this.props.languageShell.resource["in_hour_salary_req"],
              [
                { text: this.props.languageShell.resource["in_more_cancel"], onPress: () => {} },
                {
                  text: this.props.languageShell.resource["in_go"],
                  onPress: () => {
                    this.props.push("/m/member/agent/getHourContract");
                  },
                },
              ]
            );
          }
        });
      }
    }
  };
  changeRoute = (path) => {
    if (get("MC_SESSION_INFO")) {
      if (path === "service") {
        this.handleCustomerService();
      } else {
        this.props.push(path);
      }
    }
  };
  handleCustomerService = () => {
    this.props.openCs();
  };
  getHours = () => {
    const now = new Date();
    const hours = now.getHours();
    let text = "";
    if (hours >= 0 && hours <= 10) {
      text = this.props.languageShell.t("good_morning");
    } else if (hours > 10 && hours <= 14) {
      text = this.props.languageShell.t("good_afternoon_early");
    } else if (hours > 14 && hours <= 18) {
      text = this.props.languageShell.t("good_afternoon_late");
    } else if (hours > 18 && hours <= 24) {
      text = this.props.languageShell.t("good_evening");
    }
    return text;
  };
  showMenu = (key) => {
    switch (key) {
      case "service":
        return true;
      case "DAYSAL":
        return this.props.mcMenu.whitelabelVal[key] && this.props.dividend.showSalary;
      case "MRDAILYSAL3":
        return this.props.mcMenu.whitelabelVal[key] && this.props.dividend.showManualSalary;
      case "HOURSAL3":
        return this.props.mcMenu.whitelabelVal[key] && this.props.dividend.showHourSalary;
      default:
        return this.props.mcMenu.whitelabelVal[key];
    }
  };
  render() {
    const session = get("MC_SESSION_INFO") || {};
    const { teamData } = this.state;
    return (
      <div className="agent-home-container app-main avoid-footer">
        <div className="agent-home-content">
          <div className="agent-home-top">
            <div className="agent-home-header">
              <div className="header-left">
                <span>{this.getHours()}</span>
                <span className="agent-username">{session.userName}</span>
              </div>
              <div className="agent-service" onClick={this.handleCustomerService} />
            </div>
            <div className="agent-balance">
              <p>{this.props.mcLanguage.t("total")}</p>
              <div className="balance-amount">
                {this.props.wallet.wallet ? (
                  <span>{currencyFormat.format({ value: this.props.wallet.wallet?.sumBalance, fixed: 2 })}</span>
                ) : (
                  ""
                )}
              </div>
            </div>
            <div className="agent-withdrawl-deposit">
              <div className="agent-withdrawl" onClick={() => this.changeRoute("/m/withdraw")}>
                <Icon type={require("!svg-sprite-loader!@/assets/images/agent/agent-withdraw.svg")} />
                <span>{this.props.languageShell.t("hd_withdraw_button")}</span>
              </div>
              <div className="agent-deposit" onClick={() => this.changeRoute("/m/voucherCenter")}>
                <Icon type={require("!svg-sprite-loader!@/assets/images/agent/agent-deposit.svg")} />
                <span>{this.props.languageShell.t("hd_deposit_button")}</span>
              </div>
            </div>
          </div>
          <div className="agent-offline">
            <ul>
              <li>
                <span className="offline-num">{teamData.teamLoginMemberCount || 0}</span>
                <span>{this.props.mcLanguage.t("teem_overview_login_count")}</span>
              </li>
              <li>
                <span className="offline-num">{teamData.teamBetMemberCount || 0}</span>
                <span>{this.props.mcLanguage.t("betting_player")}</span>
              </li>
              <li>
                <span className="offline-num">{teamData.teamRegisterMemberCount || 0}</span>
                <span>{this.props.mcLanguage.t("registered_persons")}</span>
              </li>
              <li>
                <span className="offline-num">{teamData.teamOnlineCount || 0}</span>
                <span>{this.props.mcLanguage.t("online_users")}</span>
              </li>
            </ul>
          </div>
          <div className="agent-nav">
            <ul>
              {agentNavs.map(({ name, key, icon, path }) => {
                return (
                  this.showMenu(key) && (
                    <li key={path}>
                      <div className="agent-nav-item" onClick={() => this.changeRoute(path)}>
                        <div className="nav-icon">
                          <Icon type={icon} />
                        </div>

                        <span className="agent-nav-name">{this.props.mcLanguage.t(name)}</span>
                        {path === "/m/webEmail" && this.state.unreadCount > 0 ? (
                          <span className="msg-count">{this.state.unreadCount}</span>
                        ) : (
                          ""
                        )}
                      </div>
                    </li>
                  )
                );
              })}
            </ul>
          </div>
        </div>
        <div className="agent-footer">
          <ul>
            <li>
              <div className="footer-nav" onClick={() => this.changeRoute("/m/home")}>
                <Icon className="footer-icon home" type={home} />
                <span className="footer-nav-name on">{this.props.languageShell.t("hd_home")}</span>
              </div>
            </li>
            <li>
              <div className="footer-nav" onClick={() => this.changeRoute("/m/agentRegister")}>
                <Icon className="footer-icon account" type={openAccount} />
                <span className="footer-nav-name">{this.props.mcLanguage.t("open_account")}</span>
              </div>
            </li>
            {this.props.dividend.showDividend && (
              <li>
                <div className="footer-nav" onClick={() => this.changeRoute("/m/member/agent/agentDivident")}>
                  <Icon className="footer-icon dividend" type={dividends} />
                  <span className="footer-nav-name">{this.props.mcLanguage.t("dividend")}</span>
                </div>
              </li>
            )}
            <li>
              <div className="footer-nav" onClick={() => this.changeRoute("/m/member/notice?types=A")}>
                <Icon className="footer-icon announcement" type={announcement} />
                <span className="footer-nav-name">{this.props.mcLanguage.t("ac_announcement")}</span>
                {this.state.noticeUnreadCount > 0 && <span className="msg-count">{this.state.noticeUnreadCount}</span>}
              </div>
            </li>
          </ul>
        </div>
      </div>
    );
  }
}

export default AgentHome;
