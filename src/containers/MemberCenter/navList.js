export const mainNav = [
  {
    key: "DEPOSIT",
    name: "in_increase_deposit",
    path: "/m/voucherCenter",
    img: require("@/assets/images/member/deposit.png"),
  },
  {
    key: "WITHDRAW",
    name: "in_increase_withdrawal",
    path: "/m/withdraw",
    img: require("@/assets/images/member/deposit.png"),
  },
  {
    key: "TRANSBETREPMEM3",
    name: "in_betting_record",
    path: "/m/gameRecord",
    img: require("@/assets/images/member/betting-record.png"),
  },
  {
    key: "TRANSREC3",
    name: "in_account_record",
    path: "/m/transaction/record",
    img: require("@/assets/images/member/account-record.png"),
  },
  {
    key: "SECPRIV",
    name: "security_center",
    path: "/m/securityCenter",
    img: require("@/assets/images/member/security-center.png"),
  },
];

export const navList = [
  {
    type: "AGENT",
    name: "in_agency_center",
    path: "/m/agent/home",
    icon: require("!svg-sprite-loader!@/assets/images/member/agent-center.svg"),
  },

  {
    key: "MANPLAYREB3",
    name: "manual_rebate",
    path: "/m/member/manualRebate",
    icon: require("!svg-sprite-loader!@/assets/images/member/manual-rebate.svg"),
  },
  {
    type: "BANK",
    name: "bank_card",
    path: "/m/myBankCards",
    icon: require("!svg-sprite-loader!@/assets/images/member/bank-account.svg"),
  },
  {
    type: "REFERRAL",
    name: "invite_friends",
    path: "/m/inviteFriends",
    icon: require("!svg-sprite-loader!@/assets/images/member/invite-friends.svg"),
  },
  {
    key: "TRANSREC3",
    name: "in_account_record",
    path: "/m/transaction/record",
    icon: require("!svg-sprite-loader!@/assets/images/member/account-record.svg"),
  },
  {
    key: "DEPRECM3",
    name: "load_record",
    path: "/m/vouReport",
    icon: require("!svg-sprite-loader!@/assets/images/member/deposit-record.svg"),
  },
  {
    key: "WITHREC3",
    name: "withdraw_report",
    path: "/m/withdrawReport",
    icon: require("!svg-sprite-loader!@/assets/images/member/withdraw-record.svg"),
  },

  {
    key: "PERSREP",
    name: "propfit_and_loss",
    path: "/m/profitandloss",
    icon: require("!svg-sprite-loader!@/assets/images/member/profit-loss.svg"),
  },
  {
    key: "REWCEN3",
    name: "in_reward_center",
    path: "/m/rewardCenter",
    icon: require("!svg-sprite-loader!@/assets/images/member/reward-center.svg"),
  },
  {
    type: "MISSION",
    name: "mission_activity",
    path: "/m/leaderBoard",
    icon: require("!svg-sprite-loader!@/assets/images/member/mission-activity.svg"),
  },
  {
    type: "SERVICE",
    name: "in_customer_services_new",
    icon: require("!svg-sprite-loader!@/assets/images/member/online-service.svg"),
  },
  {
    key: "FEEDBACK3",
    name: "feedback_title",
    path: "/m/feedback",
    icon: require("!svg-sprite-loader!@/assets/images/member/feedback.svg"),
  },
  // {
  //   key: "MAILCEN",
  //   name: "news",
  //   path: "/m/webEmail",
  //   icon: require("!svg-sprite-loader!@/assets/images/member/news.svg"),
  // },
  // {
  //   type: "TRANSFERUP",
  //   name: "transfer_up",
  //   path: "/m/upLine/transfer",
  //   icon: require("!svg-sprite-loader!@/assets/images/member/transfer-up.svg"),
  // },
  // {
  //   key: "PTSYS3",
  //   name: "point_mall",
  //   path: "/m/mall/mallHome",
  //   icon: require("!svg-sprite-loader!@/assets/images/member/points.svg"),
  //   img: require("@/assets/images/common/points.png"),
  // },
  // {
  //   type: "TRANSFER",
  //   name: "wallet_transfer",
  //   path: "/m/wallet",
  //   icon: require("!svg-sprite-loader!@/assets/images/member/wallet-transfer.svg"),
  // },
  // {
  //   key: "PLYRBT",
  //   name: "my_rebate",
  //   path: "/m/member/rebateReport",
  //   icon: require("!svg-sprite-loader!@/assets/images/member/my-rebate.svg"),
  // },
];
