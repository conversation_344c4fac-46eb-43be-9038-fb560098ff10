import React from "react";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";
import { navigatorUtil, withCs } from "tcg-mobile-common";

import Footer from "@/components/Footer/Footer";
import Header from "@/components/OutterHeader/Header";
import UserAvatar from "@/components/UserAvatar/UserAvatar";
import UserBalance from "@/components/UserBalance/UserBalance";
import withAuth from "@/hoc/withAuth";
import withMemberMenu from "@/hoc/withMemberMenu";

import { mainNav, navList } from "./navList";

import "./style/MemberCenter.scss";

const getDay = (time) => {
  return Math.floor(time / 1000 / 60 / 60 / 24);
};

const showDownload = !navigatorUtil.isApp && !navigatorUtil.isPWA;

@inject("common", "languageShell", "mcMenu", "auth", "tcgCommon", "wallet", "withdraw")
@withCs
@withAuth
@withMemberMenu
@observer
class MemberCenter extends React.Component {
  get isLogin() {
    return this.props.auth.isLogin;
  }
  get isAgent() {
    return this.props.auth.isAgent;
  }
  get whitelabelVal() {
    return this.props.mcMenu.whitelabelVal;
  }
  get transferUp() {
    const memberInfo = this.props.common.memberInfo;
    return this.isLogin && this.whitelabelVal.AGETRANSFERUP3 && memberInfo?.toUpline && memberInfo?.type !== 2;
  }
  get regDays() {
    const { regDate = Date.now() } = this.props.common.memberInfo || {};
    return getDay(Date.now() - regDate);
  }
  get vipLabelName() {
    return this.props.tcgCommon.vipLabelName;
  }
  get hasVip() {
    return this.props.tcgCommon.hasVip;
  }
  get isSingleWallet() {
    return this.props.wallet.merchantWallet.isSingle === 1;
  }
  get allowBindBank() {
    const withdrawSett = this.props.withdraw.withdrawSett;
    return withdrawSett.normalBankWithdrawalSwitch === "Y" && withdrawSett.allowWithdraw === "Y";
  }
  get missionCount() {
    return this.props.personal.availablePromosByKey("MISSION").count;
  }
  get totalUnclaimedPromosCount() {
    return this.props.personal.totalUnclaimedPromosCount; //reward center count
  }
  componentDidMount() {
    if (this.isLogin) {
      this.props.common.renew(true);
      this.props.withdraw.getWithdrawSett();
    }
  }
  getUsername() {
    const { memberInfo } = this.props.common;
    if (!memberInfo) {
      return "";
    }
    return memberInfo.nickname ? memberInfo.nickname : memberInfo.account;
  }
  handleLogout = (e) => {
    this.props.logout();
  };
  headerRight = () => {
    return (
      <div className="mine-nav">
        <div className="setting-btn" onClick={() => this.props.push("/m/set")}>
          <img src={require("@/assets/images/member/icon-set.png")} />
        </div>
        <div className="message-btn" onClick={() => this.props.push("/m/webEmail")}>
          <img src={require("@/assets/images/member/icon-msg.png")} />
          <i className="icon-notify" />
        </div>
      </div>
    );
  };
  render() {
    const { menuHelper } = this.props;
    return (
      <div className="member-center-container app-main avoid-footer">
        <Header rightContent={this.headerRight()} />
        <div className="member-info-wrap">
          <div className="member-avatar">
            <UserAvatar />
          </div>
          {this.isLogin ? (
            <div className="member-logged">
              <div className="member-name">
                <span>{this.getUsername()}</span>
                {this.hasVip && <span className="vip-level">{this.vipLabelName}</span>}
              </div>
              <UserBalance />
              {/* <div className="member-reg-days">
                {this.props.languageShell
                  .t("joined_days")
                  .format(this.props.languageShell.t("brand_name"), this.regDays)}
              </div> */}
            </div>
          ) : (
            <div className="member-nologin" onClick={() => this.props.push("/m/login")}>
              <span className="please-login">{this.props.languageShell.t("login_or_register")}</span>
            </div>
          )}
        </div>
        {(this.showVipBenefit || showDownload) && (
          <div className="member-top-nav">
            <div className="top-nav-bg">
              {this.showVipBenefit && (
                <div className="top-nav-item" onClick={() => this.props.push("/m/member/benefits")}>
                  <img className="item-icon" src={require("@/assets/images/member/vip.png")} alt="" />
                  <div className="item-info">
                    <h5>{this.props.languageShell.t("VIP")}</h5>
                    <p>VIP PRIVILEGE</p>
                  </div>
                </div>
              )}
              {showDownload && (
                <div className="top-nav-item" onClick={() => this.props.common.setDownPopup(true)}>
                  <img className="item-icon" src={require("@/assets/images/member/download.png")} alt="" />
                  <div className="item-info">
                    <h5>{this.props.languageShell.t("APP")}</h5>
                    <p>DOWNLOAD</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="member-main-nav">
          <div className="member-main-nav-list">
            {mainNav.map((item, index) => {
              return (
                menuHelper.displayMenu(item) && (
                  <div className="main-nav-item" key={`main-nav-${index}`} onClick={() => menuHelper.menuClick(item)}>
                    <img className="main-nav-icon" src={item.img} alt="" />
                    <span>{this.props.languageShell.t(item.name)}</span>
                  </div>
                )
              );
            })}
          </div>
        </div>
        <div className="member-content">
          <div className="member-nav-wrap">
            <div className="member-nav-list">
              {navList.map((item, index) => {
                return (
                  menuHelper.displayMenu(item) && (
                    <div className="member-nav-item" key={`mc-nav-${index}`} onClick={() => menuHelper.menuClick(item)}>
                      <Icon className="nav-icon" type={item.icon} />
                      <span>{this.props.languageShell.t(item.name)}</span>
                      <Icon
                        className="item-arrow"
                        type={require("!svg-sprite-loader!@/assets/images/common/arrow-right.svg")}
                      />
                    </div>
                  )
                );
              })}
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }
}

export default MemberCenter;
