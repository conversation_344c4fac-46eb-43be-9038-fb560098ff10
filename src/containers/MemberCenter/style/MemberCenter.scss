.member-center-container {
  .mine-nav {
    display: flex;
    align-items: center;
    padding-right: 28px;

    .setting-btn,
    .message-btn {
      position: relative;

      .am-icon,
      img {
        display: block;
        width: 42px;
        height: 42px;
      }
    }

    .setting-btn {
      margin-right: 40px;
    }

    .icon-notify {
      position: absolute;
      top: -6px;
      right: -6px;
      width: 12px;
      height: 12px;
      background: #db6372;
      border-radius: 12px;
    }
  }

  .member-info-wrap {
    display: flex;
    align-items: center;
    padding: 20px 28px;
  }

  .member-avatar {
    width: 140px;
    height: 140px;
    margin-right: 30px;
  }

  .please-login {
    font-size: 36px;
    font-weight: 600;
    color: #fff;
  }

  .member-name {
    display: flex;
    align-items: center;
    font-size: 30px;
    color: #efc77a;
  }

  .member-balance {
    margin-top: 10px;
    font-size: 26px;
    color: #ecfaff;

    .refresh-balance {
      width: 30px;
      height: 30px;
    }
  }

  .vip-level {
    padding: 3px 8px;
    margin-left: 8px;
    font-size: 20px;
    line-height: normal;
    color: #fff;
    background: #fead00;
    border-radius: 2px;
  }

  .member-reg-days {
    margin-top: 8px;
    font-size: 24px;
    line-height: 34px;
    color: #a5a9b3;
  }

  .member-top-nav {
    padding: 0 12px;

    .top-nav-bg {
      @include flex-center;
      height: 160px;
      background: url("~@/assets/images/member/nav-bg.png") no-repeat center/cover;
    }

    .top-nav-item {
      @include flex-center;
      position: relative;
      width: 50%;
      height: 100%;

      & + .top-nav-item::after {
        position: absolute;
        top: 37px;
        left: 0;
        width: 1px;
        height: 85px;
        content: "";
        background-color: #868686;
      }

      h5 {
        font-size: 30px;
        font-weight: 500;
        line-height: normal;
        color: #fff;
      }

      p {
        font-size: 24px;
        font-weight: 500;
        color: #887f6d;
      }
    }

    .item-icon {
      display: block;
      height: 120px;
      margin-right: 15px;
    }
  }

  .member-main-nav {
    padding: 0 28px 20px;
    margin-top: 20px;

    .member-main-nav-list {
      display: flex;
      align-items: flex-start;
    }

    .main-nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 25%;
      font-size: 28px;
      font-weight: 600;
      line-height: 40px;
      color: #ecfaff;
      text-align: center;
    }

    .main-nav-icon {
      display: block;
      width: 72px;
      height: 72px;
      margin-bottom: 5px;
      object-fit: contain;
    }
  }

  .member-content {
    padding: 20px 0;
  }

  .member-nav-wrap {
    .member-nav-list {
      display: grid;
      gap: 20px;
    }

    .member-nav-item {
      position: relative;
      display: flex;
      align-items: center;
      height: 100px;
      padding: 0 0 0 32px;
      font-size: 28px;
      line-height: normal;
      color: #ecfaff;
      text-transform: capitalize;
      background: #383838;
    }

    .nav-icon {
      display: block;
      width: 44px;
      height: 44px;
      margin-right: 24px;
      fill: none;
    }

    .item-arrow {
      position: absolute;
      top: 40px;
      right: 24px;
      width: 20px;
      height: 20px;
    }
  }
}
