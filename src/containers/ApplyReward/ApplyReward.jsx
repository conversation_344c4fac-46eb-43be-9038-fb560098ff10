import React from "react";
import ReactDOM from "react-dom";
import { with<PERSON><PERSON><PERSON> } from "react-router";
import { Icon, ImagePicker, Toast } from "antd-mobile";
import { inject, observer } from "mobx-react";
import Qs from "qs";

import accountApi from "@/apis/account";
import Footer from "@/components/Footer/Footer";
import Header from "@/components/OutterHeader/Header";
import deploy from "@/config/deploy.config";
import { get } from "@/utils/storage";

import "./ApplyReward.scss";

const uploadIcon = require("!svg-sprite-loader!@/assets/images/activity/upload.svg");

@inject("common", "languageShell")
@withRouter
@observer
class ApplyReward extends React.Component {
  state = { remarks: "", files: [], pictures: [], disabled: true, promotion: {} };
  componentDidMount() {
    if (!get("MC_SESSION_INFO")) {
      this.props.history.push("/m/activity");
    }
    this.getAnnount();
  }
  getAnnount() {
    let data = {
      types: deploy.contentTypes.Promotion,
      merchantCode: deploy.merchant,
      platform: "M",
      token: get("MC_SESSION_INFO") ? get("MC_SESSION_INFO").token : "",
    };
    this.props.common.annount(data).then((res) => {
      const { search } = this.props.history.location;
      const { id } = Qs.parse(search, {
        ignoreQueryPrefix: true,
      });
      if (id) {
        const promo = this.props.common.activity.find((item) => {
          return +item.id === +id;
        });
        this.setState({
          promotion: promo || {},
        });
      } else {
        this.props.history.push("/m/activity");
      }
    });
  }
  get checkInput() {
    const { imageRequired, remarkRequired } = this.state.promotion;
    if (imageRequired === "Y" && remarkRequired === "Y") {
      return this.state.remarks.length && this.state.pictures.length;
    } else if (imageRequired === "Y" && remarkRequired === "N") {
      return this.state.pictures.length;
    } else if (imageRequired === "N" && remarkRequired === "Y") {
      return this.state.remarks.length;
    } else {
      return true;
    }
  }
  imageChange = (files, type, index) => {
    this.setState({
      files,
    });
  };
  inputChange = (e) => {
    this.setState({
      remarks: e.target.value,
    });
  };
  uploadImg = (files, type, index) => {
    const newFiles = JSON.parse(JSON.stringify(this.state.pictures));
    if (type === "add") {
      const imgType = files[files.length - 1].file.type;
      const imgSize = files[files.length - 1].file.size;
      if (imgType !== "image/jpeg" && imgType !== "image/png" && imgType !== "image/jpg") {
        Toast.info(this.props.languageShell.t("activity_image_type"), 1.5);
      } else if (imgSize > 5 * 1024 * 1024) {
        Toast.info(this.props.languageShell.t("activity_apply_remark_limit3"), 1.5);
      } else if (files.length > 3) {
        Toast.info(this.props.languageShell.t("activity_apply_remark_limit4"), 1.5);
      } else {
        const imgUrl = files[files.length - 1].url;
        newFiles.push(imgUrl);
        this.setState({
          files,
          pictures: newFiles,
        });
      }
    }
    if (type === "remove") {
      newFiles.splice(index, 1);
      this.setState({
        files,
        pictures: newFiles,
      });
    }
  };
  handleUpload = () => {
    const dom = ReactDOM.findDOMNode(this.upload);
    const fileInput = dom.querySelector("input[type=file]");
    const event = new MouseEvent("click");
    fileInput.dispatchEvent(event);
  };
  handleSubmit = () => {
    Toast.loading(this.props.languageShell.t("loading"), 10);
    const { linkedPromotionId } = this.state.promotion;
    accountApi
      .signUpRequestReward({
        pictures: this.state.pictures,
        promotionId: linkedPromotionId,
        remarks: this.state.remarks,
      })
      .then((res) => {
        if (res.data.success) {
          this.props.history.push("/m/activity");
        }
      })
      .finally(() => {
        Toast.hide();
      });
  };
  handleCancel = () => {
    this.setState({
      pictures: [],
      files: [],
      remarks: "",
    });
  };
  render() {
    const { files } = this.state;
    const { imageRequired, remarkRequired } = this.state.promotion;
    return (
      <div className="apply-reward app-main avoid-footer">
        <Header />
        <div className="apply-body">
          <div className="apply-form">
            {remarkRequired === "Y" && (
              <div className="apply-form-group">
                <label className="form-label">{this.props.languageShell.t("activity_apply_info")}</label>
                <textarea
                  value={this.state.remarks}
                  className="remark-textarea"
                  maxLength={140}
                  placeholder={this.props.languageShell.t("activity_apply_remark_limit1")}
                  onChange={this.inputChange}
                />
              </div>
            )}
            {imageRequired === "Y" && (
              <div className="apply-form-group">
                <label className="form-label">{this.props.languageShell.t("activity_apply_image")}</label>
                <div className="updoad-box">
                  <div className="upload-default" hidden={this.state.files.length}>
                    <Icon className="upload-icon" type={uploadIcon} />
                    <p>{this.props.languageShell.t("activity_apply_remark_limit2")}</p>
                    <span className="upload-btn" onClick={this.handleUpload}>
                      {this.props.languageShell.t("activity_choose_doc")}
                    </span>
                  </div>
                  <div hidden={!this.state.files.length}>
                    <ImagePicker
                      ref={(e) => (this.upload = e)}
                      files={files}
                      onChange={this.uploadImg}
                      accept="image/jpeg,image/jpg,image/png"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="apply-btns">
            <button className="apply-cancel" onClick={this.handleCancel}>
              {this.props.languageShell.t("in_more_cancel")}
            </button>
            <button disabled={!this.checkInput} onClick={this.handleSubmit} className="submit-btn">
              {this.props.languageShell.t("activity_submit_apply")}
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }
}

export default ApplyReward;
