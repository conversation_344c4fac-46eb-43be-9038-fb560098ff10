.apply-reward {
  .apply-title {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80px;
    font-size: 30px;
    color: #000;
    background: linear-gradient(180deg, #fafafa, #e2e2e2);
  }

  .apply-body {
    padding: 0 44px;

    .apply-form-group {
      margin-top: 40px;
    }

    .remark-textarea {
      width: 100%;
      height: 310px;
      padding: 20px 30px;
      font-size: 28px;
      background: #fff;
      border: none;
      border-radius: 10px;
    }

    .form-label {
      display: block;
      height: 70px;
      line-height: 70px;
      color: #fff;
      text-align: left;
    }

    .am-image-picker-list {
      padding: 0;
      margin: 0;

      .am-flexbox {
        margin: 0;
      }
    }

    .updoad-box {
      padding: 30px 0;
      background-color: #fff;
      border-radius: 10px;

      .upload-default {
        font-size: 28px;
        color: #898989;
        text-align: center;

        .upload-icon {
          width: 160px;
          height: 150px;
        }

        .upload-btn {
          display: inline-block;
          width: 268px;
          height: 70px;
          margin: 20px 0 0;
          font-size: 32px;
          font-weight: bold;
          line-height: 70px;
          color: #071805;
          text-align: center;
          background: linear-gradient(90deg, #7feb4b 0%, #deed00 100%);
          border-radius: 16px;
        }
      }
    }

    .apply-btns {
      display: flex;
      gap: 19px;
      align-items: center;
      margin-top: 45px;

      button {
        font-size: 32px;
        font-weight: bold;
        color: #fff;
      }

      .apply-cancel {
        width: 200px;
        height: 70px;
        line-height: 70px;
        background: rgb(155 155 155 / 43%);
        border: none;
        border-radius: 16px;
      }

      .submit-btn {
        display: block;
        flex: 1;
        width: 100%;
        height: 70px;
        line-height: 70px;
        color: #071805;
        background: linear-gradient(90deg, #7feb4b 0%, #deed00 100%);
        border: none;
        border-radius: 16px;

        &:disabled {
          color: #fff;
          background: rgb(155 155 155 / 43%);
          box-shadow: none;
        }
      }
    }
  }
}
