import React from "react";
import { Toast } from "antd-mobile";
import { inject, observer } from "mobx-react";
import Qs from "qs";

import { isIos } from "../../utils/navigatorUtil";

import AndroidGuide from "./AndroidGuide";
import IosGuide from "./IosGuide";

import "./AppDownload.scss";

function request(obj) {
  return new Promise(function (resolve, reject) {
    const xhr = new XMLHttpRequest();
    xhr.open(obj.method || "GET", obj.url);
    if (obj.headers) {
      Object.keys(obj.headers).forEach(function (key) {
        xhr.setRequestHeader(key, obj.headers[key]);
      });
    }
    xhr.onload = function () {
      if (xhr.status >= 200 && xhr.status < 300) {
        resolve(xhr.response);
      } else {
        reject(xhr.statusText);
      }
    };
    xhr.onerror = function () {
      return reject(xhr.statusText);
    };
    xhr.send(obj.body);
  });
}

@inject("mcLanguage", "mcCommon", "languageShell")
@observer
class AppDownload extends React.Component {
  state = {
    activeKey: "0",
    profileUrl: "",
    androidUrl: "",
    icon: `/m/icons/icon-192x192.png`,
    title: "",
  };
  componentDidMount() {
    this.fetchTitle();
    const params = Qs.parse(window.location.search, {
      ignoreQueryPrefix: true,
    });
    const { language } = params;
    if (language) {
      this.props.mcLanguage.changeLanguageTo(language);
    }
    const settings = this.props.mcCommon.resources.reduce((acc, curr) => {
      acc[curr.resourceType] = curr;
      return acc;
    }, {});
    const link = settings["MOBILECONFIG"];
    const android = settings["Android"];
    if (link && link.url) {
      this.setState({ profileUrl: (link.url || "").trim() });
    }
    if (android && android.url) {
      this.setState({ androidUrl: (android.url || "").trim() });
    }
    const icon = settings["AppDownloadIcon"];
    if (icon && icon.url) {
      this.setState({ icon: (icon.url || "").trim() });
    }
  }
  componentWillUnmount() {
    window.removeEventListener("focus", this.handleFocusRedirect);
  }

  async fetchTitle() {
    const dom = document.querySelector("link[rel=manifest]");
    if (!dom || !dom.href) {
      this.setState({ title: document.title });
      return;
    }
    try {
      const result = await request({ url: dom.href });
      this.setState({ title: JSON.parse(result).short_name || document.title });
    } catch (e) {
      this.setState({ title: document.title });
    }
  }

  handleFocusRedirect = () => {
    Toast.info(`${this.props.mcLanguage.t("app_goto_setting")}...`, 4, () => {
      setTimeout(() => {
        if (process.env.NODE_ENV === "production") {
          location.href = "/mobile/mc/setup.mobileprovision";
        } else {
          location.href = "/setup.mobileprovision";
        }
      }, 0);
    });
  };

  handleDownload = (e) => {
    if (isIos()) {
      if (this.state.profileUrl) {
        window.addEventListener("focus", this.handleFocusRedirect, { once: true });
        return true;
      } else {
        e.preventDefault();
        Toast.offline(this.props.mcLanguage.t("coming_soon"), 3);
        return false;
      }
    } else {
    }
  };
  getTarget(url) {
    if (/.*\.(mobileconfig|apk)$/gi.test(url)) {
      return "_self";
    }
    return "_blank";
  }
  getDownloadProps = () => {
    const props = {};
    if (isIos()) {
      props.href = this.state.profileUrl;
      props.target = this.getTarget(this.state.profileUrl);
      props.onClick = (e) => {
        if (this.state.profileUrl) {
          window.addEventListener("focus", this.handleFocusRedirect, { once: true });
          return true;
        } else {
          e.preventDefault();
          Toast.offline(this.props.mcLanguage.t("coming_soon"), 3);
          return false;
        }
      };
    } else {
      props.href = this.state.androidUrl;
      props.target = this.getTarget(this.state.androidUrl);
    }
    return props;
  };
  render() {
    const { t } = this.props.mcLanguage;
    const ios = isIos();
    const downloadText = ios
      ? this.props.languageShell.t("in_download_ios")
      : this.props.languageShell.t("in_download_android");
    return (
      <div className="shell-appDownload">
        <div className="appDownload-header">
          <div className="app-name">{`${this.state.title}`}</div>
          <div className="appDownload-header-content">
            <div className="app-icon">
              <img src={this.state.icon} alt="APP ICON" />
            </div>
            <div>
              <span className="app-full-name">{`${this.state.title}`}</span>
              {/*<span className="official-logo">{t("app_verified")}</span>*/}
            </div>
            <div className="app-star-grade">
              <span className="star-icon" />
              <span className="star-icon" />
              <span className="star-icon" />
              <span className="star-icon" />
              <span className="star-icon" />
              <span className="grade-text">5.0</span>
            </div>
            <a className="app-download-btn" rel="nofollow me noopener noreferrer" {...this.getDownloadProps()}>
              {t("app_win_download")}
            </a>
          </div>
        </div>
        {ios ? <IosGuide /> : <AndroidGuide />}
        <a className="donw-load-btn" rel="nofollow me noopener noreferrer" {...this.getDownloadProps()}>
          {downloadText}
        </a>
      </div>
    );
  }
}
export default AppDownload;
