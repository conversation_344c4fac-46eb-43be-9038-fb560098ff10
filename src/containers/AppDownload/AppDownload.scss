.shell-appDownload {
  position: absolute;
  width: 100%;
  min-height: 100%;
  background: #ededed;

  &::after {
    display: block;
    height: 140px;
    content: "";
  }

  .appDownload-header {
    height: 326px;
    padding: 0 30px;
    background: url("./images/appDownload-header.png") no-repeat;
    background-size: 100% 100%;

    .app-name {
      font-size: 40px;
      line-height: 150px;
      color: #fff;
      text-indent: 1em;
    }

    .appDownload-header-content {
      position: relative;
      bottom: -68px;
      height: 210px;
      padding-top: 33px;
      padding-left: 225px;
      background: #fff;
      border-radius: 28px;

      .app-icon {
        position: absolute;
        top: -34px;
        left: 30px;
        z-index: 1px;
        width: 175px;
        height: 175px;
        overflow: hidden;
        background-color: #76bfff;
        border-radius: 28px;

        img {
          display: inline-block;
          width: 175px;
          height: 175px;
        }
      }

      .official-logo {
        display: inline-block;
        min-width: 68px;
        height: 38px;
        padding: 0 1em;
        margin-left: 13px;
        font-size: 20px;
        line-height: 38px;
        vertical-align: middle;
        color: #34bff9;
        background: url("./images/official-logo.png") no-repeat;
        background-size: 100% 100%;
      }

      .app-full-name {
        display: inline-block;
        max-width: 200px;

        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 32px;
        line-height: 38px;
        vertical-align: middle;
        color: #2a2b2d;
        white-space: nowrap;
      }

      .app-star-grade {
        height: 25px;
        margin-top: 12px;
        font-size: 24px;

        color: #757575;

        .star-icon {
          display: inline-block;
          width: 24px;
          height: 25px;
          margin-right: 8px;
          background: url("./images/star.png") no-repeat;
          background-size: 24px 25px;
        }

        .grade-text {
          display: inline-block;
          vertical-align: top;
        }
      }

      .app-download-btn {
        position: absolute;
        top: 39px;
        right: 30px;
        height: 64px;
        padding: 0 38px;
        font-size: 26px;
        line-height: 64px;
        color: #fff;
        text-align: center;
        cursor: pointer;
        background: #3887fe;
        border-radius: 32px;
      }
    }
  }

  .appDownload-tabs {
    padding: 0 30px;
    margin-top: 98px;
    line-height: 48px;

    p {
      text-align: justify;
    }

    .view-item {
      position: relative;
      float: left;
      min-width: 50%;
      line-height: 88px;
      color: #5e5e5e;
      text-align: center;
    }

    .am-tabs-ink-bar {
      background-color: unset;
    }

    .am-tabs-bar,
    .am-tabs-tab {
      background-color: transparent;
      border: none;
    }

    .am-tabs-tab-active {
      .view-item {
        &::before {
          position: absolute;
          bottom: 0;
          left: 50%;
          width: 36px;
          height: 6px;
          margin-left: -18px;
          content: "";
          background: #2297ff;
          border-radius: 3px;
        }
      }
    }

    .tips-red {
      color: #ff3636;
    }

    .view-content {
      padding: 0 37px;
      margin-top: 0.5em;
      color: #5e5e5e;

      img {
        width: 100%;
      }
    }
  }

  .donw-load-btn {
    position: fixed;
    bottom: 40px;
    left: 30px;
    width: calc(100% - 60px);
    height: 64px;
    font-size: 26px;
    line-height: 64px;
    color: #fff;
    text-align: center;
    cursor: pointer;
    background: #3887fe;
    border-radius: 32px;
  }

  .app-guide {
    width: 80%;
    margin: 120px auto 0;

    .step {
      width: 100%;
      margin-bottom: 40px;

      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
}
