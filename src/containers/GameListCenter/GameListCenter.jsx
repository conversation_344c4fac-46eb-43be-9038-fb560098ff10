import React from "react";
import { with<PERSON>outer } from "react-router-dom";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";
import Qs from "qs";

import HotGame from "@/components/HotGame/HotGame";
import ProviderGameList from "@/components/ProviderGameList/ProviderGameList";
import VendorGame from "@/components/VendorGame/VendorGame";
import deploy from "@/config/deploy.config";
import { gamePath, pathGameType } from "@/config/game.config";
import { get } from "@/utils/storage";

import "./GameListCenter.scss";

const { gameSelectType, gameListType } = deploy;

@inject("languageShell", "gameCenter", "auth", "common", "tcgCommon")
@withRouter
@observer
class GameListCenter extends React.Component {
  get currentVendors() {
    const data = this.props.gameCenter.gameVendor.mapping[this.state.gameType] || [];
    return data;
  }
  get sorting() {
    return this.props.gameCenter.gameVendor.sorting;
  }
  get noSearch() {
    return !["LOTT", "ELOTT"].includes(this.state.gameType);
  }
  get banners() {
    return this.props.common.banners.filter((item) => item.groupName === deploy.bannerType[this.state.gameType]);
  }
  get menus() {
    return [...this.sorting];
  }

  constructor(props) {
    super(props);
    this.state = {
      gameType: "",
      vassalage: "",
      gameClassify: "",
      dataType: gameSelectType.ALL,
    };
  }
  componentDidMount() {
    window.scrollTo(0, 0);
    this.initGame();
    this.getFavoriteGameId();
    this.getAnnounce();
  }
  getAnnounce = () => {
    const session = get("MC_SESSION_INFO");
    let data = {
      types: "B,PL,PU,PR,H",
      groupName: "",
      platform: "M",
      merchantCode: deploy.merchant,
      token: session ? session.token : "",
    };
    this.props.common.annount(data);
  };
  initGame() {
    this.props.tcgCommon.getGameVendor().then((res) => {
      this.props.gameCenter.setGameVendor(res);
      this.initData();
    });
  }
  initData = () => {
    const url = Qs.parse(window.location.search, {
      ignoreQueryPrefix: true,
    });
    this.setState({
      gameType: pathGameType[location.pathname] || "",
      vassalage: url.vassalage || "",
      gameClassify: url.gameClassify,
      dataType: url.dataType || gameSelectType.ALL,
    });
  };
  getHotGames = (refresh) => {
    const data = {
      isPlatform: 2,
      language: this.props.languageShell.currentLanguage,
      platform: "html5",
    };
    return this.props.gameCenter.getGameHot(data);
  };
  getFavoriteGameId = () => {
    if (this.props.auth.currentToken) {
      this.props.gameCenter.getFavGames({
        token: this.props.auth.currentToken,
        pageNo: 1,
        pageSize: 200,
      });
    }
  };
  handleSearch = () => {
    this.props.common.showSearchPop(true);
  };
  menuClick = (gameCategory) => {
    this.props.history.push(gamePath[gameCategory]);
  };
  handleFilter = () => {
    this.props.common.showCategoryPopup(true);
  };
  headerRight = () => {
    return (
      <div className="header-right">
        <div className="btn-filter" onClick={() => this.props.history.replace("/m/home")}>
          <Icon type={require("!svg-sprite-loader!@/assets/images/common/icon-search.svg")} />
        </div>
      </div>
    );
  };
  renderContent = () => {
    if (this.state.gameType === gameSelectType.HOT) {
      return <HotGame />;
    }
    if (deploy.gameListType.includes(this.state.gameType)) {
      return (
        <div className="game-list-center-content">
          {this.state.gameType && (
            <ProviderGameList
              hasTitle
              gameType={this.state.gameType}
              vassalage={this.state.vassalage}
              gameClassify={this.state.gameClassify}
              dataType={this.state.dataType}
            />
          )}
        </div>
      );
    }
    if ([gameSelectType.FAV, gameSelectType.RECENT].includes(this.state.gameType)) {
      return <ProviderGameList gameType={this.state.gameType} dataType={this.state.gameType} />;
    }
    return (
      <div className="game-list-center-content">
        <VendorGame gameType={this.state.gameType} games={this.currentVendors} />
      </div>
    );
  };
  render() {
    return <div className="game-list-center-container app-main">{this.renderContent()}</div>;
  }
}

export default GameListCenter;
