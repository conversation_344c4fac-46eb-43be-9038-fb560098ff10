.category-popup-wrap {
  .common-popup-content {
    width: 100%;
    background: #f5f5f5;
    border-radius: 50px 50px 0 0;
    box-shadow: 0 11px 0 0 #e06700 inset;

    .popup-content {
      width: 100%;
      padding: 40px 69px 60px;
    }

    .popup-close {
      top: -60px;
      right: 24px;
    }

    .category-popup-title {
      margin-bottom: 40px;
      font-size: 52px;
      font-weight: 700;
      line-height: normal;
      color: #e06700;
      text-align: center;
    }

    .category-nav-wrap {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 30px 33px;
    }

    .category-nav-item {
      display: flex;
      align-items: center;
      height: 66px;
      padding: 5px 15px 5px 40px;
      font-size: 24px;
      font-weight: 600;
      line-height: normal;
      color: #3e3e3e;
      background: #fff;
      border-radius: 50px;

      &.on {
        color: #fff;
        background: #e06700;
      }

      .category-icon {
        width: 46px;
        height: 46px;
        margin-right: 18px;
      }
    }
  }
}
