import React from "react";
import { withRouter } from "react-router-dom";
import cz from "classnames";
import { inject, observer } from "mobx-react";

import { gamePath, navConfig } from "@/config/game.config";
import CommonPopup from "@/ui/CommonPopup/CommonPopup";

import "./GameCategoryPopup.scss";

@inject("languageShell", "gameCenter", "common")
@withRouter
@observer
class GameCategoryPopup extends React.Component {
  get menus() {
    return [{ gameCategory: "HOT" }, ...this.sorting];
  }
  get sorting() {
    return this.props.gameCenter.gameVendor.sorting;
  }
  handleClose = () => {
    this.props.common.showCategoryPopup(false);
  };
  handleGameNav = (gameCategory) => {
    this.props.common.showCategoryPopup(false);
    this.props.history.push(gamePath[gameCategory]);
  };
  render() {
    return (
      <CommonPopup
        wrapClassName="category-popup-wrap"
        show={this.props.common.categoryPopupStatus}
        handleClose={this.handleClose}
        position="bottom"
      >
        <div className="category-popup-content">
          <div className="category-popup-title">{this.props.languageShell.t("game_category")}</div>
          <div className="category-nav-wrap">
            {this.menus.map((item) => {
              return (
                <div
                  className={cz("category-nav-item", {
                    on: location.pathname === gamePath[item.gameCategory],
                  })}
                  key={`category-menu-${item.gameCategory}`}
                  onClick={() => this.handleGameNav(item.gameCategory)}
                >
                  <img className="category-icon" src={navConfig[item.gameCategory]?.img} />

                  <div className="category-name">
                    <span>{this.props.languageShell.t(navConfig[item.gameCategory]?.name)}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </CommonPopup>
    );
  }
}

export default GameCategoryPopup;
