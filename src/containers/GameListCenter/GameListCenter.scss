.game-list-center-container {
  width: 100%;

  .game-list-center-content {
  }

  .game-list-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    margin-bottom: 20px;
    font-size: 28px;
    font-weight: 700;
    line-height: 128%;
    color: #fff;
    text-transform: capitalize;
  }

  .game-select {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 32px;

    .select-bar {
      display: flex;
      flex-shrink: 0;
      align-items: center;

      .select-btn {
        position: relative;
        width: 64px;
        height: 64px;
        margin-right: 16px;
        background: #1d2431;
        border: 2px solid var(--Dark-Grey-Blue, #283667);
        border-radius: 10px;

        @include flex-center;

        .select-icon {
          display: block;
          width: 33px;
          height: 33px;
          fill: #fff;
        }
      }

      .select-btn.on {
        border: 2px solid #154fa0;

        .select-icon {
          fill: #0048ff;
        }
      }
    }

    .provider-btn {
      position: relative;
      display: flex;
      align-items: center;
      height: 64px;
      padding: 0 16px;
      font-size: 24px;
      line-height: normal;
      color: #fff;

      background: #1d2431;
      border: 2px solid var(--Dark-Grey-Blue, #283667);
      border-radius: 10px;
    }

    .icon-open {
      width: 30px;
      height: 30px;
      margin-right: 8px;
    }
  }

  .disable-search {
    position: relative;
    margin-bottom: 24px;

    &::after {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
      content: "";
    }
  }

  .game-pagenation-wrap {
    display: flex;
    justify-content: center;
    margin-top: 32px;

    .game-more {
      @include flex-center;
      height: 70px;
      padding: 0 40px;
      font-size: 24px;

      color: #fff;
      background: linear-gradient(180deg, rgb(60 168 243 / 20%) 0%, rgb(102 202 251 / 20%) 100%);
      border-radius: 10px;
    }

    .pages {
      margin-left: 24px;
      color: #8c9099;
    }

    .page-info {
      color: #fff;
    }
  }
}
