.activity-container {
  .activity-header {
    padding: 24px 20px;
    font-size: 30px;
    font-weight: 500;
    line-height: normal;
    color: #d9d9d9;
    background: #005b37;
  }

  .activity-preview {
    .activity-type {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      font-size: 30px;
      font-weight: 500;
      line-height: normal;
      text-align: center;
      text-shadow: 0 0 3.7px #ffcd00;
      border-bottom: 2px solid #a3a3a3;

      span {
        background: linear-gradient(180deg, #fff 0%, #ffee98 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .activity-preview-bg {
    }
  }

  .activity-sub-title {
    margin-bottom: 16px;
    font-size: 28px;
    line-height: 42px;
    color: #eff4f3;
    text-align: center;
  }

  .game-title {
    margin-bottom: 20px;
  }

  .activity-time {
    display: flex;
    align-items: center;
    margin-top: 12px;
    font-size: 22px;
    line-height: normal;
    color: #9784bf;
  }

  .promo-list {
    display: grid;
    gap: 32px;

    &.accordion-active {
      .accordion-item-bg {
        background: #f7f7f7;
      }

      .promo-item {
        .title {
          color: #000;
        }

        .accordion-arrow {
          color: #5274ff;
          transform: rotate(-180deg);
        }
      }
    }
  }

  .accordion-item-bg {
    overflow: hidden;
    background: #273bb1;
    border: 2px solid rgb(175 175 175 / 20%);
    border-radius: 16px;
    backdrop-filter: blur(25px);
  }

  .activity-item-content {
    padding: 0 20px 20px;
  }

  .activity-content {
    padding: 32px;

    .read-more {
      display: flex;
      gap: 11px;
      align-items: center;
      margin-top: 16px;
      font-size: 24px;
      font-weight: 400;
      color: var(--text-color-accent);

      .am-icon {
        display: block;
        width: 24px;
        height: 24px;
        object-fit: contain;
      }
    }

    .promo-item {
      position: relative;
      min-width: 0;

      .activity-img {
        position: relative;
        width: 100%;
        overflow: hidden;

        img {
          display: block;
          width: 100%;
          min-height: 100px;
        }
      }

      .btn-more {
        position: absolute;
        bottom: 20px;
        left: 20px;
        padding: 10px 40px 14px;
        font-size: 23px;
        font-weight: 500;
        line-height: 26.54px;
        color: #fff;
        text-align: center;
        background: linear-gradient(92deg, #8fa9ff 15.46%, #668bff 85.01%);
        border: 2px solid rgb(255 255 255 / 60%);
        border-radius: 46.16px;
      }

      .title {
        @include trim(1);
        font-size: 28px;
        font-weight: 600;
        line-height: normal;
        color: var(--text-color-primary);
      }

      .activity-info {
        width: 100%;
        padding: 16px;
        overflow: hidden;
        background: var(--bg-color-surface);
      }

      .limit-time {
        padding: 8px 15px;
        margin-right: 6px;
        font-size: 20px;
        font-weight: 500;
        line-height: normal;
        color: #fff;
        background: linear-gradient(135deg, #faa883 0%, #ec7340 100%);
        border-radius: 10px;
      }

      .accordion-arrow {
        display: block;
        width: 44px;
        height: 44px;
        color: #fec628;
        transition: all 0.3s;
      }
    }

    .promo-item-bg {
      position: relative;
      width: 100%;
    }

    .title-content {
      @include trim(6);
      margin-bottom: 12px;
      font-size: 20px;
      line-height: 26px;
      color: #b2b2b2;
    }

    .view-all {
      @include flex-center;
      gap: 16px;
      width: fit-content;
      margin-top: 16px;
      font-size: 28px;
      font-weight: 700;
      color: #27b488;

      .arrow-right {
        display: block;
        width: 20px;
        height: 20px;
      }
    }
  }

  .join-now {
    .activity-goto-btn {
      margin: 30px 0 0;
      font-size: 24px;
      line-height: 80px;
      border-radius: 24px;
    }
  }

  .wysiwyg {
    padding: 0;
    font-size: 24px;
    line-height: normal;
    color: var(--text-color-primary);

    img {
      display: block;
      max-width: 100%;
    }
  }
}

.activity-detail-popup {
  top: 0;
  width: 100%;
}

.activity-detail-container {
  width: 100%;

  .activity-detail-content {
    position: relative;
    padding: 32px;
  }

  .activity-detail-bg {
    position: relative;
  }

  .activity-detail-header {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    width: 100%;
    height: 80px;

    .activity-logo {
      display: block;
      height: 67px;
    }
  }

  .activity-detail-img {
    overflow: hidden;

    img {
      display: block;
      width: 100%;
    }
  }

  .close-detail {
    position: absolute;
    top: -86px;
    right: 24px;
    width: 56px;
    height: 56px;

    .am-icon {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .activity-detail-inner {
  }

  .activity-detail-title {
    margin: 24px 0 0;
    font-size: 32px;
    font-weight: 600;
    line-height: normal;
    color: var(--text-color-primary);
  }

  .detail-date {
    display: flex;
    gap: 4px;
    align-items: center;
    width: fit-content;
    padding: 13.5px 12px;
    margin-top: 16px;
    font-size: 24px;
    font-weight: 400;
    line-height: normal;
    color: var(--text-color-primary);
    background: #1778ff;
  }

  .activity-img {
    position: relative;
    z-index: 10;
    width: 100%;
    overflow: hidden;

    img {
      display: block;
      width: 100%;
    }
  }

  .activity-html {
    margin-top: 16px;
  }

  .activity-goto-btn {
    @include flex-center;
    position: relative;
    min-width: 165px;
    height: 60px;
    padding: 0 32px;
    margin-top: 20px;
    font-size: 28px;
    color: #fff;
    text-align: center;
    background: linear-gradient(180deg, #00b3ff 0%, #36c0f6 100%);
    border-radius: 12px;
  }
}

.container_activity table,
tbody,
td,
tfoot,
th,
thead,
time,
tr,
tt {
  border: 1px solid #fff;
}
