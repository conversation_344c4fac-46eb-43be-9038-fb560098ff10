import React from "react";
import { withRouter } from "react-router";
import { Icon, List } from "antd-mobile";
import dayjs from "dayjs";
import { inject, observer } from "mobx-react";
import PubSub from "pubsub-js";
import Qs from "qs";
import { ActivityGoTo, wysiwygUtil } from "tcg-mobile-common";

import Footer from "@/components/Footer/Footer";
import NoData from "@/components/NoData/NoData";
import Header from "@/components/OutterHeader/Header";
import HomeHeader from "@/components/OutterHeader/HomeHeader";
import deploy from "@/config/deploy.config";

// Import Common Stylesheets
import "./Activity.scss";
import "./ActivityMenu.scss";

@inject("common", "auth", "languageShell", "tcgCommon", "gameCenter")
@withRouter
@observer
class Activity extends React.Component {
  get promoMenu() {
    return this.props.tcgCommon.promotionCategory;
  }
  get promoData() {
    return (this.props.common.activity || []).filter((el) => {
      if (!this.state.activeType?.id) return true;
      return el.promoAnnouncementCategoryIds.includes(this.state.activeType?.id);
    });
  }
  get menus() {
    return this.props.gameCenter.gameVendor.sorting;
  }
  constructor(props) {
    super(props);
    this.state = {
      activeType: {},
      activeKey: "",
      activeItem: {},
      showDetail: false,
    };
    this.menuItems = {};
  }
  componentDidMount() {
    window.scrollTo(0, 0);
    this.setActiveItem();
    this.getAnnouncement().then(() => {
      this.setActiveItem();
    });
    this.changeLanguage = PubSub.subscribe("changeLanguage", this.getAnnouncement);
  }
  componentWillUnmount() {
    PubSub.unsubscribe(this.changeLanguage);
  }
  getAnnouncement = () => {
    let data = {
      types: deploy.contentTypes.Promotion,
      merchantCode: deploy.merchant,
      platform: "M",
    };
    return this.props.common.annount(data, true);
  };
  setActiveItem = () => {
    if (!this.getActivePromoId()) return;
    const activeItem = this.promoData.find((el) => el.id === this.getActivePromoId());
    this.setState({
      activeItem,
      showDetail: !!activeItem,
    });
  };
  filterPromo = (activeType) => {
    this.setState({
      activeType,
    });
  };
  getActivePromoId() {
    const params = new URLSearchParams(this.props.history.location.search);
    return params.has("id") ? +params.get("id") : undefined;
  }
  setActiveRef = (id) => (c) => {
    if (id === +this.props.location.query) {
      this.activeRef = c;
      if (this.activeRef) {
        setTimeout(() => {
          window.scrollTo(0, this.activeRef.getBoundingClientRect().top);
        }, 0);
      }
    }
  };
  preloader() {
    return <div className="loading-example" />;
  }
  renderHeader = (ret) => {
    return (
      <div className="promo-item" key={ret.id} ref={this.setActiveRef(ret.id)}>
        <div className="activity">
          <div className="activity-img">
            <img src={this.getImage(ret)} alt={ret.title} />
          </div>
          <div className="activity-info">
            <div className="title">{ret.title}</div>
            {/* {this.renderTime(ret)} */}
            {/* <div className="title-content">{this.getContent(ret.content)}</div> */}
            <div className="activity-more">
              <Icon className="arrow-down" type={require("!svg-sprite-loader!@/assets/images/common/arrow-down.svg")} />
            </div>
          </div>
        </div>
      </div>
    );
  };
  renderDetailDate = (item) => {
    const { noExpiry, startDate, endDate } = item;
    if (noExpiry === 1) {
      return (
        <div className="detail-date">
          <span>{this.props.languageShell.t("long_term")}</span>
        </div>
      );
    }
    if (endDate && startDate !== endDate) {
      return (
        <div className="detail-date">
          <span>{this.props.languageShell.t("valid_until")}</span>
          <span>{dayjs(endDate).format("DD MMMM YYYY")}</span>
        </div>
      );
    }
    return null;
  };
  renderTime = (ret) => {
    const s = dayjs(ret.startDate).format("YYYY/MM/DD");
    const e = dayjs(ret.endDate).format("YYYY/MM/DD");
    if (ret.noExpiry === 1) {
      return (
        <div className="activity-time">
          <span>{`${s}-${this.props.languageShell.t("long_term")}`}</span>
        </div>
      );
    }

    let msg = s;
    if (s !== e) {
      msg += `-${e}`;
    }
    return (
      <div className="activity-time">
        <span>{msg}</span>
      </div>
    );
  };
  checkDetail = (item) => {
    window.scrollTo(0, 0);
    const { id } = item;
    if (id) {
      return this.props.history.push(`/m/activityDetail?id=${id}`);
    }
    // this.setState({
    //   activeItem: item,
    //   showDetail: true,
    // });
  };
  renderContent = (ret) => {
    return (
      <List className="my-list my-content">
        <div
          dangerouslySetInnerHTML={{
            __html: wysiwygUtil.convertContent(ret.content),
          }}
        />
        {/* <div className="join-now">
          <ActivityGoTo item={this.state.activeItem} background="#21c7f3" color="#fff" />
        </div> */}
      </List>
    );
  };
  getImage(item) {
    const { announcementImages } = item;
    return ((announcementImages || []).find(({ sequence }) => sequence === 1) || {})?.url;
  }
  getContent = (content) => {
    const temp = document.createElement("div");
    temp.style.display = "none";
    temp.innerHTML = content;
    const convert = temp.textContent || temp.innerText;
    temp.remove();
    return convert;
  };
  closeDetail = () => {
    this.setState({
      activeItem: {},
      showDetail: false,
    });
  };
  headerRight = () => {
    return (
      <div className="header-right">
        <Icon className="btn-search" type={require("!svg-sprite-loader!@/assets/images/common/search-btn.svg")} />
      </div>
    );
  };
  updateData = (data) => {
    this.setState({
      promoData: data,
    });
  };
  closePopup = () => {
    const url = Qs.parse(window.location.search, {
      ignoreQueryPrefix: true,
    });
    if (url.id) {
      return this.props.push("/m/activity");
    }
    this.setState({
      showDetail: false,
    });
  };
  handleScroll = () => {
    this.props.common.showFooterMenu(false);
  };
  render() {
    const activeType = this.state.activeType;
    return (
      <div className="activity-container app-main">
        {this.state.showDetail ? (
          <div className="activity-detail-container">
            <Header />
            <div className="activity-detail-content">
              <div className="activity-detail-bg">
                <div className="activity-detail-img">
                  <img src={this.getImage(this.state.activeItem)} alt={this.state.activeItem?.title} />
                </div>
                <div className="activity-detail-title">{this.state.activeItem?.title}</div>
                {this.renderDetailDate(this.state.activeItem)}
                <div
                  className="activity-html"
                  dangerouslySetInnerHTML={{
                    __html: wysiwygUtil.convertContent(this.state.activeItem?.content),
                  }}
                />
                <ActivityGoTo item={this.state.activeItem} background="#00AEEF" color="#fff" />
              </div>
            </div>
          </div>
        ) : (
          <div className="activity-preview avoid-footer">
            <HomeHeader />
            <div className="activity-preview-bg">
              {/* {this.promoMenu.length > 0 && (
                <div className="activity-menu-wrap">
                  <div className="activity-menu-scroll">
                    {this.promoMenu.map((item, index) => {
                      return (
                        <div
                          ref={(el) => {
                            if (el) {
                              this.menuItems[item.id] = el;
                              el.classList.add("measure");
                              const width = el.offsetWidth;
                              el.classList.remove("measure");
                              if (width > remToPx(2.3)) {
                                el.classList.add("wide");
                              } else {
                                el.classList.remove("wide");
                              }
                            }
                          }}
                          className={cz("activity-menu-item", {
                            on: (index === 0 && !activeType?.id) || activeType?.id === item.id,
                          })}
                          key={`activity-menu-${index}`}
                          onClick={() => this.filterPromo(item)}
                        >
                          <span>{this.props.languageShell.t(item.title)}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )} */}
              <div className="activity-content">
                {this.promoData.length > 0 && (
                  // <Accordion activeKey={this.state.activeKey} className="collapse-accordion my-activity">
                  //   {this.promoData.map((ret, idx) => {
                  //     return (
                  //       <AccordionItem className="promo-list" data-key={ret.id}>
                  //         <div className="promo-item" key={ret.id} ref={this.setActiveRef(ret.id)}>
                  //           <div className="activity">
                  //             <div className="activity-img">
                  //               <img src={this.getImage(ret)} alt={ret.title} />
                  //             </div>
                  //             <div className="activity-info">
                  //               <div className="title">{ret.title}</div>
                  //               {/* {this.renderTime(ret)} */}
                  //               {/* <div className="title-content">{this.getContent(ret.content)}</div> */}

                  //               <Icon
                  //                 className="accordion-arrow"
                  //                 type={require("!svg-sprite-loader!@/assets/images/common/arrow-down.svg")}
                  //               />
                  //             </div>
                  //           </div>
                  //         </div>
                  //         <div className="activity-item-content">
                  //           <div
                  //             dangerouslySetInnerHTML={{
                  //               __html: wysiwygUtil.convertContent(ret.content),
                  //             }}
                  //           />
                  //           <div className="join-now">
                  //             <ActivityGoTo item={this.state.activeItem} background="#21c7f3" color="#fff" />
                  //           </div>
                  //         </div>
                  //       </AccordionItem>
                  //     );
                  //   })}
                  // </Accordion>
                  <div className="promo-list">
                    {this.promoData.map((ret, idx) => {
                      return (
                        <div
                          className="promo-item"
                          key={ret.id}
                          ref={this.setActiveRef(ret.id)}
                          onClick={() => this.checkDetail(ret)}
                        >
                          <div className="promo-item-bg">
                            <div className="activity-img">
                              <img src={this.getImage(ret)} alt={ret.title} />
                            </div>
                            <div className="activity-info">
                              <div className="title">
                                <span>{ret.title}</span>
                              </div>
                              <div className="read-more">
                                <span>{this.props.languageShell.t("read_more")}</span>
                                <Icon type={require("!svg-sprite-loader!@/assets/images/common/arrow-right.svg")} />
                              </div>
                              {/* <div className="title-content">{this.getContent(ret.content)}</div> */}
                              {/* {this.renderTime(ret)} */}
                              {/* <RewardBtn promo={ret} promoData={this.state.promoData} updateData={this.updateData} /> */}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
                {this.state.noData && <NoData />}
              </div>
            </div>
            <Footer />
          </div>
        )}
      </div>
    );
  }
}
export default Activity;
