import React from "react";
import { with<PERSON><PERSON><PERSON> } from "react-router";
import cz from "classnames";
import { isEqual } from "lodash";
import { inject, observer } from "mobx-react";
import PropTypes from "prop-types";
import MC from "tcgmodulemc";

import { remToPx } from "@/utils/dom";

import "./ActivityMenu.scss";

@inject("common", "languageShell", "gameCenter")
@withRouter
@observer
class ActivityMenu extends React.Component {
  constructor(props) {
    super(props);
    this.swiper = null;
  }
  componentDidMount() {
    if (this.props.menus.length) {
      this.initSwiper();
    }
    this.props.setRef && this.props.setRef(this);
  }
  componentDidUpdate(prevProps, prevState) {
    if (!isEqual(prevProps.menus, this.props.menus)) {
      this.initSwiper();
    }
  }
  componentWillUnmount() {
    this.swiper?.destroy?.();
  }
  initSwiper = () => {
    if (this.swiper) {
      this.swiper?.destroy(true, true);
    }
    this.swiper = new MC.Swiper(this.menuSwiper, {
      observer: true,
      observeParents: true,
      freeMode: true,
      freeModeMomentumRatio: 0.5,
      slidesPerView: "auto",
      spaceBetween: remToPx(0.2),
      navigation: {
        nextEl: this.nextNav,
        prevEl: this.prevNav,
      },
    });
  };
  tabChange = (item, index) => {
    this.props.menuClick(item);
    this.moveToCenter(index);
  };
  moveToCenter = (index) => {
    if (this.swiper) {
      const swiperWidth = this.swiper.width;
      const maxTranslate = this.swiper.maxTranslate();
      const maxWidth = -maxTranslate + swiperWidth / 2;
      const slide = this.swiper.slides[index];
      let slideLeft = slide.offsetLeft;
      let slideWidth = slide.clientWidth;
      let slideCenter = slideLeft + slideWidth / 2;

      this.swiper.setTransition(200);

      if (slideCenter < swiperWidth / 2) {
        this.swiper.setTranslate(0);
      } else if (slideCenter > maxWidth) {
        this.swiper.setTranslate(maxTranslate);
      } else {
        const nowTlanslate = slideCenter - swiperWidth / 2;
        this.swiper.setTranslate(-nowTlanslate);
      }
    }
  };
  render() {
    return (
      <div className={cz("activity-menu-wrap")}>
        <div className="activity-menu-content">
          <div className="activity-menu-scroll">
            {/* <div className="menu-nav nav-prev" ref={(e) => (this.prevNav = e)}>
              <Icon type={require("!svg-sprite-loader!@/assets/images/common/nav-left.svg")} />
            </div>
            <div className="menu-nav nav-next" ref={(e) => (this.nextNav = e)}>
              <Icon type={require("!svg-sprite-loader!@/assets/images/common/nav-right.svg")} />
            </div> */}
            <div className="swiper-container  activity-menu-swiper" ref={(c) => (this.menuSwiper = c)}>
              <div className="swiper-wrapper">
                {this.props.menus.map((item, index) => {
                  return (
                    <div
                      key={`activity-menu-${index}`}
                      className={`activity-menu-item swiper-slide ${cz({
                        on: (index === 0 && !this.props.activeType?.id) || this.props.activeType?.id === item.id,
                      })}`}
                      onClick={() => this.tabChange(item, index)}
                    >
                      <div className="activity-menu-name">{this.props.languageShell.t(item.title)}</div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

ActivityMenu.propTypes = {
  menus: PropTypes.array.isRequired,
  menuClick: PropTypes.func.isRequired,
  activeType: PropTypes.string.isRequired,
};

ActivityMenu.defaultProps = {
  menus: [],
};

export default ActivityMenu;
