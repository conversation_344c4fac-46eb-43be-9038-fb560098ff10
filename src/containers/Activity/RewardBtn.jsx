import React from "react";
import { with<PERSON>outer } from "react-router";
import { Modal, Toast } from "antd-mobile";
import { inject, observer } from "mobx-react";

import accountApi from "../../apis/account";

import "./RewardBtn.scss";

@inject("languageShell", "mcLanguage")
@withRouter
@observer
class RewardBtn extends React.Component {
  applyPrompt = () => {
    Modal.alert(this.props.languageShell.t("in_popup_prompt"), this.props.languageShell.t("apply_success"), [
      {
        text: this.props.languageShell.t("in_increase_submit"),
      },
    ]);
  };
  openModal = (e) => {
    e.stopPropagation();
    const { imageRequired, remarkRequired, linkedPromotionId, id } = this.props.promo;
    if (imageRequired === "N" && remarkRequired === "N") {
      return this.requestReward(linkedPromotionId);
    }
    this.props.history.push(`/m/applyReward?id=${id}`);
  };
  applyReward = (e) => {
    e.stopPropagation();
    const { linkedPromotionId, signUpRequestRewardType } = this.props.promo;
    Toast.loading(this.props.languageShell.t("loading"), 10);
    accountApi
      .signUpPromotionJoin({
        promotionId: linkedPromotionId,
      })
      .then((res) => {
        if (res.data.success) {
          const result = this.props.promoData.map((item) =>
            item.linkedPromotionId === linkedPromotionId
              ? { ...item, signUpStatus: signUpRequestRewardType === "O" ? "PENDING" : "JOINED" }
              : item
          );
          this.props.updateData(result);
          this.applyPrompt();
        }
      })
      .finally(() => {
        Toast.hide();
      });
  };
  requestReward = (id) => {
    Toast.loading(this.props.languageShell.t("loading"), 10);
    accountApi
      .signUpRequestReward({
        promotionId: id,
      })
      .then((res) => {
        if (res.data.success) {
          const result = this.props.promoData.map((item) =>
            item.linkedPromotionId === id ? { ...item, signUpStatus: "PENDING" } : item
          );
          this.props.updateData(result);
          this.applyPrompt();
        }
      })
      .finally(() => {
        Toast.hide();
      });
  };
  render() {
    // if (!get("MC_SESSION_INFO")) {
    //   return null;
    // }
    const { signUpStatus, signUpRequestRewardType } = this.props.promo;
    if (!signUpStatus) {
      return (
        <div className="activity-more">
          <span>{this.props.languageShell.t("in_more_button")}</span>
        </div>
      );
    }

    switch (signUpStatus) {
      case "ALLOW_JOIN":
        return (
          <span className="reward_btn join" onClick={this.applyReward}>
            {this.props.languageShell.t("activity_join")}
          </span>
        );
      case "JOINED":
        if (signUpRequestRewardType === "O") {
          return <span className="reward_btn padding">{this.props.languageShell.t("activity_applying")}</span>;
        } else {
          return (
            <span className="reward_btn claim" onClick={this.openModal}>
              {this.props.languageShell.t("activity_claim")}
            </span>
          );
        }
      case "PENDING":
        return <span className="reward_btn padding">{this.props.languageShell.t("activity_applying")}</span>;
      case "REJECTED":
        return <span className="reward_btn due">{this.props.languageShell.t("activity_rejected")}</span>;
      case "ISSUED":
        return <span className="reward_btn padding">{this.props.languageShell.t("activity_issued")}</span>;
      case "CLAIMED":
        return <span className="reward_btn padding">{this.props.languageShell.t("activity_claimed")}</span>;
      case "SIGN_UP_DUE":
        return <span className="reward_btn due">{this.props.languageShell.t("activity_close")}</span>;
      default:
        return null;
    }
  }
}

export default RewardBtn;
