.activity-menu-wrap {
  position: relative;
  flex-shrink: 0;
  width: 100%;
  padding: 25px 14px;
  overflow: hidden;

  .activity-menu-scroll {
    position: relative;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    width: 100%;
  }

  .activity-menu-swiper {
    width: 100%;
    overflow: hidden;
  }

  .activity-menu-item {
    @include flex-center;
    position: relative;
    flex-shrink: 0;
    height: 52px;
    font-size: 23.864px;
    font-weight: 600;
    line-height: normal;
    color: #fff;
    text-align: center;
    text-transform: uppercase;

    &.measure {
      position: absolute;
      visibility: hidden;
      width: auto;
      white-space: nowrap;
    }

    &.wide {
      grid-column: span 2;
      // background: url(~@/assets/images/bg/lg-round-btn.png) no-repeat center/100% 100%;
    }

    &.on {
    }
  }

  .menu-nav {
    position: absolute;
    top: 29px;
    z-index: 10;
    width: 42px;
    height: 42px;
    color: #fff;
    // &.swiper-button-disabled {
    //   display: none;
    // }
    .am-icon {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .nav-prev {
    left: 24px;
  }

  .nav-next {
    right: 24px;
  }
}
