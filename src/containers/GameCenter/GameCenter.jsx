import React from "react";
import { withRouter } from "react-router-dom";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";

import Header from "@/components/OutterHeader/Header";
import VendorGame from "@/components/VendorGame/VendorGame";
import deploy from "@/config/deploy.config";
import { pathGameType } from "@/config/game.config";

import "./GameCenter.scss";

@inject("common", "mcCommon", "languageShell", "gameCenter", "tcgCommon")
@withRouter
@observer
class GameCenter extends React.Component {
  get currentVendors() {
    const data = this.props.gameCenter.gameVendor.mapping[this.state.gameType] || [];
    return data;
  }
  get sorting() {
    return this.props.gameCenter.gameVendor.sorting;
  }
  get basicSportGame() {
    return (this.props.gameCenter.gameVendor.mapping["SPORTS"] || []).filter(
      (item) => ![...deploy.esportGames, ...deploy.cockfightGames].includes(item.vassalage)
    );
  }
  get esportGame() {
    return (this.props.gameCenter.gameVendor.mapping["SPORTS"] || []).filter((item) =>
      deploy.esportGames.includes(item.vassalage)
    );
  }
  get cockfightGame() {
    return (this.props.gameCenter.gameVendor.mapping["SPORTS"] || []).filter((item) =>
      deploy.cockfightGames.includes(item.vassalage)
    );
  }
  get banners() {
    return this.props.common.banners.filter((item) => item.groupName === deploy.bannerType[this.state.gameType]);
  }
  get matchs() {
    return this.props.tcgCommon.gameMatches;
  }
  state = {
    gameType: "",
  };
  componentDidMount() {
    window.scrollTo(0, 0);
    this.initGame();
    this.getAnnount();
  }
  initGame() {
    this.props.tcgCommon.getGameVendor().then((res) => {
      this.props.gameCenter.setGameVendor(res);
      this.setState({
        gameType: pathGameType[location.pathname] || "",
      });
    });
  }
  getAnnount = () => {
    let data = {
      types: "B",
      groupName: "",
      platform: "M",
      merchantCode: deploy.merchant,
    };
    this.props.common.annount(data);
  };
  handleFilter = () => {
    this.props.common.showCategoryPopup(true);
  };
  headerRight = () => {
    return (
      <div className="header-right">
        <div className="btn-filter" onClick={this.handleFilter}>
          <Icon type={require("!svg-sprite-loader!@/assets/images/common/icon-filter.svg")} />
        </div>
      </div>
    );
  };
  render() {
    return (
      <div className={`game-center-container app-main ${this.state.gameType}`}>
        <Header
          title={this.props.languageShell.t("in_game_center")}
          rightContent={this.headerRight()}
          handleGoBack={() => this.props.history.replace("/m/home")}
        />
        <div className="game-center-content">
          <VendorGame gameType={this.state.gameType} games={this.currentVendors} />
        </div>
      </div>
    );
  }
}

export default GameCenter;
