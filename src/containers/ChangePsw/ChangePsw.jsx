import React from "react";
import { Icon, Modal, Toast } from "antd-mobile";
import cz from "classnames";
import { Base64 } from "js-base64";
import { inject, observer } from "mobx-react";
import { createForm } from "rc-form";
import { AutoIcon } from "tcg-mobile-common";
import { utils } from "tcgmodulemc";

import FormService from "@/components/OnlineService/FormService";
import { formConfig } from "@/config/form.config";
import { inputScrollIntoView } from "@/utils/navigatorUtil";
import { get } from "@/utils/storage";

import "./ChangePsw.scss";

class ChangePassword extends React.Component {
  constructor(props) {
    super(props);
    this.alert = Modal.alert;

    const { userName } = get("MC_SESSION_INFO") || {};
    this.state = {
      changeUsername: userName,
      password: "",
      oldPassword: "",
      disableSubmit: true,
      showOldPass: false,
      showPass: false,
      showConfirmPass: false,
    };
  }

  componentDidMount() {
    if (!get("MC_SESSION_INFO")) {
      this.props.replace("/m/home");
    }
  }

  handleConfirmPassword = (rule, value, callback) => {
    const { getFieldValue } = this.props.form;
    if (value && value !== getFieldValue("password")) {
      callback(this.props.languageShell.resource["in_confirmation_correct"]);
    }
    const currPass = getFieldValue("password");
    const currConfirm = getFieldValue("confimpsw");
    if (currPass && currConfirm) {
      if (currPass !== currConfirm) {
        callback(this.props.languageShell.resource["in_confirmation_correct"]);
      }
    }
    // Note: 必须总是返回一个 callback，否则 validateFieldsAndScroll 无法响应
    callback();
  };

  handleSubmit = () => {
    let self = this;

    this.props.form.validateFields(async (error, value) => {
      if (error) {
        return;
      }
      let data = {
        oldPassword: this.props.auth.psw || value.old_password,
        newPassword: value.password,
        confirmNewPassword: value.confimpsw,
      };
      if (data.newPassword !== data.oldPassword) {
        Toast.loading(this.props.languageShell.resource["in_please_wait"], 10);
        this.props.seculist
          .passWordChangeV2(data)
          .then(() => {
            Toast.hide();
            Toast.success(this.props.languageShell.resource["in_chang_saved"], 1); // 有些品牌有用多语系
            window.sessionStorage.removeItem("PSW");
            this.props.auth.setChangePsw(data.newPassword);
            this.updateRemPwd(data.newPassword).then(() => {
              setTimeout(() => {
                self.props.replace("/m/home");
                self.props.common.showChangePsw(false);
              }, 1000);
            });
          })
          .catch(() => {
            Toast.hide();
          });
      } else {
        this.alert(
          this.props.languageShell.resource["in_popup_prompt"],
          this.props.languageShell.resource["same_password"],
          [{ text: this.props.languageShell.resource["in_increase_submit"], onPress: () => {} }]
        );
      }
    });
  };
  updateRemPwd = async (password) => {
    try {
      const { userName } = get("MC_SESSION_INFO") || {};
      if (!userName) {
        return false;
      }

      // save pass
      const sass = await utils.appStorageUtils.storageGetItem("sass");
      const remPass = await utils.appStorageUtils.storageGetItem("remPass");
      if (sass && remPass) {
        const value = {
          ...JSON.parse(remPass),
          password: Base64.encode(Base64.encode(`${password}${sass}`)),
        };
        utils.appStorageUtils.storageSetItem("remPass", JSON.stringify(value));
      }
    } catch (e) {
      console.warn("updateRemPwd failed", e);
      return false;
    }
    return true;
  };
  handleValidate = (field) => (rule, value, callback) => {
    const setting = this.props.mcCommon.regExp[field];
    if (setting && !setting.required && !value) {
      callback();
      return;
    }
    const result = this.props.mcCommon.verification(value || "", field);
    if (result.pass) {
      callback();
      return;
    }
    callback([new Error(result.message)]);
  };
  inputFocus = (e) => {
    inputScrollIntoView(e.target);
  };
  handleInputChange = (fieldName, value) => {
    const { setFieldsValue, validateFields, getFieldValue } = this.props.form;

    setFieldsValue({ [fieldName]: value });

    if (fieldName === "password" && getFieldValue("confimpsw")) {
      validateFields(["confimpsw"], { force: true });
    }

    // validateFields((errors, values) => {
    //   this.setState({
    //     disableSubmit: Object.values(values).some((value) => !value),
    //   });
    // });
  };
  toggleState = (name) => {
    this.setState({
      [name]: !this.state[name],
    });
  };
  render() {
    const { getFieldProps, getFieldError, getFieldDecorator, getFieldValue } = this.props.form;
    return (
      <div className="form-page-container change-password-container app-main">
        <div className="form-wrap">
          {/* <div className="form-header">
              <CommonLogo className="form-logo" src={require("@/assets/images/logo/logo.png")} />
            </div> */}
          <div className="form-content">
            <form className="outter-form" autoComplete="off">
              <div className="form-title">
                <h6 className="font-bsr">{this.props.languageShell.t("change_password")}</h6>
                <p className="form-sub-title">{this.props.languageShell.t("in_first_password")}</p>
              </div>
              <div className="form-group-wrap">
                {this.state.changeUsername && (
                  <div className="form-group">
                    {/* <div className="form-label">{this.props.languageShell.t("in_increase_username")}</div> */}
                    <div className="fixed-list">
                      <input
                        key="username"
                        type="text"
                        className={`form-control`}
                        autoComplete="off"
                        placeholder={this.props.languageShell.t("in_increase_username")}
                        name="username"
                        ref={(c) => {
                          this.username = c;
                        }}
                        value={this.state.changeUsername}
                        disabled="disabled"
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.username?.icon} />
                      </i>
                    </div>
                    <div className="panel panel-default" />
                  </div>
                )}
                {!this.props.auth.psw && (
                  <div
                    className={cz("form-group", {
                      form_error: getFieldError("old_password"),
                      form_success: getFieldValue("old_password") && !getFieldError("old_password"),
                    })}
                  >
                    {/* <div className="form-label">{this.props.languageShell.t("in_increase_oldPwd")}</div> */}
                    <div className="fixed-list">
                      <input
                        type={this.state.showOldPass ? "text" : "password"}
                        autoComplete="off"
                        className={`form-control`}
                        name="old_password"
                        placeholder={this.props.languageShell.t("in_increase_oldPwd")}
                        ref={(c) => {
                          this.old_password = c;
                        }}
                        {...getFieldProps("old_password", {
                          onChange: (e) => {
                            const value = e.target.value;
                            this.handleInputChange("old_password", value);
                          },
                          onFocus: this.inputFocus,
                          rules: [{ validator: this.handleValidate("loginPassword") }],
                        })}
                      />
                      <i className="input-icon">
                        <AutoIcon icon={formConfig.password?.icon} />
                      </i>
                      <span
                        className={`right-icon ${this.state.showOldPass ? "on" : "off"}`}
                        onClick={() => this.toggleState("showOldPass")}
                      >
                        <Icon
                          type={
                            this.state.showOldPass
                              ? require("!svg-sprite-loader!@/assets/images/form/eye-open.svg")
                              : require("!svg-sprite-loader!@/assets/images/form/eye-closed.svg")
                          }
                        />
                      </span>
                    </div>
                    <div className="panel panel-default">
                      <p>{getFieldError("old_password")?.join(",")}</p>
                    </div>
                  </div>
                )}

                <div
                  className={cz("form-group", {
                    form_error: getFieldError("password"),
                    form_success: getFieldValue("password") && !getFieldError("password"),
                  })}
                >
                  {/* <div className="form-label">{this.props.languageShell.t("in_increase_password")}</div> */}
                  <div className="fixed-list">
                    <input
                      type={this.state.showPass ? "text" : "password"}
                      autoComplete="off"
                      className={`form-control`}
                      name="password"
                      placeholder={this.props.languageShell.t("in_increase_password")}
                      ref={(c) => {
                        this.password = c;
                      }}
                      {...getFieldProps("password", {
                        onChange: (e) => {
                          const value = e.target.value;
                          this.handleInputChange("password", value);
                        },
                        onFocus: this.inputFocus,
                        rules: [
                          { validator: this.handleValidate("password") },
                          { validator: this.handleConfirmPassword },
                        ],
                      })}
                    />
                    <i className="input-icon">
                      <AutoIcon icon={formConfig.password?.icon} />
                    </i>
                    <span
                      className={`right-icon ${this.state.showPass ? "on" : "off"}`}
                      onClick={() => this.toggleState("showPass")}
                    >
                      <Icon
                        type={
                          this.state.showPass
                            ? require("!svg-sprite-loader!@/assets/images/form/eye-open.svg")
                            : require("!svg-sprite-loader!@/assets/images/form/eye-closed.svg")
                        }
                      />
                    </span>
                  </div>
                  <div className="panel panel-default">
                    <p>{getFieldError("password")?.join(",")}</p>
                  </div>
                </div>
                <div
                  className={cz("form-group", {
                    form_error: getFieldError("confimpsw"),
                    form_success: getFieldValue("confimpsw") && !getFieldError("confimpsw"),
                  })}
                >
                  {/* <div className="form-label">{this.props.languageShell.t("in_increase_verifyPwd")}</div> */}
                  <div className="fixed-list">
                    <input
                      type={this.state.showConfirmPass ? "text" : "password"}
                      autoComplete="off"
                      className={`form-control`}
                      name="confimpsw"
                      placeholder={this.props.languageShell.t("in_increase_verifyPwd")}
                      ref={(c) => {
                        this.confimpsw = c;
                      }}
                      {...getFieldProps("confimpsw", {
                        onChange: (e) => {
                          const value = e.target.value;
                          this.handleInputChange("confimpsw", value);
                        },
                        onFocus: this.inputFocus,
                        rules: [
                          { required: true, message: this.props.languageShell.resource["in_enter_password"] },
                          { validator: this.handleConfirmPassword },
                        ],
                      })}
                    />
                    <i className="input-icon icon_password">
                      <AutoIcon icon={formConfig.password?.icon} />
                    </i>
                    <span
                      className={`right-icon ${this.state.showConfirmPass ? "on" : "off"}`}
                      onClick={() => this.toggleState("showConfirmPass")}
                    >
                      <Icon
                        type={
                          this.state.showConfirmPass
                            ? require("!svg-sprite-loader!@/assets/images/form/eye-open.svg")
                            : require("!svg-sprite-loader!@/assets/images/form/eye-closed.svg")
                        }
                      />
                    </span>
                  </div>
                  <div className="panel panel-default">
                    <p>{getFieldError("confimpsw")?.join(",")}</p>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div className="form-actions">
            <div className="btn-group">
              <button type="button" onClick={this.handleSubmit} className="submit-btn forgot-btn">
                {this.props.languageShell.t("in_increase_submit")}
              </button>
            </div>
            <FormService />
          </div>
        </div>
      </div>
    );
  }
}
export default inject(
  "common",
  "mcCommon",
  "auth",
  "seculist",
  "languageShell"
)(createForm()(observer(ChangePassword)));
