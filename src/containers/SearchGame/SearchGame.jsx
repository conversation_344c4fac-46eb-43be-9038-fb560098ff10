import React from "react";
import { withRouter } from "react-router";
import { inject, observer } from "mobx-react";

import Footer from "@/components/Footer/Footer";
import HomeFooter from "@/components/Footer/HomeFooter";
import GameSearch from "@/components/GameSearch/GameSearch";
import HomeHeader from "@/components/OutterHeader/HomeHeader";

import "./SearchGame.scss";

@inject("languageShell", "common", "gameCenter", "auth")
@withRouter
@observer
class SearchGame extends React.Component {
  componentDidMount() {
    window.scrollTo(0, 0);
  }

  render() {
    return (
      <div className="search-game-container app-main avoid-footer">
        <HomeHeader />
        <div className="game-search-content">
          <GameSearch />
          <HomeFooter />
        </div>
        <Footer />
      </div>
    );
  }
}

export default SearchGame;
