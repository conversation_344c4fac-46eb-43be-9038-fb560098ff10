.service-page-container {
  .shell-header {
    .mc-navbar-blue {
      position: relative !important;
      background: transparent !important;

      .shell-return-icon {
        color: var(--text-color-third) !important;
      }
    }
  }

  .service-page-content {
    padding: 23px 0;
  }

  .service-title {
    display: flex;
    flex-direction: column;
    gap: 40px;
    align-items: center;

    .service-logo {
      height: 69px;
    }

    .service-text {
      height: 52px;
    }
  }

  .app-download-wrap {
    position: relative;
    display: flex;
    justify-content: flex-end;
    padding: 0 42px;
    margin-top: 73px;

    .download-banner {
      position: absolute;
      top: -19px;
      left: 0;
      width: 451px;
    }

    .download-tip {
      width: 280px;
      height: 68px;
      padding: 19.16px 13.472px 18.998px 13.552px;
      margin-top: 29px;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      color: var(--text-color-third);
      text-align: center;
      background: #effbff;
      border: 1.705px solid #fff;
      border-radius: 10.65px;
      box-shadow: 0 0 6.307px 0 #8ac5da inset;
    }

    .qrcode-list {
      display: flex;
      gap: 11px;
      align-items: center;
    }

    .download-item {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .download-item-bg {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 133px;
      height: 166px;
      padding-top: 20px;

      &::before {
        position: absolute;
        top: -20px;
        left: -20px;
        width: 173px;
        height: 206px;
        content: "";
        background: url(~@/assets/images/service/qrcode-bg.png) no-repeat center/cover;
      }
    }

    .qr-code {
      position: relative;
      z-index: 1;
    }

    .sys-name {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      font-size: 15px;
      font-weight: 400;
      line-height: normal;
      text-align: center;

      .icon-sys {
        width: 25px;
        height: 30px;
        margin-right: 5px;
        object-fit: contain;
      }
    }

    .download-btn {
      @include flex-center;
      position: relative;
      z-index: 1;
      width: 102px;
      height: 31px;
      margin-top: 10px;
      font-size: 13px;
      font-weight: 600;
      line-height: normal;
      color: #fff;
      text-align: center;
    }
  }

  .service-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px 17px;
    padding: 0 26px;
    margin-top: 42px;

    .service-item {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      min-width: 0;
      height: 52px;
      font-size: 17px;
      font-weight: 700;
      line-height: normal;
      color: var(--text-color-third);
      text-align: center;
      background: url(~@/assets/images/service/item-bg.png) no-repeat center/100% 100%;
    }

    .service-icon {
      @include flex-center;
      flex-shrink: 0;
      width: 62px;
      height: 62px;
      background: url(~@/assets/images/service/service-icon-bg.png) no-repeat center/100% 100%;

      .am-icon {
        display: block;
        width: 40px;
        height: 40px;
        object-fit: contain;
      }
    }

    .service-name {
      flex: 1;
      padding-right: 5px;
    }
  }
}
