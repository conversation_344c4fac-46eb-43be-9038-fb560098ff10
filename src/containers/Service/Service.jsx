import React from "react";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";
import QRCode from "qrcode.react";

import Header from "@/components/OutterHeader/Header";
import SpeedTest from "@/components/SpeedTest/SpeedTest";
import withDownload from "@/hoc/withDownload";
import withMemberMenu from "@/hoc/withMemberMenu";

import { serviceList } from "./serviceList";

@inject("languageShell", "common")
@withDownload
@withMemberMenu
@observer
class Service extends React.Component {
  get resources() {
    return this.props.common.downLinkObj;
  }
  componentDidMount() {}
  componentDidUpdate(prevProps, prevState) {}
  componentWillUnmount() {}
  render() {
    const { menuHelper } = this.props;
    return (
      <div className="service-page-container app-main">
        <Header backHome />
        <div className="service-page-content">
          <div className="service-title">
            <img className="service-text" src={require("@/assets/images/service/service-title.png")} alt="" />
          </div>
          <div className="app-download-wrap">
            <img className="download-banner" src={require("@/assets/images/service/download-app.png")} alt="" />
            <div className="download-qrcode">
              <div className="qrcode-list">
                {this.resources["MOBILECONFIG"]?.url && (
                  <div className="download-item">
                    <div className="download-item-bg">
                      <div className="qr-code">
                        <QRCode value={this.resources["MOBILECONFIG"]?.url} size={47} />
                      </div>
                      <div className="sys-name">
                        <img className="icon-sys" src={require("@/assets/images/service/ios.png")} />
                        <span>iOS</span>
                      </div>
                    </div>

                    <div className="download-btn" onClick={this.props.handleIOSDownload}>
                      <span>{this.props.languageShell.t("download")}</span>
                    </div>
                  </div>
                )}
                {this.resources["Android"]?.url && (
                  <div className="download-item">
                    <div className="download-item-bg">
                      <div className="qr-code">
                        <QRCode value={this.resources["Android"]?.url} size={47} />
                      </div>
                      <div className="sys-name">
                        <img className="icon-sys" src={require("@/assets/images/service/android.png")} />
                        <span>Android</span>
                      </div>
                    </div>

                    <div className="download-btn" onClick={this.props.handleAndroidDownload}>
                      <span>{this.props.languageShell.t("download")}</span>
                    </div>
                  </div>
                )}
              </div>
              <div className="download-tip">
                <span>{this.props.languageShell.t("download_tip")}</span>
              </div>
            </div>
          </div>
          <div className="service-list">
            {serviceList.map((item, index) => {
              return (
                menuHelper.displayMenu(item) && (
                  <div
                    key={`service_resource_${index}`}
                    className="service-item"
                    onClick={() => menuHelper.menuClick(item)}
                  >
                    <div className="service-icon">
                      <Icon type={item.icon} />
                    </div>
                    <div className="service-name">
                      <span>{this.resources[item.resource]?.labelNameEn || this.props.languageShell.t(item.name)}</span>
                    </div>
                  </div>
                )
              );
            })}
          </div>
          <SpeedTest />
        </div>
      </div>
    );
  }
}

export default Service;
