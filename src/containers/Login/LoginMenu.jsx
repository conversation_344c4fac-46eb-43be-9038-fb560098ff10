import React from "react";
import { withRouter } from "react-router-dom";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";
import { withCs } from "tcg-mobile-common";

import withAuth from "@/hoc/withAuth";

import "./LoginMenu.scss";

@inject("languageShell", "mcMenu", "auth")
@withCs
@withRouter
@withAuth
@observer
class LoginMenu extends React.Component {
  get showForgetPass() {
    return this.props.mcMenu.whitelabelVal.CHNGPASSMAIL3 || this.props.mcMenu.whitelabelVal.CHNGPASSVERIF3;
  }
  render() {
    if (this.props.type === "service") {
      return (
        <div className="login-nav-wrap">
          <div className="login-nav-list">
            <div className="login-nav-item" onClick={this.props.openCs}>
              <Icon className="nav-icon" type={require("!svg-sprite-loader!@/assets/images/form/icon-service.svg")} />
              <span>{this.props.languageShell.t("in_customer_services_new")}</span>
            </div>
          </div>
        </div>
      );
    }
    return (
      <div className="login-nav-wrap">
        <div className="login-nav-list">
          {location.pathname === "/m/register" && (
            <div className="login-nav-item" onClick={() => this.props.history.push("/m/login")}>
              <Icon className="nav-icon" type={require("!svg-sprite-loader!@/assets/images/form/icon-login.svg")} />
              <span>{this.props.languageShell.t("hd_login_button")}</span>
            </div>
          )}
          {location.pathname === "/m/login" && (
            <div className="login-nav-item" onClick={() => this.props.history.push("/m/register")}>
              <Icon className="nav-icon" type={require("!svg-sprite-loader!@/assets/images/form/icon-login.svg")} />
              <span>{this.props.languageShell.t("hd_reg_button")}</span>
            </div>
          )}
          {this.props.mcMenu.whitelabelVal.FREEPLAY3 && (
            <div className="login-nav-item" onClick={this.props.freeTrial}>
              <span>{this.props.languageShell.t("in_free_trial")}</span>
            </div>
          )}
          <div className="login-nav-item" onClick={this.props.openCs}>
            <Icon className="nav-icon" type={require("!svg-sprite-loader!@/assets/images/form/icon-service.svg")} />
            <span>{this.props.languageShell.t("in_customer_services_new")}</span>
          </div>
        </div>
      </div>
    );
  }
}

export default LoginMenu;
