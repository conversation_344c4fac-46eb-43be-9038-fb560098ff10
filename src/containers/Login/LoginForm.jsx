import React from "react";
import { Icon, Toast } from "antd-mobile";
import cz from "classnames";
import { Base64 } from "js-base64";
import { debounce } from "lodash";
import { inject, observer } from "mobx-react";
import PubSub from "pubsub-js";
import { createForm } from "rc-form";
import { AutoIcon, autoLogin, CountryCodeList, withFirebase, withGeetest } from "tcg-mobile-common";
import { withCs } from "tcg-mobile-common";
import { utils } from "tcgmodulemc";

import apiUtils from "@/apis/apiUtils";
import address from "@/config/address.config";
import { formConfig } from "@/config/form.config";
import storageConstants from "@/constants/storageConstants";
import CheckBox from "@/ui/CheckBox";
import { inputScrollIntoView } from "@/utils/navigatorUtil";
import { handleWindowPage } from "@/utils/openLink";

const form_success = require("!svg-sprite-loader!@/assets/images/form/success.svg");
const form_error = require("!svg-sprite-loader!@/assets/images/form/error.svg");

@withGeetest
@withFirebase
@autoLogin
@createForm()
@withCs
@inject("mcMenu", "mcCommon", "common", "auth", "languageShell", "login")
@observer
class LoginForm extends React.Component {
  get showForgetPass() {
    return this.props.mcMenu.whitelabelVal.CHNGPASSMAIL3 || this.props.mcMenu.whitelabelVal.CHNGPASSVERIF3;
  }
  constructor(props) {
    super(props);
    this.handleLogin = this.handleLogin.bind(this);
    this.inputRefs = {};
    this.state = {
      agentPage: false,
      showPass: false,
      showCaptcha: false,
      remPass: true,
      codeStatus: true,
      codeText: "",
      login_method: true,
      resources: { SpeedTest: {} },
      MOBILEVERCODELOGIN3: this.props.mcMenu.whitelabelVal.MOBILEVERCODELOGIN3,
      defaultDialingCode: null,
      countryDialingCode: undefined,
      disableSubmit: true,
      checkDisclaimer: true,
      loadingCaptcha: false,
    };
    this.initState = this.initState.bind(this);
  }
  componentDidMount() {
    window.scrollTo(0, 0);
    this.rememberPwd();
    this.initState();
    this.subscribeLangChanged();
    this.agentSetting();
    if (this.props.mcMenu.whitelabelVal.LOGINREQCAPTCHA3) {
      this.handleCaptchat();
    }
  }
  componentWillUnmount() {
    clearTimeout(this.smsTimer);
    this.smsTimer = undefined;
    PubSub.unsubscribe(this.translateError);
  }
  subscribeLangChanged = () => {
    this.translateError = PubSub.subscribe(
      "changeLanguage",
      debounce(() => {
        const { getFieldValue, validateFields } = this.props.form;
        const username = getFieldValue("username");
        const password = getFieldValue("password");
        const mobileNum = getFieldValue("mobileNum");
        const messageCode = getFieldValue("messageCode");

        const fields = [];
        if (username) {
          fields.push("username");
        }
        if (password) {
          fields.push("password");
        }
        if (mobileNum) {
          fields.push("mobileNum");
        }
        if (messageCode) {
          fields.push("messageCode");
        }
        validateFields(fields, { force: true });
      }, 200)
    );
  };
  agentSetting() {
    if (location.pathname === "/m/agent") {
      this.setState({
        agentPage: true,
      });
    } else {
      window.localStorage.removeItem("ac");
    }
  }
  showClear(value) {
    return value.length > 0 ? " show" : "";
  }
  handleClear = (name) => {
    const { setFieldsValue } = this.props.form;
    setFieldsValue({ [name]: "" });
    this.inputRefs[name]?.focus();
  };
  initState() {
    const resources = this.props.common.downLink.reduce((acc, curr) => {
      return { ...acc, [curr.resourceType]: curr };
    }, {});
    this.setState({
      resources,
      codeText: "",
      showCaptcha: this.props.mcMenu.whitelabelVal.LOGINREQCAPTCHA3,
    });
  }
  getCode = () => {
    this.props.form.validateFields(["mobileNum"], async (error, value) => {
      if (error) {
        return;
      }
      if (this.state.codeStatus) {
        Toast.loading(`${this.props.languageShell.resource["in_sms_sending"]}...`, 10);
        const geetest = await this.props.geetestVerify("sms");
        const res = await apiUtils.post(address.account.getCode, {
          data: {
            mobileNum: value.mobileNum,
            countryDialingCode: this.state.countryDialingCode,
            ...geetest,
          },
        });
        if (this.props.usingFirebase) {
          try {
            const success = await this.props.sendCodeByFirebase(this.state.countryDialingCode, value.mobileNum);
            if (success) {
              Toast.hide();
              Toast.success(this.props.languageShell.resource["in_sms_send"], 1.5, () => {});
              this.countDownSms(res.data.value.expirationTime);
            }
          } catch (e) {
            Toast.hide();
          }
        } else {
          Toast.hide();
          Toast.success(this.props.languageShell.resource["in_sms_send"], 1.5, () => {});
          this.countDownSms(res.data.value.expirationTime);
        }
      }
    });
  };
  rememberPwd = async (revertType) => {
    const sass = await utils.appStorageUtils.storageGetItem("sass");
    const remPass = await utils.appStorageUtils.storageGetItem("remPass");
    if (!sass || !remPass) {
      return;
    }
    const typeMap = {
      username: true,
      sms: false,
    };
    const user = JSON.parse(remPass);
    let init = false;
    if (!revertType) {
      init = true;
      revertType = user.type;
    }
    if (user.type === revertType) {
      switch (user.type) {
        case "sms": {
          if (!this.props.mcMenu.whitelabelVal.MOBILEVERCODELOGIN3) {
            return;
          }
          this.setState((state) => {
            return {
              remPass: init || state.remPass,
              login_method: typeMap[revertType],
              defaultDialingCode: user.countryDialingCode,
            };
          });
          if (user.countryDialingCode) {
            this.setDialingCode(user.countryDialingCode);
          }
          this.props.form.setFieldsValue({
            mobileNum: user.username,
          });
          return;
        }
        default: {
          let pass = Base64.decode(Base64.decode(user.password));
          this.setState((state) => {
            return {
              remPass: init || state.remPass,
              login_method: typeMap[revertType] || 1,
            };
          });
          this.props.form.setFieldsValue({
            username: user.username,
            password: pass.substr(0, pass.indexOf(sass)),
          });
        }
      }
    }
    // this.props.form.validateFields((errors, values) => {
    //   this.setState({
    //     disableSubmit: Object.values(values).some((value) => !value),
    //   });
    // });
  };

  onChange = (value) => {
    this.setState({
      remPass: value,
    });
  };

  handleLogin() {
    this.props.form.validateFields(async (error, value) => {
      if (error) {
        return;
      }
      const data = {
        username: this.state.login_method ? value.username : value.mobileNum,
        password: this.state.login_method ? value.password : value.messageCode,
        captcha: value.identifying,
        type: this.state.login_method ? "username" : "sms",
      };
      let firebaseIdToken;
      if (this.props.usingFirebase && !this.state.login_method) {
        firebaseIdToken = await this.props.verifyCodeByFirebase(value.messageCode);
        if (!firebaseIdToken) {
          return;
        }
        data.type = "firebase";
        data.firebaseChannel = "sms";
        data.firebaseIdToken = firebaseIdToken;
        data.firebaseMobileNo = value.mobileNum;
        data.countryDialingCode = this.state.countryDialingCode;
      }
      localStorage.removeItem(storageConstants.LOGIN_TIME);
      this.props
        .loginAction({ data, remPass: this.state.remPass, geetest: true })
        .then((res) => {
          this.props.common.showLogin(false);
        })
        .catch((res) => {
          if (res.data.value.captcha) {
            this.setState(
              {
                showCaptcha: true,
              },
              () => {
                this.handleCaptchat();
              }
            );
          }
        });
    });
  }
  handleCaptchat = () => {
    this.setState({
      loadingCaptcha: true,
    });
    this.props.auth.captchas().finally((res) => {
      this.setState({
        loadingCaptcha: false,
      });
    });
    this.props.form.setFieldsValue({ identifying: "" });
  };

  handleValidate = (field) => (rule, value, callback) => {
    const result = this.props.mcCommon.verification(value || "", field);
    if (result.pass) {
      callback();
      return;
    }
    callback([new Error(result.message)]);
  };
  requiredDot(key) {
    if (this.props.mcCommon.regExp[key] && this.props.mcCommon.regExp[key].required) {
      return "*";
    }
    return null;
  }

  countDownSms = (expired) => {
    this.smsTimer = setTimeout(() => {
      const count = Math.floor((expired - Date.now()) / 1000);
      if (count <= 0) {
        clearTimeout(this.smsTimer);
        this.setState({
          codeStatus: true,
          codeText: "",
        });
      } else {
        this.setState({
          codeStatus: false,
          codeText: `${count}s`,
        });
        this.countDownSms(expired);
      }
    }, 500);
  };
  handlePassLoginTabClick = (login_method) => () => {
    this.setState({ login_method });
    this.rememberPwd(login_method ? "username" : "sms");
    if (this.state.showCaptcha) {
      this.handleCaptchat();
    }
  };
  onRef = (ref) => {
    this.child = ref;
  };
  changeLanguage = (e) => {
    this.child.changeLanguage();
  };
  getInputIcon(field) {
    const { getFieldError, getFieldValue } = this.props.form;
    let errors = getFieldError(field);
    let value = getFieldValue(field);
    if (!value) {
      return null;
    }
    return <Icon className="input_status" type={errors ? form_error : form_success} />;
  }
  inputFocus = (e) => {
    inputScrollIntoView(e.target);
  };
  handleInputChange = (fieldName, value) => {
    const { setFieldsValue, validateFields } = this.props.form;

    // setFieldsValue({ [fieldName]: value });

    // validateFields((errors, values) => {
    //   this.setState({
    //     disableSubmit: Object.values(values).some((value) => !value),
    //   });
    // });
  };
  handleFirebaseLogin = (provider) => (e) => {
    this.props.firebaseLogin(provider);
  };
  showSocialMediaLogin() {
    return (
      this.props.firebaseInit &&
      (this.props.mcMenu.whitelabelVal["FACEBOOKLOGIN3"] ||
        this.props.mcMenu.whitelabelVal["GOOGLELOGIN3"] ||
        this.props.mcMenu.whitelabelVal["LINELOGIN3"])
    );
  }
  setDialingCode = (countryDialingCode) => {
    this.setState({
      countryDialingCode,
    });
  };
  togglePass = () => {
    this.setState({
      showPass: !this.state.showPass,
    });
  };
  removeValue = (key) => {
    this.props.form.setFieldsValue({
      [key]: "",
    });
  };
  handleDisclaimer = () => {
    this.setState({ checkDisclaimer: !this.state.checkDisclaimer });
  };
  handlePolicy = (e) => {
    this.props.push(`/m/help?tag=terms`);
  };
  openResources = (type) => {
    const link = this.props.common.downLinkObj[type];
    if (link?.url) {
      handleWindowPage(link?.url);
    }
  };

  render() {
    const { getFieldProps, getFieldError, getFieldValue } = this.props.form;
    return (
      <div className="form-wrap">
        <form className="form-content" autoComplete="off">
          <div className="form-title">
            <h6>{this.props.languageShell.t("hd_login_button")}</h6>
          </div>
          {this.state.MOBILEVERCODELOGIN3 && (
            <div className="method-select">
              <div className="select-wrap">
                <div
                  className={cz("method-toggle", { on: this.state.login_method })}
                  onClick={this.handlePassLoginTabClick(true)}
                >
                  <span>{this.props.languageShell.t("in_increase_username")}</span>
                </div>
                <div
                  className={cz("method-toggle", { on: !this.state.login_method })}
                  onClick={this.handlePassLoginTabClick(false)}
                >
                  <span>{this.props.languageShell.t("in_increase_mobile_placeholder")}</span>
                </div>
              </div>
            </div>
          )}
          <div className="outter-form">
            <div className="form-group-wrap">
              {this.state.login_method && (
                <div
                  className={cz("form-group", {
                    form_error: getFieldError("username"),
                    form_success: getFieldValue("username") && !getFieldError("username"),
                  })}
                >
                  {/* <div className="form-label">{this.props.languageShell.t("in_increase_username")}</div> */}
                  {/* <span className="input-required">*</span> */}
                  <div className="fixed-list">
                    <input
                      key="username"
                      type="text"
                      className={`form-control`}
                      name="username"
                      placeholder={this.props.languageShell.t("in_increase_username")}
                      autoComplete="off"
                      ref={(c) => {
                        this.username = c;
                      }}
                      {...getFieldProps("username", {
                        onChange: (e) => {
                          const value = e.target.value;
                          this.handleInputChange("username", value);
                        },
                        onFocus: this.inputFocus,
                        rules: [{ validator: this.handleValidate("loginUsername") }],
                        ref: (c) => {
                          this.inputRefs["username"] = c;
                        },
                      })}
                    />
                    <i className="input-icon">
                      <AutoIcon icon={formConfig.username?.icon} />
                    </i>

                    {/* {getFieldValue("username") && (
                    <div className="clear-field" onClick={() => this.handleClear("username")}>
                      <Icon type={require("!svg-sprite-loader!@/assets/images/common/btn-close.svg")} />
                    </div>
                  )} */}
                  </div>
                  <div className="panel panel-default">
                    <p>{getFieldError("username")?.join(",")}</p>
                  </div>
                </div>
              )}
              {!this.state.login_method && (
                <div
                  className={cz("form-group", {
                    form_error: getFieldError("mobileNum"),
                    form_success: getFieldValue("mobileNum") && !getFieldError("mobileNum"),
                  })}
                >
                  {/* <div className="form-label">{this.props.languageShell.t("in_increase_mobileNum")}</div> */}
                  {/* <span className="input-required">*</span> */}
                  <div className={cz("fixed-list")}>
                    <CountryCodeList
                      defaultDialingCode={this.state.defaultDialingCode}
                      show={this.props.showLoginDialingCode}
                      setDialingCode={this.setDialingCode}
                    />
                    <input
                      key="mobileNum"
                      type="text"
                      className={`form-control form-mobileNum`}
                      name="mobileNum"
                      placeholder={this.props.languageShell.t("in_increase_mobileNum")}
                      autoComplete="off"
                      inputMode="tel"
                      maxLength="20"
                      ref={(c) => {
                        this.mobileNum = c;
                      }}
                      {...getFieldProps("mobileNum", {
                        onChange: (e) => {
                          const value = e.target.value;
                          this.handleInputChange("mobileNum", value);
                        },
                        onFocus: this.inputFocus,
                        rules: [{ validator: this.handleValidate("loginMobileNum") }],
                      })}
                    />
                    <i className="input-icon">
                      <AutoIcon icon={formConfig.mobileNum?.icon} />
                    </i>
                  </div>
                  <div className="panel panel-default">
                    <p>{getFieldError("mobileNum")?.join(",")}</p>
                  </div>
                </div>
              )}

              {!this.state.login_method
                ? [
                    <div
                      className={cz("form-group", {
                        form_error: getFieldError("messageCode"),
                        form_success: getFieldValue("messageCode") && !getFieldError("messageCode"),
                      })}
                    >
                      {/* <div className="form-label">{this.props.languageShell.t("in_sms_code")}</div> */}
                      {/* <span className="input-required">*</span> */}
                      <div className="fixed-list">
                        <input
                          type="text"
                          key="messageCode"
                          className={`form-control message-code`}
                          name="messageCode"
                          placeholder={this.props.languageShell.t("in_sms_code")}
                          pattern="[0-9]*"
                          autoComplete="one-time-code"
                          inputMode="numeric"
                          maxLength="10"
                          ref={(c) => {
                            this.messageCode = c;
                          }}
                          {...getFieldProps("messageCode", {
                            onChange: (e) => {
                              const value = e.target.value;
                              this.handleInputChange("messageCode", value);
                            },
                            onFocus: this.inputFocus,
                            rules: [{ validator: this.handleValidate("verificationCode") }],
                          })}
                        />
                        <i className="input-icon">
                          <AutoIcon icon={formConfig.messageCode?.icon} />
                        </i>

                        <a className={"get-code"} onClick={this.getCode}>
                          {this.state.codeText === ""
                            ? this.props.languageShell.t("in_fetch_verificationCode")
                            : this.state.codeText}
                        </a>
                      </div>
                      <div className="panel panel-default">
                        <p>{getFieldError("messageCode")?.join(",")}</p>
                      </div>
                    </div>,
                  ]
                : [
                    <div
                      className={cz("form-group", {
                        form_error: getFieldError("password"),
                        form_success: getFieldValue("password") && !getFieldError("password"),
                      })}
                    >
                      {/* <div className="form-label">{this.props.languageShell.t("in_increase_password")}</div> */}
                      {/* <span className="input-required">*</span> */}
                      <div className="fixed-list">
                        <input
                          key="password"
                          type={this.state.showPass ? "text" : "password"}
                          autoComplete="off"
                          className={`form-control`}
                          name="password"
                          placeholder={this.props.languageShell.t("in_increase_password")}
                          ref={(c) => {
                            this.password = c;
                          }}
                          {...getFieldProps("password", {
                            onChange: (e) => {
                              const value = e.target.value;
                              this.handleInputChange("password", value);
                            },
                            onFocus: this.inputFocus,
                            rules: [{ validator: this.handleValidate("loginPassword") }],
                          })}
                        />
                        <i className="input-icon">
                          <AutoIcon icon={formConfig.password?.icon} />
                        </i>
                        <span className={`right-icon`} onClick={this.togglePass}>
                          <Icon
                            type={
                              this.state.showPass
                                ? require("!svg-sprite-loader!@/assets/images/form/eye-open.svg")
                                : require("!svg-sprite-loader!@/assets/images/form/eye-closed.svg")
                            }
                          />
                        </span>
                      </div>
                      <div className="panel panel-default">
                        <p>{getFieldError("password")?.join(",")}</p>
                      </div>
                    </div>,
                    this.state.showCaptcha && (
                      <div
                        className={cz("form-group", {
                          form_error: getFieldError("identifying"),
                          form_success: getFieldValue("identifying") && !getFieldError("identifying"),
                        })}
                      >
                        {/* <div className="form-label">{this.props.languageShell.t("in_vwrite_code")}</div> */}
                        {/* <span className="input-required">*</span> */}
                        <div className="fixed-list">
                          <input
                            type="text"
                            autoComplete="off"
                            className={`form-control captcha`}
                            name="identifying"
                            placeholder={this.props.languageShell.t("in_vwrite_code")}
                            inputMode="numeric"
                            pattern="[0-9]*"
                            maxLength="6"
                            ref={(c) => {
                              this.identifying = c;
                            }}
                            {...getFieldProps("identifying", {
                              onChange: (e) => {
                                const value = e.target.value;
                                this.handleInputChange("identifying", value);
                              },
                              onFocus: this.inputFocus,
                              rules: [{ required: true, message: this.props.languageShell.resource["in_vwrite_code"] }],
                            })}
                          />
                          <i className="input-icon">
                            <AutoIcon icon={formConfig.captcha?.icon} />
                          </i>
                          {this.props.auth.cap && (
                            <img
                              alt="captcha"
                              onClick={this.handleCaptchat}
                              src={`data:image/png;base64,${this.props.auth.cap}`}
                              className="captcha-pic"
                            />
                          )}
                          {/* {this.props.auth.cap && (
                          <Icon
                            className={cz("refresh-captcha", { loading: this.state.loadingCaptcha })}
                            type={require("!svg-sprite-loader!@/assets/images/common/icon-refresh.svg")}
                            onClick={this.handleCaptcha}
                          />
                        )} */}
                        </div>
                        <div className="panel panel-default">
                          <p>{getFieldError("identifying")?.join(",")}</p>
                        </div>
                      </div>
                    ),
                  ]}
            </div>
            <div className="form-checkbox-wrap">
              <CheckBox checked={this.state.remPass} onChange={this.onChange} ref={(el) => (this.checkRef = el)}>
                <p>{this.props.languageShell.t("in_remember_password")}</p>
              </CheckBox>
              {this.showForgetPass && (
                <span className="forget-password" onClick={() => this.props.history.push("/m/forget")}>
                  <span>{this.props.languageShell.t("in_forget_password")}</span>
                </span>
              )}
            </div>
          </div>
        </form>
        <div className="form-actions">
          <div className="btn-group">
            <button
              type="button"
              onClick={this.handleLogin}
              disabled={!this.state.checkDisclaimer}
              className="submit-btn login-btn"
            >
              <span>{this.props.languageShell.t("hd_login_button")}</span>
            </button>
          </div>
          <div className="already-account">
            <span>{this.props.languageShell.t("in_no_account_tip")}</span>
            <span className="login-now" onClick={() => this.props.history.push("/m/register")}>
              {this.props.languageShell.t("hd_reg_button")}
            </span>
          </div>
          {!this.state.agentPage && (
            <div className={cz("social-media-login", { hide: !this.showSocialMediaLogin() })}>
              <div className={"tips"}>
                <span>{this.props.languageShell.t("login_way")}</span>
              </div>
              <div className={cz("item-group")}>
                {this.props.mcMenu.whitelabelVal["LINELOGIN3"] && (
                  <div onClick={this.handleFirebaseLogin("line")} className={cz("item line")}>
                    <img src={require("@/assets/images/common/line.png")} />
                  </div>
                )}
                {this.props.mcMenu.whitelabelVal["FACEBOOKLOGIN3"] && (
                  <div onClick={this.handleFirebaseLogin("facebook")} className={cz("item facebook")}>
                    <img src={require("@/assets/images/common/facebook.png")} />
                  </div>
                )}
                {this.props.mcMenu.whitelabelVal["GOOGLELOGIN3"] && (
                  <div onClick={this.handleFirebaseLogin("google")} className={cz("item google")}>
                    <img src={require("@/assets/images/common/google.png")} />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
}

export default LoginForm;
