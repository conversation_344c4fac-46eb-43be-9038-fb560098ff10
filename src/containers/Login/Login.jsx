import React from "react";
import { withRout<PERSON> } from "react-router";
import { inject, observer } from "mobx-react";

import GoBack from "@/components/GoBack/GoBack";

import LoginForm from "./LoginForm";

import "./Login.scss";

@inject("languageShell", "common")
@withRouter
@observer
class Login extends React.Component {
  render() {
    return (
      <div className="form-page-container login-container app-main">
        <GoBack type="left" />

        <LoginForm {...this.props} />
      </div>
    );
  }
}
export default Login;
