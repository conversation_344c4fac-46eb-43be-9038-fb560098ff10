import React from "react";
import { inject, observer } from "mobx-react";

import withAuth from "@/hoc/withAuth";

import "./LoginMenu.scss";

@inject("languageShell", "mcMenu", "auth")
@withAuth
@observer
class FreeTrial extends React.Component {
  render() {
    if (this.props.mcMenu.whitelabelVal.FREEPLAY3) {
      return (
        <div className="free-trial" onClick={this.props.freeTrial}>
          <div className="trial-title">
            <span>{this.props.languageShell.t("quick_login")}</span>
          </div>

          <img className="icon-trial" src={require("@/assets/images/form/trial.png")} alt="" />
        </div>
      );
    }
    return null;
  }
}

export default FreeTrial;
