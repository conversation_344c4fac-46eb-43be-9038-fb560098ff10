import React from "react";
import { withRouter } from "react-router-dom";
import { Icon } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";

import "./LoginSwitch.scss";

@inject("mcMenu", "languageShell", "auth", "common")
@withRouter
@observer
class LoginSwitch extends React.Component {
  renderMenu = () => {
    if (["login", "register"].includes(this.props.type)) {
      return (
        <div className="menu-links">
          <div
            className={cz("menu-link", { on: this.props.common.loginModalStatus })}
            onClick={() => this.props.common.showLogin(true)}
          >
            <Icon className="menu-icon" type={require("!svg-sprite-loader!@/assets/images/form/icon-login.svg")} />
            <span>{this.props.languageShell.t("hd_login_button")}</span>
          </div>
          <div
            className={cz("menu-link", { on: this.props.common.registerModalStatus })}
            onClick={() => this.props.common.showRegister(true)}
          >
            <Icon className="menu-icon" type={require("!svg-sprite-loader!@/assets/images/form/icon-register.svg")} />
            <span>{this.props.languageShell.t("hd_reg_button")}</span>
          </div>
        </div>
      );
    }
    if (["forget", "changePsw"].includes(this.props.type)) {
      return (
        <div className="menu-links">
          {this.props.type === "forget" && (
            <div className={cz("menu-link on")}>
              <Icon className="menu-icon" type={require("!svg-sprite-loader!@/assets/images/form/icon-password.svg")} />
              <span>{this.props.languageShell.t("in_forget_password")}</span>
            </div>
          )}
          {this.props.type === "changePsw" && (
            <div className={cz("menu-link on")}>
              <Icon className="menu-icon" type={require("!svg-sprite-loader!@/assets/images/form/icon-password.svg")} />
              <span>{this.props.languageShell.t("change_password")}</span>
            </div>
          )}
        </div>
      );
    }
    return null;
  };
  render() {
    return <div className="form-switch-menu">{this.renderMenu()}</div>;
  }
}

export default LoginSwitch;
