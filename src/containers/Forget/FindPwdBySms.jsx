import React from "react";
import { with<PERSON><PERSON><PERSON> } from "react-router";
import { Toast } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import { createForm } from "rc-form";
import { AutoIcon, autoLogin, CountryCodeList, withFirebase, withGeetest } from "tcg-mobile-common";

import account from "@/apis/account";
import apiUtils from "@/apis/apiUtils";
import FormService from "@/components/OnlineService/FormService";
import address from "@/config/address.config";
import { formConfig } from "@/config/form.config";

@withGeetest
@createForm()
@withFirebase
@autoLogin(false)
@inject("mcCommon", "languageShell")
@withRouter
@observer
class FindPwdBySms extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      sendSuccess: false, // 是否正在发生验证码
      canSendTime: 60,
      countryDialingCode: "",
      disableSubmit: true,
    };
  }

  componentWillUnmount() {
    this.clearCountDown();
  }

  // 重置倒计时
  clearCountDown() {
    this.setState({
      sendSuccess: false,
      canSendTime: 0,
    });
    this.countDownSmsInterval && clearInterval(this.countDownSmsInterval);
    this.countDownSmsInterval = undefined;
  }

  // 获取验证码
  getVerificationCode = () => {
    this.props.form.validateFields(["mobileNum"], async (error, value) => {
      if (error) {
        return;
      }
      Toast.loading(this.props.languageShell.t("in_sms_sending"), 9999);
      const geetest = await this.props.geetestVerify("sms");
      const res = await apiUtils.post(address.account.getCode, {
        data: {
          mobileNum: value.mobileNum,
          countryDialingCode: this.state.countryDialingCode,
          ...geetest,
        },
      });
      if (this.props.usingFirebase) {
        try {
          const success = await this.props.sendCodeByFirebase(this.state.countryDialingCode, value.mobileNum);
          if (success) {
            Toast.hide();
            Toast.success(this.props.languageShell.t("in_sms_send"), 1.5, () => {});
            this.countDownSms(res.data.value.expirationTime);
          }
        } catch (e) {
          Toast.hide();
        }
      } else {
        Toast.hide();
        Toast.success(this.props.languageShell.t("in_sms_send"), 1.5, () => {});
        this.countDownSms(res.data.value.expirationTime);
      }
    });
  };
  countDownSms = (expirationTime) => {
    const now = Date.now();
    this.setState({
      sendSuccess: true,
      canSendTime: Math.floor((expirationTime - now) / 1000),
    });
    this.countDownSmsTimer = setTimeout(() => {
      let count = Math.floor((expirationTime - Date.now()) / 1000);
      if (count <= 0) {
        clearTimeout(this.countDownSmsTimer);
        this.setState({
          sendSuccess: false,
          canSendTime: 0,
        });
      } else {
        this.setState({
          sendSuccess: true,
          canSendTime: count,
        });
        this.countDownSms(expirationTime);
      }
    }, 500);
  };

  // 新密码输入之后,并登录
  setPasswordLogin = () => {
    this.props.form.validateFields((error) => {
      if (error) {
        return false;
      }
      this.handleSetPassword();
    });
  };

  // 更新密码
  handleSetPassword = async () => {
    const { getFieldValue } = this.props.form;
    const mobileNum = getFieldValue("mobileNum");
    const verificationCode = getFieldValue("verificationCode");
    const newPassword = getFieldValue("password");
    const payload = {
      newPassword,
      mobileNum,
      verificationCode,
    };
    if (this.props.usingFirebase) {
      let firebaseIdToken = await this.props.verifyCodeByFirebase(payload.verificationCode);
      if (!firebaseIdToken) {
        return;
      }
      payload.firebaseIdToken = firebaseIdToken;
    }
    Toast.loading(this.props.languageShell.t("loading"), 15);
    account
      .getSetPasswordPhone(payload)
      .then((res) => {
        Toast.hide();
        this.handleLogin(payload.mobileNum, payload.newPassword);
      })
      .catch((e) => {
        const { setFieldsValue } = this.props.form;
        if (e.response.data) {
          const { errorCode } = e.response.data;
          if (errorCode === "modify.new.old.same") {
            return setFieldsValue({ password: "" });
          }
        }
        setFieldsValue({ verificationCode: "" });
      });
  };
  //登录
  handleLogin = (username, password) => {
    const data = {
      username,
      password,
    };
    this.props.loginAction({ data, headerColor: "#dc181c,#dc181c", geetest: true }).then(() => {
      this.props.common.showForget(false);
    });
  };
  handleValidate = (field) => (rule, value, callback) => {
    const result = this.props.mcCommon.verification(value || "", field);
    if (result.pass) {
      callback();
      return;
    }
    callback([new Error(result.message)]);
  };
  inputChange = (e) => {
    this.props.form.validateFields();
  };
  disabledSubmit = () => {
    const notTouched = !this.props.form.isFieldsTouched();
    const validate = this.props.form.getFieldsError();
    return notTouched || Object.keys(validate).some((v) => validate[v]);
  };
  setCountryCode = (e) => {
    this.setState({
      countryDialingCode: e,
    });
  };
  removeValue = (key) => {
    this.props.form.setFieldsValue({
      [key]: "",
    });
  };
  handleInputChange = (fieldName, value) => {
    const { setFieldsValue, validateFields } = this.props.form;

    // setFieldsValue({ [fieldName]: value });

    // validateFields((errors, values) => {
    //   this.setState({
    //     disableSubmit: !!errors,
    //   });
    // });
  };
  render() {
    let InputData = this.props.mcCommon.regExp;
    const { canSendTime, sendSuccess } = this.state;
    const { getFieldProps, getFieldError, getFieldValue } = this.props.form;
    return (
      <div className="form-content">
        <div className="outter-form">
          <div className="form-group-wrap">
            <div
              className={cz("form-group", {
                form_error: getFieldError("mobileNum"),
                form_success: getFieldValue("mobileNum") && !getFieldError("mobileNum"),
              })}
            >
              {/* <div className="form-label">{this.props.languageShell.t("in_increase_mobileNum")}</div> */}
              <div className="fixed-list">
                <CountryCodeList show={this.props.showRegisterDialingCode} setDialingCode={this.setCountryCode} />

                <input
                  type="text"
                  inputMode="tel"
                  className="form-control form-mobileNum"
                  placeholder={this.props.languageShell.t("in_increase_mobileNum")}
                  name="mobileNum"
                  {...getFieldProps("mobileNum", {
                    onChange: (e) => {
                      const value = e.target.value;
                      this.handleInputChange("mobileNum", value);
                    },
                    rules: [{ required: true, validator: this.handleValidate("mobileNum") }],
                  })}
                />
                <i className="input-icon">
                  <AutoIcon icon={formConfig.mobileNum?.icon} />
                </i>
              </div>
              <div className="panel panel-default">
                <p>{this.props.languageShell.t("in_mobileNum_error").format(InputData.mobileNum.minLength)}</p>
              </div>
            </div>
            <div
              className={cz("form-group", {
                form_error: getFieldError("verificationCode"),
                form_success: getFieldValue("verificationCode") && !getFieldError("verificationCode"),
              })}
            >
              {/* <div className="form-label">{this.props.languageShell.t("in_sms_code")}</div> */}
              {/* <span className="input-required">*</span> */}
              <div className="fixed-list messageCode">
                <input
                  type="text"
                  inputMode="decimal"
                  className="form-control-sms message-code"
                  placeholder={this.props.languageShell.t("in_sms_code")}
                  name="verificationCode"
                  autoComplete="new-password"
                  {...getFieldProps("verificationCode", {
                    onChange: (e) => {
                      const value = e.target.value;
                      this.handleInputChange("verificationCode", value);
                    },
                    rules: [{ validator: this.handleValidate("verificationCode") }],
                  })}
                />
                <i className="input-icon icon_sms">
                  <AutoIcon icon={formConfig.messageCode?.icon} />
                </i>
                {/* 倒计时按钮 */}
                <span onClick={this.getVerificationCode} className="get-code">
                  {sendSuccess && canSendTime > 0
                    ? `${canSendTime}s`
                    : this.props.languageShell.t("in_fetch_verificationCode")}
                </span>
              </div>
              <div className="panel panel-default">
                <p>{getFieldError("verificationCode")?.join(",")}</p>
              </div>
            </div>
            <div
              className={cz("form-group", {
                form_error: getFieldError("password"),
                form_success: getFieldValue("password") && !getFieldError("password"),
              })}
            >
              {/* <div className="form-label">{this.props.languageShell.t("in_increase_password")}</div> */}
              {/* <span className="input-required">*</span> */}
              <div className={`fixed-list`}>
                <input
                  type="password"
                  className="form-control-user"
                  autoComplete="off"
                  placeholder={this.props.languageShell.t("in_increase_password")}
                  name="password"
                  {...getFieldProps("password", {
                    onChange: (e) => {
                      const value = e.target.value;
                      this.handleInputChange("password", value);
                    },
                    rules: [{ validator: this.handleValidate("password") }],
                  })}
                />
                <i className="input-icon icon_password">
                  <AutoIcon icon={formConfig.password?.icon} />
                </i>
              </div>
              <div className="panel panel-default">
                <p>{getFieldError("password")?.join(",")}</p>
              </div>
            </div>
          </div>
        </div>
        <div className="form-actions">
          <div className="btn-group">
            <button type="button" onClick={this.setPasswordLogin} className="submit-btn">
              {this.props.languageShell.t("in_increase_submit")}
            </button>
          </div>
          <FormService />
        </div>
      </div>
    );
  }
}

export default FindPwdBySms;
