import React from "react";
import { Toast } from "antd-mobile";
import cz from "classnames";
import { inject, observer } from "mobx-react";
import Qs from "qs";
import { createForm } from "rc-form";
import { AutoIcon } from "tcg-mobile-common";

import GoBack from "@/components/GoBack/GoBack";
import FormService from "@/components/OnlineService/FormService";
import { formConfig } from "@/config/form.config";

import FindPwdBySms from "./FindPwdBySms";

import "./Forget.scss";

class Forget extends React.Component {
  constructor(props) {
    super(props);
    this.handleLogin = this.handleLogin.bind(this);
    this.state = {
      findType: "email",
      CHNGPASSVERIF3: false,
      CHNGPASSMAIL3: false,
      disableSubmit: true,
    };
  }
  componentDidMount() {
    this.getConfigPhone();
  }
  componentDidUpdate(nextProps) {
    if (nextProps.auth.token) {
      Toast.hide();
    }
  }
  switchFindType = (findType) => {
    this.setState({ findType });
  };
  getConfigPhone() {
    const CHNGPASSVERIF3 = this.props.mcMenu.whitelabelVal.CHNGPASSVERIF3;
    const CHNGPASSMAIL3 = this.props.mcMenu.whitelabelVal.CHNGPASSMAIL3;
    this.setState({
      CHNGPASSVERIF3: CHNGPASSVERIF3,
      CHNGPASSMAIL3: CHNGPASSMAIL3,
      findType: CHNGPASSVERIF3 ? "phone" : "email",
    });
  }
  handleValidate = (field) => (rule, value, callback) => {
    const result = this.props.mcCommon.verification(value || "", field);
    if (result.pass) {
      callback();
      return;
    }
    callback([new Error(result.message)]);
  };

  handleLogin(e) {
    this.props.form.validateFields((error, value) => {
      if (error) {
        return;
      }
      Toast.loading(this.props.languageShell.resource["in_please_wait"], 9999);
      let data = {
        username: value.username.trim(),
        email: value.email.trim(),
      };
      this.props.auth.email(Qs.stringify(data)).then((rs) => {
        Toast.hide();
        Toast.success(this.props.languageShell.resource["in_been_out"], 1, () => {
          this.props.goBack();
        });
      });
    });
  }
  removeValue = (key) => {
    this.props.form.setFieldsValue({
      [key]: "",
    });
  };
  handleInputChange = (fieldName, value) => {
    const { setFieldsValue, validateFields } = this.props.form;

    // setFieldsValue({ [fieldName]: value });

    // validateFields((errors, values) => {
    //   this.setState({
    //     disableSubmit: !!errors,
    //   });
    // });
  };
  render() {
    const { getFieldProps, getFieldError, getFieldValue } = this.props.form;
    const { findType, CHNGPASSVERIF3, CHNGPASSMAIL3 } = this.state;
    return (
      <div className="form-page-container forget-password-container app-main">
        <GoBack type="left" />
        {/* <Header /> */}
        <div className="form-wrap">
          <div className="form-title">
            <h6>{this.props.languageShell.t("in_forget_password")}</h6>
            {/* <p>{this.props.languageShell.t("retrieve_pass_ways")}</p> */}
          </div>
          {CHNGPASSVERIF3 && CHNGPASSMAIL3 && (
            <div className="method-select">
              <div className="select-wrap">
                <div
                  className={cz("method-toggle", { on: findType === "phone" })}
                  onClick={() => {
                    this.switchFindType("phone");
                  }}
                >
                  <span>{this.props.languageShell.t("in_increase_mobile_placeholder")}</span>
                </div>
                <div
                  className={cz("method-toggle", { on: findType === "email" })}
                  onClick={() => {
                    this.switchFindType("email");
                  }}
                >
                  <span>{this.props.languageShell.t("in_increase_email")}</span>
                </div>
              </div>
            </div>
          )}
          <div className="form-bg">
            {findType === "phone" ? (
              <FindPwdBySms />
            ) : (
              <form className="form-content" autoComplete="off">
                <div className="outter-form">
                  <div className="form-group-wrap">
                    <div
                      className={cz("form-group", {
                        form_error: getFieldError("username"),
                        form_success: getFieldValue("username") && !getFieldError("username"),
                      })}
                    >
                      {/* <div className="form-label">{this.props.languageShell.t("in_increase_username")}</div> */}
                      {/* <span className="input-required">*</span> */}
                      <div className="fixed-list">
                        <input
                          key="username"
                          type="text"
                          autoComplete="off"
                          className="form-control"
                          placeholder={this.props.languageShell.t("in_increase_username")}
                          name="username"
                          ref={(c) => {
                            this.username = c;
                          }}
                          {...getFieldProps("username", {
                            onChange: (e) => {
                              const value = e.target.value;
                              this.handleInputChange("username", value);
                            },
                            rules: [{ validator: this.handleValidate("loginUsername") }],
                          })}
                        />
                        <i className="input-icon icon_username">
                          <AutoIcon icon={formConfig.username?.icon} />
                        </i>
                      </div>
                      <div className="panel panel-default">
                        <p>{getFieldError("username")?.join(",")}</p>
                      </div>
                    </div>
                    <div
                      className={cz("form-group", {
                        form_error: getFieldError("email"),
                        form_success: getFieldValue("email") && !getFieldError("email"),
                      })}
                    >
                      {/* <div className="form-label">{this.props.languageShell.t("in_increase_email")}</div> */}
                      {/* <span className="input-required">*</span> */}
                      <div className="fixed-list">
                        <input
                          type="text"
                          autoComplete="off"
                          className="form-control"
                          placeholder={this.props.languageShell.t("in_increase_email")}
                          name="email"
                          ref={(c) => {
                            this.email = c;
                          }}
                          {...getFieldProps("email", {
                            onChange: (e) => {
                              const value = e.target.value;
                              this.handleInputChange("email", value);
                            },
                            rules: [
                              {
                                required: true,
                                pattern: /^(\w)+(\.\w+)*@(\w)+((\.\w{2,3}){1,3})$/,
                                message: this.props.languageShell.resource["in_email_incorrect"],
                              },
                            ],
                          })}
                        />
                        <i className="input-icon icon_email">
                          <AutoIcon icon={formConfig.email?.icon} />
                        </i>
                      </div>
                      <div className="panel panel-default">
                        <p>{getFieldError("email")?.join(",")}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="form-actions">
                  <div className="btn-group">
                    <button type="button" className="submit-btn" onClick={this.handleLogin}>
                      {this.props.languageShell.t("in_increase_submit")}
                    </button>
                  </div>
                  <FormService />
                </div>
              </form>
            )}
          </div>
          {/* <div className="already-account">
              <p>{this.props.languageShell.t("remembered_account")}</p>
              <p className="login-now" onClick={() => this.props.history.push("/m/login")}>
                {this.props.languageShell.t("in_login_now")}
              </p>
            </div> */}
        </div>
      </div>
    );
  }
}

export default inject("common", "mcCommon", "auth", "mcMenu", "languageShell")(createForm()(observer(Forget)));
