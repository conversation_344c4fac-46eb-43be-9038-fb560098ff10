import React from "react";
import { Icon, Modal } from "antd-mobile";
import { isEmpty } from "lodash";
import { inject, observer } from "mobx-react";
import { autoLogin, CommonLogo, DownloadPopup, PopupV2, withCs, withReferral } from "tcg-mobile-common";

import account from "@/apis/account";
import Footer from "@/components/Footer/Footer";
import NoticeMarquee from "@/components/NoticeMarquee/NoticeMarquee";
import FloatService from "@/components/OnlineService/FloatService";
import HomeHeader from "@/components/OutterHeader/HomeHeader";
import PageBanner from "@/components/PageBanner/PageBanner";
import deploy from "@/config/deploy.config";
import { handleWindowPage } from "@/utils/openLink";
import { get, getToken } from "@/utils/storage";

import Home from "./Home/Home";

import "./OutterApp.scss";

@inject("common", "wallet", "mcCommon", "languageShell", "tcg<PERSON><PERSON>mon", "gameCenter", "auth")
@withReferral
@autoLogin
@withCs
@observer
class OutterApp extends React.Component {
  get resources() {
    return this.props.common.downLinkObj;
  }
  get isLogin() {
    return this.props.auth.isLogin;
  }
  get popupList() {
    if (this.isLogin) {
      return this.props.common.loginPus.filter((item) => item.execution === "A");
    } else {
      return this.props.common.loginPus.filter((item) => item.execution === "B");
    }
  }
  get popupIndex() {
    return this.popupRef?.popupSwiper?.swiper?.activeIndex || 0;
  }
  get promoList() {
    return this.props.common.activity.filter((item) =>
      item.promoAnnouncementCategoryIds?.includes(+this.gamingPromoId)
    );
  }
  get gamingPromoId() {
    return this.props.tcgCommon.promotionCategory?.find((item) => item.type === "gaming")?.id;
  }
  componentDidMount() {
    window.scrollTo(0, 0);
    const session = get("MC_SESSION_INFO");
    session && this.props.common.renew(false).then(this.checkContract);
    this.props.wallet.getMerchantWallet(deploy.merchant);
    if (session) {
      this.props.wallet.getWallet();
    }
  }

  checkContract = async () => {
    let token = get("MC_SESSION_INFO");
    if (token && this.props.common.memberInfo) {
      const userType = this.props.common.memberInfo.type;
      const isNotVirtual = ![4, 40, 41, 42].includes(userType);
      let self = this;
      if (isNotVirtual) {
        account.getContractDividendRequest({ token: getToken() }).then((res) => {
          if (!isEmpty(res.data.value.dailysalreqtype)) {
            Modal.alert(
              this.props.languageShell.resource["in_daily_salary_agt"],
              this.props.languageShell.resource["in_daily_salary_req"],
              [
                {
                  text: this.props.languageShell.resource["in_more_cancel"],
                  onPress: () => {},
                },
                {
                  text: this.props.languageShell.resource["in_go"],
                  onPress: () => {
                    this.props.push("/m/member/agent/getSalaryContract");
                  },
                },
              ]
            );
          }
          if (!isEmpty(res.data.value.divtypecompperiodreq)) {
            Modal.alert(
              this.props.languageShell.resource["in_dividend_agt"],
              this.props.languageShell.resource["in_dividend_req"],
              [
                { text: this.props.languageShell.resource["in_more_cancel"], onPress: () => {} },
                {
                  text: this.props.languageShell.resource["in_go"],
                  onPress: () => {
                    self.props.push(`/m/member/agent/getDevidendContract`);
                  },
                },
              ]
            );
          }
          if (!isEmpty(res.data.value.contracthoursalreq)) {
            Modal.alert(
              this.props.languageShell.resource["in_hour_salary_agt"],
              this.props.languageShell.resource["in_hour_salary_req"],
              [
                { text: this.props.languageShell.resource["in_more_cancel"], onPress: () => {} },
                {
                  text: this.props.languageShell.resource["in_go"],
                  onPress: () => {
                    this.props.push("/m/member/agent/getHourContract");
                  },
                },
              ]
            );
          }
        });
      }
    }
  };

  openResources = (type) => {
    const link = this.resources[type];
    if (link?.url) {
      handleWindowPage(link?.url);
    }
  };

  renderHeader = () => {
    return (
      <div className="shell-popup-header">
        <CommonLogo className="popup-logo" src={require("@/assets/images/logo/logo.png")} />
      </div>
    );
  };

  renderFooter = () => {
    return (
      <div className="shell-popup-footer">
        {/* <div className="popup-nav-title">
          <span>{this.popupList[this.popupRef?.state?.curIndex]?.title}</span>
        </div> */}
        {/* <Pagination
          className="common-pagination"
          currentPage={this.popupRef?.state?.curIndex + 1}
          onChange={this.popupChange}
          totalPages={this.popupList?.length}
          prevText={<Icon type={require("!svg-sprite-loader!@/assets/images/common/page-left.svg")} />}
          nextText={<Icon type={require("!svg-sprite-loader!@/assets/images/common/page-right.svg")} />}
        /> */}
        {this.popupList?.length > 1 && (
          <div className="popup-nav">
            <button className="popup-nav-left" disabled={this.popupRef?.state?.curIndex === 0} onClick={this.popupPrev}>
              <Icon className="nav-icon" type={require("!svg-sprite-loader!@/assets/images/common/arrow-left.svg")} />
            </button>
            <button
              className="popup-nav-right"
              disabled={this.popupRef?.state?.curIndex === this.popupList?.length - 1}
              onClick={this.popupNext}
            >
              <Icon className="nav-icon" type={require("!svg-sprite-loader!@/assets/images/common/arrow-right.svg")} />
            </button>
          </div>
        )}
      </div>
    );
  };

  popupChange = (index) => {
    const popupSwiper = this.popupRef?.popupSwiper?.swiper;
    if (popupSwiper) {
      popupSwiper.slideTo(index - 1, 400, false);
    }
  };

  closePopup = (e) => {
    if (this.popupRef) {
      this.popupRef?.handleClick(true)(e);
    }
  };

  popupPrev = () => {
    const popupSwiper = this.popupRef?.popupSwiper?.swiper;
    if (popupSwiper) {
      popupSwiper.slidePrev();
    }
  };

  popupNext = () => {
    const popupSwiper = this.popupRef?.popupSwiper?.swiper;
    if (popupSwiper) {
      popupSwiper.slideNext();
    }
  };

  setPopupRef = (e) => {
    this.popupRef = e;
  };

  handlePopupHide = () => {
    this.props.tcgCommon.showDownloadPopup(false);
  };

  render() {
    return (
      <div className={`home-container app-main avoid-footer`}>
        <HomeHeader />
        <div className="banner-notice">
          <PageBanner
            className="home-banner"
            banners={this.props.common.banners.filter((item) => item.groupName === "m_home")}
          />
          <NoticeMarquee {...this.props} items={this.props.common.not} />
        </div>
        <div className="home-content-wrap" id="home-content">
          <Home {...this.props} />
        </div>
        <Footer />
        <FloatService />
        <PopupV2
          popupList={this.props.common.loginPus}
          setRef={this.setPopupRef}
          // header={this.renderHeader()}
          footer={this.renderFooter}
        />
        <DownloadPopup
          show={this.props.tcgCommon.isShowDownloadPopup}
          hideHomeScreen={this.props.hideHomeScreen}
          onHide={this.handlePopupHide}
          showAppBonus={this.props.showAppBonus}
          hideIosApp={this.props.hideIosApp}
        />
      </div>
    );
  }
}

export default OutterApp;
