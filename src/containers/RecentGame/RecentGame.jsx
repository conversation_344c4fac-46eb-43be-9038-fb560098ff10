import React from "react";
import { inject, observer } from "mobx-react";

import Footer from "@/components/Footer/Footer";
import HomeHeader from "@/components/OutterHeader/HomeHeader";
import ProviderGameList from "@/components/ProviderGameList/ProviderGameList";
import deploy from "@/config/deploy.config";

import "./RecentGame.scss";

const { gameSelectType } = deploy;

@inject("gameCenter", "auth", "languageShell", "common")
@observer
class RecentGame extends React.Component {
  componentDidMount() {
    window.scrollTo(0, 0);
    this.getFavoriteGameId();
  }
  getFavoriteGameId = () => {
    if (this.props.auth.currentToken) {
      this.props.gameCenter.getFavGames({
        token: this.props.auth.currentToken,
        pageNo: 1,
        pageSize: 200,
      });
    }
  };
  render() {
    return (
      <div className="recent-game-container app-main">
        <HomeHeader />
        <div className="page-title">
          <img className="title-icon" src={require("@/assets/images/common/icon-recent.png")} alt="" />
          <span>{this.props.languageShell.t("in_recent_game")}</span>
        </div>
        <div className="recent-game-content">
          <ProviderGameList dataType={gameSelectType.RECENT} />
        </div>
        <Footer />
      </div>
    );
  }
}

export default RecentGame;
