.app-download-container {
  .download-banner {
    display: flex;
    justify-content: center;

    .banner-img {
      display: block;
      width: 100%;
    }
  }

  .app-download-content {
    padding: 64px 32px;
  }

  .download-title {
    margin-bottom: 40px;

    h6 {
      margin-bottom: 10px;
      font-size: 47px;
      font-weight: 700;
      line-height: normal;
      color: #fff;
      text-align: center;
    }

    p {
      font-size: 30px;
      font-weight: 700;
      line-height: normal;
      text-align: center;
      background: linear-gradient(90deg, #ffa800 0%, #f90 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .download-info {
    margin-bottom: 40px;
  }

  .download-main {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;

    .download-btns {
      display: flex;
      flex-direction: column;
      gap: 80px;
      width: 100%;
    }

    .h-line {
      width: 100%;
      height: 1px;
      background: var(--border-color-surface);
    }

    .download-item {
      display: flex;
      align-items: center;
    }

    .app-info {
      display: flex;
      flex-direction: column;
      gap: 24px;
      font-size: 32px;
      font-weight: 600;
      line-height: normal;
    }

    .app-icon {
      flex-shrink: 0;
      width: 124px;
      height: 124px;
      margin-right: 44px;
    }

    .download-btn {
      @include flex-center;
      position: relative;
      width: fit-content;
      min-width: 267px;
      padding: 16px 24px;
      font-size: 24px;
      font-weight: 900;
      line-height: normal;
      color: var(--text-color-primary);
      letter-spacing: 0.48px;
      background: var(--bg-color-gradient-primary);
      border-radius: 100px;
    }

    .download-for {
      margin-top: 15px;
      font-size: 24px;
      line-height: normal;
      color: #fff;
      text-align: center;
    }
  }

  .scan-download {
    margin-top: 20px;
    font-size: 32px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #118eea;
    text-align: center;
  }

  .download-list {
    display: grid;
    gap: 30px;

    .item-link {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      background: linear-gradient(270deg, #3f0303 0%, #570906 100%);
      border-radius: 16px;
      box-shadow: 0 4px 8px 0 rgb(0 0 0 / 10%);
    }

    .app-info {
      display: flex;
      flex: 1;
      align-items: center;
      overflow: hidden;
    }

    .app-icon {
      width: 88px;
      height: 88px;
      margin-right: 15px;
      border-radius: 32px;
    }
  }

  .download-tip {
    margin-bottom: 32px;
    font-size: 24px;
    font-weight: 400;
    line-height: normal;
    color: #000;
    text-align: center;
  }
}
