import React from "react";
import { withRouter } from "react-router-dom";
import { Icon } from "antd-mobile";
import { inject, observer } from "mobx-react";
import { navigatorUtil, withCs, withDownload } from "tcg-mobile-common";

import Header from "@/components/OutterHeader/Header";

// import IosTutorial from "@/components/AppTutorial/IosTutorial";
import "./Download.scss";

const { isApp, isPWA, isIos, isMobile, isSafari } = navigatorUtil;

@withCs
@withDownload
@inject("common", "languageShell", "mcLanguage", "mcCommon")
@withRouter
@observer
class AppDownload extends React.Component {
  get resources() {
    return this.props.common.downLinkObj;
  }
  componentDidMount() {
    window.scrollTo(0, 0);
  }
  handleIOSDownload = () => {
    const IOS = this.props.common.downLinkObj["IOS"];
    const MOBILECONFIG = this.props.common.downLinkObj["MOBILECONFIG"];
    if (IOS?.url) {
      window.location.href = IOS.url;
      return;
    }
    if (!MOBILECONFIG?.url) {
      return null;
    }
    window.location.href = MOBILECONFIG.url;
    this.props.processProfileDownload();
  };
  handleAndroidDownload = () => {
    const android = this.props.common.downLinkObj["Android"];
    if (android?.url) {
      window.location.href = android.url;
    }
  };
  handleDownload = () => {
    if (isIos) {
      this.handleIOSDownload();
    } else {
      this.handleAndroidDownload();
    }
  };
  canShowProfile() {
    const profile = this.resources["MOBILECONFIG"];
    return profile && window.localStorage.getItem("WEBCLIP") !== "1" && isIos;
  }
  canShowIosAPP() {
    const ios = this.resources["IOS"];
    return !isMobile() || (ios && isIos && !this.props.hideIosApp);
  }
  canShowAndroidAPP() {
    return !isMobile() || !isIos;
  }
  getIcon = () => {
    const host = this.props.mcCommon.imageCDNOne;
    if (this.resources["AppDownloadIcon"]) {
      return this.resources["AppDownloadIcon"]?.url;
    }
    return `${host}/TCG_PROD_IMAGES/B2C/download/${this.props.mcCommon.merchant}/appIcon.png`;
  };
  headerRight = () => {
    return (
      <div className="down-header-right">
        <div className="service-btn" onClick={this.props.openCs}>
          <Icon type={require("!svg-sprite-loader!@/assets/images/common/icon-service.svg")} />
        </div>
      </div>
    );
  };
  checkIosTutorial = () => {
    this.props.common.showIosTutorial(true);
  };
  render() {
    if (isApp || isPWA) {
      return null;
    }
    return (
      <div className="app-download-container app-main">
        <Header />
        <div className="app-download-content">
          {/* <div className="download-banner">
            <img className="banner-img" src={require("@/assets/images/download/download-banner.png")} alt="" />
          </div> */}

          <div className="download-main">
            {/* <div className="download-title">
              <h6>{this.props.languageShell.t("download_title")}</h6>
              <p>{this.props.languageShell.t("download_info")}</p>
            </div> */}
            <div className="download-btns">
              <div className="download-item">
                <img className="app-icon" src={this.getIcon()} alt="homescreen" />
                <div className="app-info">
                  <p>Install iOS application</p>
                  <div className="download-btn" onClick={this.handleIOSDownload}>
                    {this.props.languageShell.t("download")}
                  </div>
                </div>
              </div>
              <div className="h-line" />
              <div className="download-item">
                <img className="app-icon" src={this.getIcon()} alt="homescreen" />
                <div className="app-info">
                  <p>Install Android application</p>
                  <div className="download-btn" onClick={this.handleAndroidDownload}>
                    {this.props.languageShell.t("download")}
                  </div>
                </div>
              </div>
            </div>
            {/* <div className="download-tutorial">
              <span onClick={this.checkIosTutorial}>{this.props.languageShell.t("view_install_tutorial")}</span>
            </div> */}
            {/* <div className="download-list">
              {this.canShowAndroidAPP() && (
                <div className="item-link">
                  <div className="app-info">
                    <img className="app-icon" src={this.getIcon()} alt="homescreen" />
                    <div>
                      <h5>{this.props.mcLanguage.t("app_popup_android")}</h5>
                    </div>
                  </div>
                  <a
                    className="down-btn"
                    rel="nofollow me noopener noreferrer"
                    href={this.resources["Android"]?.url}
                    target={this.props.getDownloadTarget(this.resources["Android"]?.url)}
                  >
                    {this.props.languageShell.t("app_download_app")}
                  </a>
                </div>
              )}
              {this.canShowProfile() && (
                <div className="item-link iphone-link1">
                  <div className="app-info">
                    <img className="app-icon" src={this.getIcon()} alt="homescreen" />
                    <div>
                      <h5>{this.props.mcLanguage.t("app_popup_profile")}</h5>
                    </div>
                  </div>
                  <a
                    className="down-btn"
                    rel="nofollow me noopener noreferrer"
                    href={this.resources["MOBILECONFIG"]?.url}
                    target={this.props.getDownloadTarget(this.resources["MOBILECONFIG"]?.url)}
                    onClick={this.props.processProfileDownload}
                  >
                    {this.props.languageShell.t("app_download_app")}
                  </a>
                </div>
              )}
              {this.canShowIosAPP() && (
                <div className="item-link iphone-link1">
                  <div className="app-info">
                    <img className="app-icon" src={this.getIcon()} alt="APP" />
                    <div>
                      <h5>{this.props.mcLanguage.t("app_popup_ios")}</h5>
                    </div>
                  </div>
                  <a
                    className="down-btn"
                    rel="nofollow me noopener noreferrer"
                    href={this.resources["IOS"]?.url}
                    target={this.props.getDownloadTarget(this.resources["IOS"]?.url)}
                  >
                    {this.props.languageShell.t("app_download_app")}
                  </a>
                </div>
              )}
            </div> */}
            {/* <div className="download-info">
              <h6>{this.props.languageShell.t("click_download")}</h6>
              <p>{this.props.languageShell.t("download_info")}</p>
            </div> */}
          </div>
        </div>
      </div>
    );
  }
}

export default AppDownload;
