//Oddsking
export default {
  SPORTS: {
    OK: {
      gameId: "OK0263",
      nodeId: 8262,
      vassalage: "OK",
    },
    UG: {
      gameId: "UG0001",
      nodeId: 8300,
      vassalage: "UG",
    },
    GG: {
      gameId: "G00001",
      nodeId: 5018,
      vassalage: "GG",
    },
    BBIN: {
      gameId: "BB1002",
      nodeId: 7477,
      vassalage: "BBIN",
    },
  },

  LIVE: {
    AG: {
      gameId: "A00070",
      nodeId: 5079,
      vassalage: "AG",
    },
    MGS: {
      gameId: "M00100",
      nodeId: 4655,
      vassalage: "MGS",
    },
    BBIN: {
      gameId: "BB1001",
      nodeId: 7016,
      vassalage: "BBIN",
    },
    EA: {
      gameId: "EA0001",
      nodeId: 7000,
      vassalage: "EA",
    },
    OPUS: {
      gameId: "OP0001",
      nodeId: 7515,
      vassalage: "OPUS",
    },
    PT: {
      gameId: "P00168",
      nodeId: 73,
      vassalage: "PT",
    },
  },
  FISH: {
    GG: {
      gameId: "G00001",
      nodeId: 5018,
      vassalage: "GG",
    },
    AG: {
      gameId: "A00012",
      nodeId: 10,
      vassalage: "AG",
    },
    PT: {
      gameId: "P00384",
      nodeId: 7474,
      vassalage: "PT",
    },
    CQ: {
      gameId: "CQ0103",
      nodeId: 8522,
      vassalage: "CQ9",
    },
    CQ9: {
      gameId: "CQ0157",
      nodeId: 10293,
      vassalage: "CQ9",
    },
  },
};
