const common = require.context("./i18n", false, /[A-Za-z-]+\.js$/i);
const local = require.context("./local", false, /[A-Za-z-]+\.js$/i);
const i18n = {};

common.keys().forEach((item) => {
  const matched = item.match(/([A-Za-z-]+)\./i);
  if (matched && matched.length > 1) {
    const key = matched[1].toUpperCase();
    i18n[key] = common(item).default;
  }
});

local.keys().forEach((item) => {
  const matched = item.match(/([A-Za-z-]+)\./i);
  if (matched && matched.length > 1) {
    const key = matched[1].toUpperCase();
    const localI18n = local(item).default;
    const exist = i18n[key] || {};
    i18n[key] = { ...exist, ...localI18n };
  }
});

export default i18n;
