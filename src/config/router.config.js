// Game Center
import memberCenter from "tcgmodulemc";

import Login from "../containers/Login/Login";
import OutterApp from "../containers/OutterApp";
import Register from "../containers/Register/Register";

export const routes = [
  {
    path: "/",
    title: "",
    component: OutterApp,
    back: false,
    right: false,
    rightComponent: null,
  },
  {
    path: "/m/login",
    title: "",
    component: Login,
    back: false,
    right: false,
    rightComponent: null,
  },
  {
    path: "/m/register",
    title: "",
    component: Register,
    back: false,
    backPath: "/",
    right: false,
    rightComponent: null,
  },
];
let memberCenterRoutes = memberCenter.routes;
const newRouters = routes.concat(memberCenterRoutes);

export const routeMap = newRouters.reduce((acc, curr) => {
  return {
    ...acc,
    [curr.path]: curr,
  };
}, {});

export default { routeMap, newRouters };
