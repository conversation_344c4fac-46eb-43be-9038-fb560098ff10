import { invertBy, mapValues } from "lodash";

import deploy from "@/config/deploy.config";

export const gamePath = {
  HOME: "/m/home",
  LIVE: "/m/live-games",
  RNG: "/m/slot-games",
  SPORTS: "/m/sport-games",
  ESPORTS: "/m/esport-games",
  FISH: "/m/fish-games",
  PVP: "/m/pvp-games",
  LOTT: "/m/lott",
  ELOTT: "/m/elott",
  HOT: "/m/hot-games",
  SEARCH: "/m/search",
  RECENT: "/m/recent",
  INHOUSE: "/m/inHouse",
  FAV: "/m/fav-games",
  COCKFIGHT: "/m/cockfight",
  NEW: "/m/new-games",
  JL: "/m/jili-games",
  RTG: "/m/rtg-games",
  PROMO: "/m/activity",
  HELP: "/m/help",
  JACKPOT: "/m/jackpots",
  BINGO: "/m/bingo",
  FEATURE: "/m/slot-games?gameClassify=FeatureBuy",
};

const invertedObj = invertBy(gamePath);
export const pathGameType = mapValues(invertedObj, (values) => values[0]);

export const homePath = ["/", "/m/", "/m/home", "/m/index", "/m/index.html"];

export const downloadBarPath = [...homePath];

export const gameListPath = [gamePath.LIVE, gamePath.RNG, gamePath.PVP, gamePath.FAV, gamePath.FISH];

export const gameVendorPath = [gamePath.SPORTS, gamePath.ESPORTS, gamePath.LOTT, gamePath.ELOTT, gamePath.COCKFIGHT];

export const navConfig = {
  HOME: {
    name: "hd_home",
    icon: require("!svg-sprite-loader!@/assets/images/common/nav-home.svg"),
    img: require("@/assets/images/common/nav-home.png"),
    imgActive: require("@/assets/images/common/nav-hot-active.png"),
  },
  HOT: {
    name: "hot",
    icon: require("!svg-sprite-loader!@/assets/images/common/nav-hot.svg"),
    img: require("@/assets/images/common/nav-hot.png"),
    imgActive: require("@/assets/images/common/nav-hot-active.png"),
  },
  FAV: {
    name: "in_favorite",
    img: require("@/assets/images/common/nav-fav.png"),
    imgActive: require("@/assets/images/common/nav-fav-active.png"),
  },
  NEW: {
    name: "game_new",
  },
  RECENT: {
    name: "in_recent_game",
  },
  PVP: {
    name: "PVP",
    icon: require("!svg-sprite-loader!@/assets/images/common/nav-pvp.svg"),
    img: require("@/assets/images/common/nav-pvp.png"),
    imgActive: require("@/assets/images/common/nav-pvp-active.png"),
  },
  FISH: {
    name: "fishing",
    icon: require("!svg-sprite-loader!@/assets/images/common/nav-fish.svg"),
    img: require("@/assets/images/common/nav-fish.png"),
    imgActive: require("@/assets/images/common/nav-fish-active.png"),
  },
  RNG: {
    name: "RNG",
    icon: require("!svg-sprite-loader!@/assets/images/common/nav-slot.svg"),
    img: require("@/assets/images/common/nav-slot.png"),
    imgActive: require("@/assets/images/common/nav-slot-active.png"),
  },
  LIVE: {
    name: "hd_live",
    icon: require("!svg-sprite-loader!@/assets/images/common/nav-live.svg"),
    img: require("@/assets/images/common/nav-live.png"),
    imgActive: require("@/assets/images/common/nav-live-active.png"),
  },
  SPORTS: {
    name: "SPORTS",
    icon: require("!svg-sprite-loader!@/assets/images/common/nav-sport.svg"),
    img: require("@/assets/images/common/nav-sport.png"),
    imgActive: require("@/assets/images/common/nav-sport-active.png"),
  },
  ESPORTS: {
    name: "ESPORTS",
    icon: require("!svg-sprite-loader!@/assets/images/common/nav-esport.svg"),
    img: require("@/assets/images/common/nav-esport.png"),
    imgActive: require("@/assets/images/common/nav-esport-active.png"),
  },
  COCKFIGHT: {
    name: "hd_cockfight",
    icon: require("!svg-sprite-loader!@/assets/images/common/nav-cockfight.svg"),
    img: require("@/assets/images/common/nav-cockfight.png"),
    imgActive: require("@/assets/images/common/nav-cockfight-active.png"),
  },
  LOTT: {
    name: "LOTT",
    icon: require("!svg-sprite-loader!@/assets/images/common/nav-lottery.svg"),
    img: require("@/assets/images/common/nav-lottery.png"),
    imgActive: require("@/assets/images/common/nav-lottery-active.png"),
  },
  ELOTT: {
    name: "LOTT",
    icon: require("!svg-sprite-loader!@/assets/images/common/nav-lottery.svg"),
    img: require("@/assets/images/common/nav-lottery.png"),
    imgActive: require("@/assets/images/common/nav-lottery-active.png"),
  },
};

export const coverVassalage = (vassalage) => {
  if (!deploy.supportLanguages.includes("TY")) {
    return vassalage;
  }
  if (vassalage === "BTI" || vassalage.startsWith("TCG")) {
    return `123BET`;
  }
  return vassalage;
};
