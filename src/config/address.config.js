/**
 * Web Service address configuration.
 */
export default {
  game: {
    winnerBoard: "/wps/relay/GCSGAME_getRankList",
    vassgameType: "/wps/relay/GCSGAME_getVassGameType",
    gamelist: "/wps/relay/GCSGAME_gameList",
    addFavGame: "/wps/relay/MCSFE_addFavoriteGameV2",
    removeFavGame: "/wps/relay/MCSFE_removeFavoriteGameV2",
    getFavGames: "/wps/relay/MCSFE_getFavoriteGames",
    getFavGameList: "/wps/relay/MCSFE_getFavoriteGamesList",
    addRecentGame: "wps/relay/MCS2_addRecentGame",
    getRecentGameList: "/wps/relay/GCS_recentGame",
    getLottList: "/lgw/games/mobile",
    launchGame: "/wps/game/launchGame",
    numero: "/lotto/lgw/games/opened/numero",
    gameHot: "/wps/relay/GCSGAME_hotGamesV2",
    lottMenu: "/lgw/vn/games/game_menu",
    vnNumero: "/lgw/vn/numeros/opened/game",
    recentVNLott: "/lgw/vn/games/game_menu/recent",
    gameLaunchFB: "/wps/relay/GCS_gameLaunchFB",
    gameMenus: "/lgw/games/mobile_game_menus",
  },
  gameRecord: {
    lottOrders: "/lgw/orders",
    lottOrderDetail: "/lgw/orders/detail",
    cancelOrder: "/lgw/orders/suborders/cancel",
    thirdGameRecord: "/wps/relay/",
  },
  announcements: {
    bannerNew: "/wps/relay/MCSFE_getListAnnouncements",
    markAsRead: "/wps/relay/MCSFE_markAnnouncementAsRead",
    //轮播公告旧api---->notice: "/wps/system/announcements",
    ///轮播公告新api
    noticeNew: "/wps/relay/GLS2_lobbyNews",
    populor: "/lgw/games/popular",
    signUpPromotionJoin: "/wps/relay/MCSFE_signUpPromotionJoin",
    signUpRequestReward: "/wps/relay/MCSFE_signUpRequestReward",
  },
  getService: {
    service: "/wps/system/domainRoute",
  },
  agent: {
    kpiA: "/wps/relay/ODSFE2_TeamKPIA",
    kpiB: "/wps/relay/ODSFE2_TeamKPIB",
    downline: "/wps/relay/ODSFE2_Subordinate",
    undateRebate: "/wps/agent/updateDownlineRebates",
    currentDailySalary: "/wps/relay/ODSFE2_CurrentDailySalary",
    getContractDailySalaryInfo: "/wps/relay/MCS2_getContractDailySalInfo",
    teamKPIC: "/wps/relay/ODSFE2_teamKPIC",
  },
  member: {
    info: "/wps/member/info",
  },
  account: {
    login: "/wps/session/login",
    register: "/wps/member/register",
    registerMobile: "/wps/member/register/mobile",
    regValidata: "/wps/system/setting/register",
    affiliate: "/wps/agent/affiliate",
    trial: "/wps/member/trialuser",
    logout: "/wps/session/logout",
    captcha: "/wps/captcha",
    foxmail: "/wps/member/password/email",
    transdownline: "/wps/agent/transferDownline",
    username: "/wps/check/username",
    testPlay: "/wps/session/login/freeplay",
    verification: "/wps/system/setting/register",
    headIcon: "/wps/member/info/icon",
    setTemplate: "/wps/system/settings/consolidated",
    setRegister: "/wps/system/setting/register",
    getCode: "/wps/verification/sms/noLogin",
    sendVerificationCode: "/wps/verification/sms/register",
    launchChat: "/wps/relay/CBS_launchChat",
    systemCountry: "/wps/system/country",
    inboxUnreadCount: "/wps/relay/CSP_unreadCount",
    helpCenter: "/wps/system/helpCenter",
    setPasswordPhone: "/wps/member/password/verification",
    feedback: "/wps/feedback",
    getPlayerRankProgress: "/wps/relay/MCSFE_getPlayerRankProgress",
    getDomainRoute: "/wps/system/domainRoute",
  },
  report: {
    memberPNL: "/wps/relay/ODSFE2_MemPNL",
  },
  whitelabel: {
    menu: "/wps/system/whitelabel",
    vassgameType: "/wps/relay/GCSGAME_getVassGameType",
    systemStatus: "/wps/system/status",
  },
  wallet: {
    balance: "/wps/wallets/gcs",
    transfer: "/wps/relay/GCS_walletTransfer",
    transferDetails: "/wps/relay/ODS2_MemberTransaction",
    txCode: "/wps/relay/GLS2_TxCodeList",
  },
  agentRegister: {
    globalRebateSettings: "/wps/relay/MCS2_getGlobalRebateSettingsByProductType",
    lottoCustomersSeries: "/wps/relay/MCS2_getCustomerRebateSettingByProductType",
    quotas: "/wps/relay/MCS2_getAgentQuotaGroupByProduct",
    affiliateUrls: "/wps/relay/MCS2_wpsViewAffUrls",
    affiliateUrl: "/wps/relay/MCS2_wpsDeleteAffUrl",
    downlineLink: "/wps/relay/MCS2_wpsRegAff",
    createAffUrl: "/wps/relay/MCS2_wpsCreateAffUrl",
    checkUsername: "/wps/check/username",
    downline: "/wps/agent/downline",
  },
  redRain: {
    downlines: "/wps/agent/downlines",
    transferMultipleDownline: "/wps/agent/transferMultipleDownline",
  },
  withdraw: {
    bankCard: "/wps/relay/MCS2_bankCards",
    withdrawMoney: "/wps/transaction/withdraw",
    //提现报表
    report: "/wps/relay/MCS2_WithdrawTransactionReport",
  },
  deposit: {
    bankList: "/wps/relay/MCS2_depositBanksByVendor",
    activePro: "/wps/relay/MCS2_depositPromoList",
    translateMT: "/wps/relay/MCS2_depositManualTransfer",
    translateAlipay: "/wps/relay/MCS2_DepositAlipay",
    translateQQ: "/wps/relay/MCS2_DepositQQ",
    translatePG: "/wps/relay/MCS2_depositPaymentGateway",
    translateWeChat: "/wps/relay/MCS2_depositWechat",
    discounts: "/wps/relay/MCS2_acceptPromo",
    //充值报表
    records: "/wps/relay/MCS2_depositTransactioNReport",
    //代充报表
    agencyRecords: "/wps/relay/MCS2_depositTransactioNReport",
  },
  securityList: {
    securityConfig: "/wps/member/securityConfig",
    passWord: "/wps/member/password",
    hasPassword: "/wps/member/hasWithdrawalPassword",
    fundsPassword: "/wps/member/paymentPassword",
    fundsPassword1: "/wps/member/paymentPassword",
    bindCardBase: "/wps/member/bankCard",
    getCardList: "/wps/relay/MCS2_bankCards",
    bankCardList: "/wps/relay/MCS2_withdrawBanks",
    //设定客户安全问题
    firstPasswordSet: "/wps/member/customerSecurityQuestions",
    //取得用户安全问题
    customerSecQuestions: "/wps/member/customerSecurityQuestions",
    //获得安全问题
    getSecurityQuestions: "/wps/member/securityQuestions",
    //重置资金密码
    resetfundsPassword: "/wps/member/paymentPassword/reset",
    //绑定邮箱
    bindEmails: "/wps/member/info/email",
    //绑定取款人
    bindPayee: "/wps/member/info/payeename",
  },
  personalInfo: {
    setPersonalInfo: "/wps/member/info",
    setNickName: "/wps/member/info/nickname",
  },
  // 代理分红
  agentDividend: {
    // 当前页
    current: "/wps/relay/ODSFE2_CurrentDividend",
    // 分红信息
    info: "/wps/relay/MCS2_getContractDividendInfo",
    // 分红历史
    history: "/wps/relay/ODSFE2_QueryDividend",
    // 派发
    payOut: "/wps/relay/ODSFE2_PayoutDividend",
    message: "/wps/relay/ODS2_AgentPrevAndCurrIncome",
    getContractDividendRequest: "/wps/member/info/funds/consolidated",
  },
  //代理日工资
  agentSalary: {
    currentDailySalary: "/wps/relay/ODSFE2_CurrentDailySalary",
    getContractDailySalaryInfo: "/wps/relay/MCS2_getContractDailySalInfo",
    QueryDailySalary: "/wps/relay/ODSFE2_QueryDailySalary",
    applyContractDailySal: "/wps/relay/MCS2_applyContractDailySal",
    processDailySalContract: "/wps/relay/MCS2_processDailySalContract",
    getContracDailySalReq: "/wps/relay/MCS2_getContracDailySalReq",
    getDailySalContractHist: "/wps/relay/MCS2_getDailySalContractHist",
  },
  chat: {
    anchorList: "/wps/relay/CBS_modelList",
    launchChat: "/wps/relay/CBS_launchChat",
    unreadCount: "/wps/relay/CBS_unreadCount",
  },
  live: {
    modelList: "/wps/relay/CBS_modelList",
    launchChat: "/wps/relay/CBS_launchChat",
  },
};
