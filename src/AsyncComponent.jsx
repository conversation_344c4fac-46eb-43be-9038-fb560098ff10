import React from "react";
import Loadable from "react-loadable";

import SpinnerLoading from "./components/SpinnerLoading";

export default function AsyncComponent(importComponent) {
  window.scrollTo(0, 0);
  return Loadable({
    loader: () => import(`./containers/${importComponent}`),
    loading: (props) => {
      if (props.error) {
        console.error("Async route failed", props.error);
      }
      return <SpinnerLoading />;
    },
  });
}
