import React from "react";
import { throttle } from "lodash";
import PropTypes from "prop-types";

import { getScrollParent } from "@/utils/scroll";

class EasySticky extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      fixed: false,
      width: 0,
      height: 0,
      transform: 0,
    };
    this.root = null;
    this.scrollParent = null;
  }

  componentDidMount() {
    this.updateScrollParent();
    this.initResizeObserver();
  }

  componentWillUnmount() {
    this.removeScrollListener();
    this.resizeObserver?.disconnect();
    this.onScroll.cancel();
  }

  componentDidUpdate(prevProps, prevState) {
    const { onChange, container } = this.props;
    if (prevState.fixed !== this.state.fixed && onChange) {
      onChange(this.state.fixed);
    }
    if (prevProps.container !== container) {
      this.updateScrollParent();
    }
  }

  initResizeObserver = () => {
    this.resizeObserver = new ResizeObserver((entries) => {
      const { width, height } = entries[0].contentRect;
      this.setState({ width, height });
    });
    if (this.root) {
      this.resizeObserver.observe(this.root);
    }
  };

  updateScrollParent = () => {
    if (!this.root) return;

    this.removeScrollListener();

    this.scrollParent = getScrollParent(this.root);

    if (this.scrollParent) {
      this.scrollParent.addEventListener("scroll", this.onScroll);
      this.onScroll();
    }
  };

  removeScrollListener = () => {
    if (this.scrollParent) {
      this.scrollParent.removeEventListener("scroll", this.onScroll);
    }
  };

  getScrollTop = (el) => {
    const top = "scrollTop" in el ? el.scrollTop : el.pageYOffset;
    return Math.max(top, 0);
  };

  isHidden = (element) => {
    if (!element) {
      return false;
    }
    const style = window.getComputedStyle(element);
    const hidden = style.display === "none";
    const parentHidden = element.offsetParent === null && style.position !== "fixed";
    return hidden || parentHidden;
  };

  onScroll = throttle(() => {
    const { position, offsetTop, offsetBottom, container, onScroll } = this.props;

    if (!this.root || this.isHidden(this.root)) {
      return;
    }

    const rootRect = this.root.getBoundingClientRect();
    const scrollTop = this.getScrollTop(window);
    let currentState = { ...this.state };

    currentState.width = rootRect.width;
    currentState.height = rootRect.height;

    const offset = position === "top" ? offsetTop : offsetBottom;

    if (position === "top") {
      if (container) {
        const containerRect = container.getBoundingClientRect();
        const difference = containerRect.bottom - offset - currentState.height;
        currentState.fixed = offset > rootRect.top && containerRect.bottom > 0;
        currentState.transform = difference < 0 ? difference : 0;
      } else {
        currentState.fixed = offset > rootRect.top;
      }
    } else {
      const { clientHeight } = document.documentElement;
      if (container) {
        const containerRect = container.getBoundingClientRect();
        const difference = clientHeight - containerRect.top - offset - currentState.height;
        currentState.fixed = clientHeight - offset < rootRect.bottom && clientHeight > containerRect.top;
        currentState.transform = difference < 0 ? -difference : 0;
      } else {
        currentState.fixed = clientHeight - offset < rootRect.bottom;
      }
    }

    this.setState(currentState);

    onScroll?.(scrollTop, currentState.fixed);
  }, 16);

  render() {
    const { children, position, zIndex } = this.props;
    const { fixed, width, height, transform } = this.state;

    const rootStyle = fixed
      ? {
          width: `${width}px`,
          height: `${height}px`,
        }
      : {};

    const stickyStyle = fixed
      ? {
          width: `${width}px`,
          height: `${height}px`,
          [position]: `${position === "top" ? this.props.offsetTop : this.props.offsetBottom}px`,
          zIndex,
          transform: transform ? `translate3d(0, ${transform}px, 0)` : undefined,
        }
      : {};

    return (
      <div
        ref={(ref) => {
          this.root = ref;
        }}
        style={rootStyle}
      >
        <div className={fixed ? "sticky--fixed" : ""} style={stickyStyle}>
          {children}
        </div>
      </div>
    );
  }
}

EasySticky.propTypes = {
  position: PropTypes.oneOf(["top", "bottom"]), // 定位位置 top bottom
  container: PropTypes.element, // 指定容器
  offsetTop: PropTypes.number, // 顶部距离 remToPx转换
  offsetBottom: PropTypes.number, // 底部距离 remToPx转换 position: bottom 时使用
  onScroll: PropTypes.func, // 回调函数，获取滚动信息 params: scrollTop fixed
  onChange: PropTypes.func, // 回调函数，fixed变化时调用
  zIndex: PropTypes.number, // 定位层级
};

EasySticky.defaultProps = {
  position: "top",
  offsetTop: 0,
  offsetBottom: 0,
  zIndex: 33,
};

export default EasySticky;
