.common-popup-wrap {
  position: fixed;

  &.popup-center {
    top: 50%;
    left: 50%;
    width: fit-content;
    transform: translate3d(-50%, -50%, 0);
  }

  &.popup-top {
    top: 0;
  }

  &.popup-bottom {
    bottom: 0;
  }

  &.popup-left {
    left: 0;
  }

  &.popup-right {
    right: 0;
  }

  .popup-close {
    position: absolute;
    top: 24px;
    right: 24px;
    z-index: 10;
    width: 48px;
    height: 48px;

    .am-icon,
    img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .common-popup-content {
    position: relative;
    max-height: calc(var(--vh, 1vh) * 100);
    overflow-y: auto;
  }
}

.popup-zoom-enter {
  .common-popup-content {
    transform: scale(0);
  }
}

.popup-zoom-enter-active {
  .common-popup-content {
    transform: scale(1);
    transition: transform var(--popup-duration);
    transition-timing-function: var(--popup-timing-function);
  }
}

.popup-zoom-exit {
  .common-popup-content {
    transform: scale(1);
  }
}

.popup-zoom-exit-active {
  .common-popup-content {
    transform: scale(0);
    transition: transform var(--popup-duration);
    transition-timing-function: var(--popup-timing-function);
  }
}

.popup-slide-top-enter {
  transform: translateY(-100%);
}

.popup-slide-top-enter-active {
  transform: translateY(0);
  transition: transform var(--popup-duration);
  transition-timing-function: var(--popup-timing-function);
}

.popup-slide-top-exit {
  transform: translateY(0);
}

.popup-slide-top-exit-active {
  transform: translateY(-100%);
  transition: transform var(--popup-duration);
  transition-timing-function: var(--popup-timing-function);
}

.popup-slide-bottom-enter {
  transform: translateY(100%);
}

.popup-slide-bottom-enter-active {
  transform: translateY(0);
  transition: transform var(--popup-duration);
  transition-timing-function: var(--popup-timing-function);
}

.popup-slide-bottom-exit {
  transform: translateY(0);
}

.popup-slide-bottom-exit-active {
  transform: translateY(100%);
  transition: transform var(--popup-duration);
  transition-timing-function: var(--popup-timing-function);
}

.popup-slide-left-enter {
  transform: translateX(-100%);
}

.popup-slide-left-enter-active {
  transform: translateX(0);
  transition: transform var(--popup-duration);
  transition-timing-function: var(--popup-timing-function);
}

.popup-slide-left-exit {
  transform: translateX(0);
}

.popup-slide-left-exit-active {
  transform: translateX(-100%);
  transition: transform var(--popup-duration);
  transition-timing-function: var(--popup-timing-function);
}

.popup-slide-right-enter {
  transform: translateX(100%);
}

.popup-slide-right-enter-active {
  transform: translateX(0);
  transition: transform var(--popup-duration);
  transition-timing-function: var(--popup-timing-function);
}

.popup-slide-right-exit {
  transform: translateX(0);
}

.popup-slide-right-exit-active {
  transform: translateX(100%);
  transition: transform var(--popup-duration);
  transition-timing-function: var(--popup-timing-function);
}

.popup-fade-enter {
  opacity: 0;
}

.popup-fade-enter-active {
  opacity: 1;
  transition: opacity var(--popup-duration);
  transition-timing-function: var(--popup-timing-function);
}

.popup-fade-exit {
  opacity: 1;
}

.popup-fade-exit-active {
  opacity: 0;
  transition: opacity var(--popup-duration);
  transition-timing-function: var(--popup-timing-function);
}
