import React from "react";
import { CSSTransition } from "react-transition-group";
import cz from "classnames";
import PropTypes from "prop-types";

import Overlay from "@/ui/Overlay";
import { getCssVarNumber } from "@/utils/dom";
import { callInterceptor } from "@/utils/interceptor";

import LockScroll from "../LockScroll";

import PopupPortal from "./PopupPortal";

let globalIndex = 1000;

const IconClose = () => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="icon-close"
  >
    <line x1="18" y1="6" x2="6" y2="18" />
    <line x1="6" y1="6" x2="18" y2="18" />
  </svg>
);

class CommonPopup extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      zIndex: props.zIndex || globalIndex,
      visible: props.show,
      animatedVisible: false,
    };
    this.popupRef = null;
  }
  componentDidMount() {
    window.addEventListener("popstate", this.close);
  }

  componentWillUnmount() {
    window.removeEventListener("popstate", this.close);
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.show !== this.props.show) {
      this.setState(
        {
          visible: this.props.show,
        },
        () => {
          if (this.props.show) {
            this.setState({
              animatedVisible: true,
            });
          }
        }
      );
    }
  }
  open = () => {
    this.setState({
      zIndex: this.props.zIndex !== undefined ? +this.props.zIndex : globalIndex++,
    });

    this.props.onOpen?.();
  };
  close = () => {
    callInterceptor(this.props.beforeClose, {
      args: ["close"],
      done: () => {
        this.props.onClose?.();
      },
    });
  };
  onEntered = () => {
    this.props.onOpened?.();
  };
  onExited = () => {
    this.setState({ animatedVisible: false }, () => this.props.onClosed?.());
  };
  getAnimateName = () => {
    const { position, motion } = this.props;
    if (position === "center") {
      return motion === "fade" ? "popup-fade" : "popup-zoom";
    }
    const animations = {
      bottom: "popup-slide-bottom",
      right: "popup-slide-right",
      left: "popup-slide-left",
      top: "popup-slide-top",
    };
    return animations[position];
  };
  onClickOverlay = (event) => {
    this.props.onClickOverlay?.(event);
    if (this.props.closeOnClickOverlay) {
      this.close();
    }
  };
  renderOverlay = () => {
    const { overlay, overlayClass } = this.props;
    if (overlay) {
      return (
        <Overlay
          show={this.state.visible}
          className={overlayClass}
          zIndex={this.state.zIndex}
          onClick={this.onClickOverlay}
          lockScroll={this.props.lockScroll}
        />
      );
    }
    return null;
  };
  renderCloseIcon = () => {
    const { closeable, closeIcon } = this.props;
    if (closeable) {
      return (
        <div className="popup-close" onClick={this.close}>
          {closeIcon || <IconClose />}
        </div>
      );
    }
    return null;
  };
  renderPopup = () => {
    const { className, destroyOnClose, position, closeIconPlacement } = this.props;
    const style = {
      zIndex: this.state.zIndex,
      display: !this.state.visible && !this.state.animatedVisible ? "none" : undefined,
      ...this.props.style,
    };

    return (
      <CSSTransition
        in={this.state.visible}
        timeout={getCssVarNumber("--popup-duration")}
        classNames={this.getAnimateName()}
        onEnter={this.open}
        onEntered={this.onEntered}
        onExited={this.onExited}
        unmountOnExit={destroyOnClose}
        mountOnEnter={true}
      >
        <div className={cz("common-popup-wrap", `popup-${position}`, className)} style={style}>
          {closeIconPlacement === "outside" && this.renderCloseIcon()}
          <div className="common-popup-content">
            {closeIconPlacement === "inside" && this.renderCloseIcon()}
            {this.props.children}
          </div>
        </div>
      </CSSTransition>
    );
  };
  render() {
    return (
      <PopupPortal>
        {this.renderOverlay()}
        {this.renderPopup()}
        {this.props.lockScroll && <LockScroll shouldLock={this.state.visible} />}
      </PopupPortal>
    );
  }
}

CommonPopup.propTypes = {
  show: PropTypes.bool,
  className: PropTypes.string,
  overlayClass: PropTypes.string, //遮罩层类名
  closeable: PropTypes.bool, //是否显示关闭按钮
  closeIcon: PropTypes.node, //自定义关闭图标
  closeIconPlacement: PropTypes.oneOf(["inside", "outside"]), //关闭按钮放置位置
  position: PropTypes.oneOf(["bottom", "center", "right", "left", "top"]), //弹出位置
  motion: PropTypes.oneOf(["zoom", "fade"]), //position:center时可选动画
  overlay: PropTypes.bool, //是否显示遮罩
  lockScroll: PropTypes.bool, //是否锁定背景滚动
  zIndex: PropTypes.number, //自定义层级
  destroyOnClose: PropTypes.bool, //关闭时销毁 Popup
  closeOnClickOverlay: PropTypes.bool, //是否点击遮罩层关闭弹窗
  // event
  onOpen: PropTypes.func, //打开弹窗触发
  onClose: PropTypes.func, //关闭弹窗触发
  onOpened: PropTypes.func, //打开弹窗动画结束后触发
  onClosed: PropTypes.func, //关闭弹窗动画结束后触发
  onClickOverlay: PropTypes.func, //点击遮罩层
  beforeClose: PropTypes.func, //关闭前拦截函数，可以返回Promise，或者返回 true 允许关闭，false 阻止关闭
};

CommonPopup.defaultProps = {
  show: false,
  className: "",
  overlayClass: "",
  closeable: true,
  closeIconPlacement: "inside",
  position: "center",
  motion: "zoom",
  overlay: true,
  lockScroll: true,
  destroyOnClose: true,
  closeOnClickOverlay: false,
};

export default CommonPopup;
