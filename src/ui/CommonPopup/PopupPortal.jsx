import { createPortal } from "react-polyfill-portal";

function getPortalRoot() {
  let root = document.getElementById("popup-portal-root");
  if (!root) {
    root = document.createElement("div");
    root.id = "popup-portal-root";
    root.style.display = "contents";
    document.body.appendChild(root);
  }
  return root;
}

function PopupPortal(props) {
  const popupRoot = getPortalRoot();
  return createPortal(props.children, popupRoot);
}

export default PopupPortal;
