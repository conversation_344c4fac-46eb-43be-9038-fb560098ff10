import React from "react";
import cz from "classnames";
import PropTypes from "prop-types";

const PageEllipsis = () => (
  <div className="page-ellipsis">
    <span>•••</span>
  </div>
);

class Pagination extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      internalPage: props.currentPage || 1,
    };
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.currentPage !== this.props.currentPage) {
      this.setState({ internalPage: this.props.currentPage });
    }
  }

  handleChange = (newPage) => {
    const { currentPage, onChange } = this.props;
    const { internalPage } = this.state;
    if (newPage === internalPage) return;
    if (typeof currentPage !== "number") {
      this.setState({ internalPage: newPage });
    }
    onChange?.(newPage);
  };

  handleNext = () => {
    const currentPage = this.getCurrentPage();
    const total = this.getTotalPages();
    if (currentPage < total) {
      this.handleChange(currentPage + 1);
    }
  };

  handlePrev = () => {
    const currentPage = this.getCurrentPage();
    if (currentPage > 1) {
      this.handleChange(currentPage - 1);
    }
  };

  getCurrentPage = () => this.props.currentPage || this.state.internalPage;

  getTotalPages = () => parseInt(this.props.totalPages, 10);

  getMaxLength = () => parseInt(this.props.showPageSize, 10);

  renderPageItem = (number, text, active) => {
    const { pageRender } = this.props;
    if (pageRender) return pageRender({ number, text, active });
    return (
      <div key={number} className={cz("page-item", { on: active })} onClick={() => this.handleChange(number)}>
        {text}
      </div>
    );
  };

  renderPages = () => {
    const totalPages = this.getTotalPages();
    const currentPage = this.getCurrentPage();
    const maxLength = this.getMaxLength();
    const pages = [];

    const showPageRange = () => {
      if (totalPages <= maxLength) {
        // 总页数少于等于最大显示页数，全部渲染
        for (let i = 1; i <= totalPages; i++) {
          pages.push(this.renderPageItem(i, String(i), currentPage === i));
        }
      } else {
        // 总页数超过最大显示数量，按规则渲染
        pages.push(this.renderPageItem(1, "1", currentPage === 1));

        const middleCount = maxLength - 2; // 除去首页尾页，能展示的中间页数量

        let start = currentPage - Math.floor(middleCount / 2);
        let end = currentPage + Math.floor(middleCount / 2);

        if (start < 2) {
          start = 2;
          end = start + middleCount - 1;
        }

        if (end > totalPages - 1) {
          end = totalPages - 1;
          start = end - middleCount + 1;
        }

        if (start > 2) {
          pages.push(<PageEllipsis key="ellipsis-start" />);
        }

        for (let i = start; i <= end; i++) {
          pages.push(this.renderPageItem(i, String(i), currentPage === i));
        }

        if (end < totalPages - 1) {
          pages.push(<PageEllipsis key="ellipsis-end" />);
        }

        pages.push(this.renderPageItem(totalPages, String(totalPages), currentPage === totalPages));
      }
    };

    showPageRange();

    const prev = (
      <div key="prev" className={cz("page-nav page-left", { disabled: currentPage === 1 })} onClick={this.handlePrev}>
        {this.props.prevText || <span className="default-prev">Prev</span>}
      </div>
    );

    const next = (
      <div
        key="next"
        className={cz("page-nav page-right", { disabled: currentPage === totalPages })}
        onClick={this.handleNext}
      >
        {this.props.nextText || <span className="default-next">Next</span>}
      </div>
    );

    return [prev, ...pages, next];
  };

  render() {
    const total = this.getTotalPages();
    if (total < 2) return null;
    return (
      <div className={cz("pagination-container", this.props.className)}>
        <div className="pagination-wrapper">{this.renderPages()}</div>
      </div>
    );
  }
}

Pagination.propTypes = {
  className: PropTypes.string,
  prevText: PropTypes.node,
  nextText: PropTypes.node,
  currentPage: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  totalPages: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  showPageSize: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  pageRender: PropTypes.func,
  onChange: PropTypes.func.isRequired,
};

Pagination.defaultProps = {
  className: "",
  totalPages: 1,
  showPageSize: 5,
};

export default Pagination;
