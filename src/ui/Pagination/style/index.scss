:root {
  --pagination-item-width: 52px;
  --pagination-item-height: 52px;
  --pagination-item-default-background: #fff;
  --pagination-item-active-background: #00aeef;
  --pagination-item-border-radius: 6px;
  --pagination-font-size: 24px;
  --pagination-default-font-color: #1c6783;
  --pagination-active-font-color: #fff;
  --pagination-disabled-opacity: 0.6;
}

.pagination-container {
  display: flex;
  justify-content: center;

  .pagination-wrapper {
    display: flex;
    gap: 10px;
    align-items: center;

    .page-item {
      @include flex-center;
      min-width: var(--pagination-item-width);
      height: var(--pagination-item-height);
      font-size: var(--pagination-font-size);
      line-height: normal;
      color: var(--pagination-default-font-color);
      background: var(--pagination-item-default-background);
      border-radius: var(--pagination-item-border-radius);

      &.on {
        color: var(--pagination-active-font-color);
        background: var(--pagination-item-active-background);
      }
    }
  }

  .page-nav {
    @include flex-center;
    width: var(--pagination-item-width);
    height: var(--pagination-item-height);
    color: var(--pagination-default-font-color);
    background: var(--pagination-item-default-background);
    border-radius: var(--pagination-item-border-radius);

    &.disabled {
      opacity: var(--pagination-disabled-opacity);
    }
  }

  .page-ellipsis {
    @include flex-center;
    width: var(--pagination-item-width);
    height: var(--pagination-item-height);
    font-size: var(--pagination-font-size);
    color: var(--pagination-default-font-color);
  }
}
