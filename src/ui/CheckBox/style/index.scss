:root {
  --checkbox-size: 32px;
  --checkbox-border-radius: 5px;
  --checkbox-border: 1px solid #bcbcbc;
  --checkbox-checked-border: 1px solid #fff;
  --checkbox-background-color: transparent;
  --checkbox-checked-background-color: transparent;
  --checkbox-checked-icon-size: 20px;
  --checkbox-checked-icon-color: #ff0;
  --checkbox-label-margin: 12px;
}

.checkbox-item {
  position: relative;
  display: flex;
  align-items: center;
  overflow: hidden;
  cursor: pointer;

  &-select {
    .checkbox-item-bg {
      background: var(--checkbox-checked-background-color);
      border: var(--checkbox-checked-border);

      .checkbox-icon {
        display: block;
      }
    }
  }

  &-bg {
    @include flex-center;
    position: relative;
    flex-shrink: 0;
    width: var(--checkbox-size);
    height: var(--checkbox-size);
    background: var(--checkbox-background-color);
    border: var(--checkbox-border);
    border-radius: var(--checkbox-border-radius);
  }

  .checkbox-icon {
    display: none;
    width: var(--checkbox-checked-icon-size);
    height: var(--checkbox-checked-icon-size);
    color: var(--checkbox-checked-icon-color);
  }

  .checkbox-label {
    margin-left: var(--checkbox-label-margin);
  }
}
