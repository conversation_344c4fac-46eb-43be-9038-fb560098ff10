import React from "react";
import { Icon } from "antd-mobile";
import cz from "classnames";
import PropTypes from "prop-types";

class CheckBox extends React.Component {
  constructor(props) {
    super(props);
    this.state = { checked: false };
  }
  componentDidMount() {
    this.setState({ checked: this.props.checked });
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.checked !== this.props.checked) {
      this.setState({
        checked: this.props.checked,
      });
    }
  }

  change = () => {
    this.setState(
      {
        checked: !this.state.checked,
      },
      () => {
        this.props.onChange?.(this.state.checked);
      }
    );
  };
  render() {
    const { checked } = this.state;
    let props = this.props;
    let className = cz({
      "checkbox-item": true,
      "checkbox-item-select": checked,
      [props.className]: !!props.className,
    });
    return (
      <div className={className}>
        <i className="checkbox-item-bg" onClick={this.change}>
          <Icon className="checkbox-icon" type={require("!svg-sprite-loader!./images/icon-correct.svg")} />
        </i>
        <div className="checkbox-label" onClick={!props.labelDisabled ? this.change : null}>
          {props.children}
        </div>
      </div>
    );
  }
}

CheckBox.propTypes = {
  checked: PropTypes.bool,
  onChange: PropTypes.func,
  labelDisabled: PropTypes.bool,
};

CheckBox.defaultProps = {
  checked: false,
  labelDisabled: false,
};

export default CheckBox;
