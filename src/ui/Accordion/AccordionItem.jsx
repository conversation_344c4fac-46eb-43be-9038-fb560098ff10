import React from "react";
import cz from "classnames";
import PropTypes from "prop-types";

class AccordionItem extends React.Component {
  componentDidMount() {
    this.updateHeight();
  }
  componentDidUpdate(prevProps) {
    if (this.props.isActive !== prevProps.isActive) {
      this.updateHeight();
    }
  }
  updateHeight = () => {
    const content = this.accordionContent;
    const contentInner = this.accordionContentInner;
    if (this.props.isActive) {
      content.style.height = `${contentInner.scrollHeight}px`;
    } else {
      content.style.height = `${contentInner.scrollHeight}px`;
      void content.offsetHeight;
      content.style.height = "0px";
    }
  };
  handleTransitionEnd = () => {
    if (this.props.isActive) {
      this.accordionContent.style.height = "auto";
    }
  };

  render() {
    return (
      <div className={cz("accordion-item", this.props.className, { "accordion-active": this.props.isActive })}>
        <div className="accordion-item-bg">
          <div className="accordion-header" onClick={this.props.onClick}>
            {this.props.children[0]}
          </div>
          <div
            ref={(e) => (this.accordionContent = e)}
            className={`accordion-content ${this.props.isActive ? "accordion-active" : ""}`}
            style={{ height: 0 }}
            onTransitionEnd={this.handleTransitionEnd}
          >
            <div ref={(e) => (this.accordionContentInner = e)} className="accordion-content-inner">
              {this.props.children[1]}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

AccordionItem.propTypes = {
  className: PropTypes.string,
};

AccordionItem.defaultProps = {
  className: "",
};

export default AccordionItem;
