.accordion-wrap {
  .accordion-content {
    height: 0;
    overflow: hidden;
    transition: height 0.3s ease-in-out;
  }
}

.common-accordion {
  .accordion-active {
    .arrow-down {
      transform: rotate(-180deg);
    }

    .common-accordion-header {
      color: #fff;
    }
  }

  .common-accordion-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 104px;
    font-size: 28px;
    line-height: normal;
    color: var(--text-color-secondary);
    text-transform: capitalize;

    .header-info {
      display: flex;
      gap: 30px;
      align-items: center;
    }

    .accordion-icon {
      display: block;
      width: 40px;
      height: 40px;
      object-fit: contain;
    }
  }

  .arrow-down {
    width: 30px;
    height: 30px;
    transition: all 0.2s;
  }
}
