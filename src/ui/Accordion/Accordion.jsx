import React from "react";
import cz from "classnames";
import PropTypes from "prop-types";

import AccordionItem from "./AccordionItem";

class Accordion extends React.Component {
  state = {
    activeKey: this.props.accordion
      ? Array.isArray(this.props.activeKey)
        ? this.props.activeKey.find(Boolean) || null
        : this.props.activeKey
      : Array.isArray(this.props.activeKey)
        ? [...new Set(this.props.activeKey)].filter(Boolean)
        : [this.props.activeKey].filter(Boolean),
  };
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.activeKey !== this.props.activeKey) {
      this.updateActiveKey(this.props.activeKey);
    }
  }
  updateActiveKey = (propKey) => {
    this.setState({
      activeKey: this.props.accordion
        ? Array.isArray(propKey)
          ? propKey.find(Boolean) || null
          : propKey
        : Array.isArray(propKey)
          ? [...new Set(propKey)].filter(Boolean)
          : [propKey].filter(Boolean),
    });
  };
  handlePanelClick = (propKey) => {
    let activeKey = this.state.activeKey;
    if (this.props.accordion) {
      activeKey = activeKey === propKey ? null : propKey;
    } else {
      activeKey = activeKey.slice();
      let index = activeKey.indexOf(propKey);
      if (index > -1) {
        activeKey.splice(index, 1);
      } else {
        activeKey.push(propKey);
      }
    }
    this.setState({ activeKey: activeKey });
    if (typeof this.props.onChange === "function") {
      this.props.onChange(activeKey);
    }
  };
  renderItem = () => {
    const self = this;
    const childrenWithProps = React.Children.map(this.props.children, function (child) {
      if (React.isValidElement(child) && child.type === AccordionItem) {
        const key = child.props["data-key"];
        return React.cloneElement(child, {
          isActive: self.props.accordion ? self.state.activeKey === key : self.state.activeKey?.indexOf(key) > -1,
          onClick: self.handlePanelClick.bind(self, key),
        });
      } else {
        return child;
      }
    });
    return childrenWithProps;
  };
  render() {
    return <div className={cz("accordion-wrap", this.props.className)}>{this.renderItem()}</div>;
  }
}

Accordion.propTypes = {
  className: PropTypes.string,
  accordion: PropTypes.bool,
  activeKey: PropTypes.oneOfType([PropTypes.string, PropTypes.arrayOf(PropTypes.string)]),
};

Accordion.defaultProps = {
  className: "",
  accordion: false,
  activeKey: [],
};

export default Accordion;
