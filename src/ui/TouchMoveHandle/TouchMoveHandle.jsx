import React from "react";
import cz from "classnames";
import PropTypes from "prop-types";

import TouchHandler from "@/utils/touchHandler";

class TouchMoveHandle extends React.Component {
  constructor(props) {
    super(props);
    this.touchEl = null;
    this.touch = new TouchHandler();
  }

  componentDidMount() {
    this.addTouchEvent();
  }

  componentWillUnmount() {
    this.removeTouchEvent();
  }

  addTouchEvent = () => {
    if (!this.touchEl) return;
    this.touchEl.addEventListener("touchstart", this.touch.start);
    this.touchEl.addEventListener("touchmove", this.touch.move);
    this.touchEl.addEventListener("touchend", this.handleTouchEnd);
  };

  removeTouchEvent = () => {
    this.touchEl.removeEventListener("touchstart", this.touch.start);
    this.touchEl.removeEventListener("touchmove", this.touch.move);
    this.touchEl.removeEventListener("touchend", this.handleTouchEnd);
  };

  handleTouchEnd = () => {
    if (this.touch.isTap) return;

    const { position, threshold } = this.props;
    let flag = false;

    switch (position) {
      case "left":
        flag = this.touch.deltaX < -threshold;
        break;
      case "right":
        flag = this.touch.deltaX > threshold;
        break;
      case "up":
        flag = this.touch.deltaY < -threshold;
        break;
      case "down":
        flag = this.touch.deltaY > threshold;
        break;
      default:
        break;
    }

    if (flag) {
      this.props.handleEvent?.();
    }
  };
  render() {
    return (
      <div className={cz("touch-move-wrap", this.props.className)} ref={(e) => (this.touchEl = e)}>
        {this.props.children}
      </div>
    );
  }
}

TouchMoveHandle.propTypes = {
  position: PropTypes.oneOf(["right", "left", "up", "down"]),
  handleEvent: PropTypes.func.isRequired,
  threshold: PropTypes.number,
};

TouchMoveHandle.defaultProps = {
  position: "left",
  threshold: 60,
};

export default TouchMoveHandle;
