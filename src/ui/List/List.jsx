import React from "react";
import cz from "classnames";
import { throttle } from "lodash";
import PropTypes from "prop-types";

import { getScrollParent } from "@/utils/scroll";

import LoadingIcon from "./LoadingIcon";

class List extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      failed: false,
    };
    this.placeholderRef = null;
    this.scrollParent = null;

    this.flag = {};
    this.nextFlag = this.flag;

    this.handleScroll = throttle(this.check, props.throttle, {
      leading: true,
      trailing: true,
    });
  }

  componentDidMount() {
    this.check();
    this.bindScroll();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.scroller !== this.props.scroller) {
      this.unbindScroll();
      this.bindScroll();
      this.check();
    }
  }

  componentWillUnmount() {
    this.unbindScroll();
    this.handleScroll.cancel?.();
  }

  getScrollContainer = () => {
    const { scroller } = this.props;
    if (typeof scroller === "string") {
      return document.querySelector(scroller);
    }
    if (scroller instanceof HTMLElement) {
      return scroller;
    }
    return this.placeholderRef ? getScrollParent(this.placeholderRef) : null;
  };

  bindScroll = () => {
    this.scrollParent = this.getScrollContainer();
    if (this.scrollParent) {
      this.scrollParent.addEventListener("scroll", this.handleScroll, { passive: true });
    }
  };

  unbindScroll = () => {
    if (this.scrollParent) {
      this.scrollParent.removeEventListener("scroll", this.handleScroll);
      this.scrollParent = null;
    }
  };

  loadMore = async (isRetry) => {
    try {
      await this.props.onLoad?.(isRetry);
    } catch (err) {
      if (this.props.pauseOnError) {
        this.setState({ failed: true });
      }
    }
  };

  check = async () => {
    if (this.nextFlag !== this.flag) return;

    const { threshold, finished, disabled, pauseOnError } = this.props;
    const { failed } = this.state;
    if (finished || disabled || (pauseOnError && failed)) return;

    const el = this.placeholderRef;
    if (!el || !el.offsetParent) return;

    const parent = this.scrollParent;
    if (!parent) return;

    const rect = el.getBoundingClientRect();
    const elementTop = rect.top;

    const bottomPosition = parent === window ? window.innerHeight : parent.getBoundingClientRect().bottom;

    const shouldLoad = bottomPosition >= elementTop - threshold;

    if (shouldLoad) {
      const newFlag = {};
      this.nextFlag = newFlag;
      await this.loadMore(false);
      this.flag = newFlag;
    }
  };

  retry = async () => {
    this.setState({ failed: false });
    await this.loadMore(true);
    this.flag = this.nextFlag;
  };

  renderFinished = () => {
    const { finishedText } = this.props;
    if (finishedText) {
      return <div className="list-finished">{finishedText}</div>;
    }
    return null;
  };

  renderError = () => {
    const { errorText } = this.props;
    if (errorText) {
      if (typeof errorText === "function") return errorText(this.retry);
      return (
        <div onClick={this.retry} className="list-error">
          {errorText}
        </div>
      );
    }
    return (
      <div onClick={this.retry} className="list-error">
        Load failed, Tap to retry.
      </div>
    );
  };

  renderPlaceholder = () => {
    const { finished, loadingText, loadingRender, disabled, pauseOnError } = this.props;
    const { failed } = this.state;

    if (disabled) return null;
    if (finished) return this.renderFinished();
    if (failed && pauseOnError) return this.renderError();
    if (loadingRender) return loadingRender();

    return (
      <div className="list-loading">
        <LoadingIcon />
        <span className="loading-text">{loadingText}</span>
      </div>
    );
  };

  render() {
    const { className, style, children } = this.props;
    return (
      <div className={cz("list", className)} style={style}>
        {children}
        <div
          className="list-trigger"
          ref={(el) => {
            this.placeholderRef = el;
          }}
        >
          {this.renderPlaceholder()}
        </div>
      </div>
    );
  }
}

List.propTypes = {
  className: PropTypes.string,
  finished: PropTypes.bool, //是否全部加载完毕
  onLoad: PropTypes.func.isRequired, //滚动距离底部小于threshold时触发, promise
  threshold: PropTypes.number, //滚动触底距离阈值
  throttle: PropTypes.number, //节流时间ms，onLoad不是promise，需要增加时间(600-800)
  finishedText: PropTypes.node, //加载完成提示
  loadingText: PropTypes.node, //加载中提示
  errorText: PropTypes.oneOfType([PropTypes.node, PropTypes.func]), //加载错误提示
  loadingRender: PropTypes.func, //自定义加载中图标和提示
  scroller: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(HTMLElement)]), //滚动容器，选择器或html元素
  disabled: PropTypes.bool, //禁用
  pauseOnError: PropTypes.bool, //失败是否暂停加载
};

List.defaultProps = {
  finished: false,
  threshold: 300,
  throttle: 200,
  disabled: false,
  pauseOnError: true,
};

export default List;
