:root {
  --list-loading-size: 50px;
  --list-loading-height: 80px;
  --list-loading-color: #697088;
  --list-font-color: #697088;
  --list-font-size: 26px;
  --list-text-margin: 20px;
}

.list-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--list-font-size);
  line-height: var(--list-loading-height);
  color: var(--list-font-color);

  .list-loading {
    display: flex;
    align-items: center;
  }

  .loading-text {
    margin-left: var(--list-text-margin);
  }
}

.loading-circle {
  display: inline-block;
  width: var(--list-loading-size);
  max-width: 100%;
  height: var(--list-loading-size);
  max-height: 100%;
  vertical-align: middle;
  color: var(--list-loading-color);
  animation: rotate-full 1.2s linear infinite;

  .loading-icon {
    display: block;
    width: 100%;
    height: 100%;

    circle {
      stroke: currentcolor;
      stroke-width: 3;
      stroke-linecap: round;
      animation: loading-circle 1.5s ease-in-out infinite;
    }
  }
}

@keyframes loading-circle {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40;
  }

  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120;
  }
}
