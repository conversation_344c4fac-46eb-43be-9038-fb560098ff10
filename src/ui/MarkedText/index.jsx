import React from "react";
import PropTypes from "prop-types";

class MarkedText extends React.Component {
  parseContent(content, tags) {
    const result = [];
    let remaining = content;
    let globalMatchIndex = 0;

    while (remaining.length > 0) {
      let earliest = null;
      let matchedTag = null;
      let match;

      for (const tag in tags) {
        const reg = new RegExp(`<${tag}>(.*?)</${tag}>`, "i");
        match = reg.exec(remaining);
        if (match && (!earliest || match.index < earliest.index)) {
          earliest = match;
          matchedTag = tag;
        }
      }

      if (!earliest) {
        result.push(remaining);
        break;
      }

      const matchStart = earliest.index;
      const matchText = earliest[1];
      const fullMatch = earliest[0];
      const currentIndex = globalMatchIndex++;

      if (matchStart > 0) {
        result.push(remaining.slice(0, matchStart));
      }

      const tagConfig = tags[matchedTag] || {};
      result.push(
        <span
          key={`${matchedTag}-${currentIndex}`}
          className={tagConfig.className}
          style={tagConfig.style}
          onClick={() => {
            if (typeof tagConfig.onClick === "function") {
              tagConfig.onClick({ tag: matchedTag, target: matchText, index: currentIndex });
            }
          }}
        >
          {matchText}
        </span>
      );

      remaining = remaining.slice(matchStart + fullMatch.length);
    }

    return result;
  }
  render() {
    const { content, tags, onTagClick, prefixIcon, wrapperTag: WrapperTag, className } = this.props;

    const children = this.parseContent(content, tags, onTagClick);

    if (prefixIcon) {
      children.unshift(prefixIcon);
    }

    return <WrapperTag className={className}>{children}</WrapperTag>;
  }
}

MarkedText.propTypes = {
  className: PropTypes.string,
  content: PropTypes.string.isRequired, // 翻译文本
  prefixIcon: PropTypes.element, // 可选图标
  tags: PropTypes.object.isRequired, // { tagName: { className?, style?, onClick? } }
  wrapperTag: PropTypes.oneOfType([PropTypes.string, PropTypes.func, PropTypes.object]),
};

MarkedText.defaultProps = {
  wrapperTag: "p",
};

export default MarkedText;
