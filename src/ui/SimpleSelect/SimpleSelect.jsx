import React from "react";
import { CSSTransition } from "react-transition-group";
import { Icon } from "antd-mobile";
import cz from "classnames";
import PropTypes from "prop-types";

import Overlay from "../Overlay/Overlay";

class SimpleSelect extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showOption: false,
    };
    this.selectRef = null;
  }
  componentDidMount() {
    this.props.setRef && this.props.setRef(this);
    if (!this.props.hasMask) {
      document.addEventListener("click", this.handleClickOutside);
    }
  }
  componentWillUnmount() {
    document.removeEventListener("click", this.handleClickOutside);
  }
  handleClickOutside = (e) => {
    if (this.selectRef && !this.selectRef.contains(e.target)) {
      this.setState({ showOption: false });
    }
  };
  toggleOptions = () => {
    if (!this.props.disabled) {
      this.setState((prevState) => ({
        showOption: !prevState.showOption,
      }));
    }
  };
  handleOptionClick = (optionValue) => {
    this.props.onChange(optionValue);
    this.setState({ showOption: false });
  };
  getAnimateName = () => {
    const animations = {
      down: "menu-fade-slide-down",
      up: "menu-fade-slide-up",
    };
    return animations[this.props.position];
  };
  render() {
    return (
      <div className={cz("simple-select", this.props.className)} ref={(c) => (this.selectRef = c)}>
        <div className={cz("select-wrap", { on: this.state.showOption })} onClick={this.toggleOptions}>
          {this.props.selected}
          {this.props.hasArrow && (
            <div className={cz("select-icon", { on: this.state.showOption })}>
              <Icon type={require("!svg-sprite-loader!@/assets/images/common/arrow-down.svg")} />
            </div>
          )}
        </div>
        <CSSTransition
          in={this.state.showOption}
          timeout={300}
          classNames={this.getAnimateName()}
          unmountOnExit={false}
        >
          <div className="option-wrap" style={{ display: this.state.showOption ? "block" : "none" }}>
            <div className="option-bg">
              {React.Children.map(this.props.children, (child, index) => {
                let optionClassName = "";
                if (this.props.value === child.props.value) {
                  optionClassName = "option-item selected";
                } else {
                  optionClassName = "option-item";
                }
                return (
                  <div onClick={() => this.handleOptionClick(child.props.value)} className={optionClassName}>
                    {child}
                  </div>
                );
              })}
            </div>
          </div>
        </CSSTransition>
        {this.props.overlay && (
          <Overlay show={this.state.showOption} onClick={this.toggleOptions} zIndex={this.props.overlayIndex} />
        )}
      </div>
    );
  }
}

SimpleSelect.propTypes = {
  disabled: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  className: PropTypes.string,
  onChange: PropTypes.func,
  selected: PropTypes.node,
  children: PropTypes.node,
  setRef: PropTypes.func,
  hasArrow: PropTypes.bool,
  position: PropTypes.oneOf(["down", "up"]),
  overlay: PropTypes.bool,
  overlayIndex: PropTypes.number,
};

SimpleSelect.defaultProps = {
  className: "",
  onChange: () => {},
  hasArrow: false,
  position: "down",
  overlay: false,
};

export default SimpleSelect;
