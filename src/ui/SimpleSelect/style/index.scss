.simple-select {
  position: relative;

  .select-wrap {
    display: flex;
    gap: 16px;
    align-items: center;
    justify-content: space-between;
  }

  .select-icon {
    display: block;
    flex-shrink: 0;
    width: 30px;
    height: 30px;
    transform: rotate(0deg);
    transform-origin: center;
    transition: 0.2s;

    svg {
      display: block;
      width: 100%;
      height: 100%;
    }

    &.on {
      transform: rotate(-180deg);
    }
  }

  .option-wrap {
    position: absolute;
    z-index: 20;
    display: none;

    &.on {
      display: block;
    }

    .option-item {
      display: flex;
      align-items: center;
      width: 100%;
      white-space: nowrap;

      &.selected {
      }
    }
  }
}
