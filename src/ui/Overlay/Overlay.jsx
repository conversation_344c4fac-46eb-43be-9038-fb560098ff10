import React from "react";
import { CSSTransition } from "react-transition-group";
import cz from "classnames";
import PropTypes from "prop-types";

import { getCssVarNumber } from "@/utils/dom";

class Overlay extends React.Component {
  constructor(props) {
    super(props);
    this.nodeRef = null;
  }
  componentDidMount() {
    this.nodeRef?.addEventListener("touchmove", this.onTouchMove, { passive: false });
  }
  componentWillUnmount() {
    this.nodeRef?.removeEventListener("touchmove", this.onTouchMove);
  }
  onTouchMove = (event) => {
    if (!this.props.lockScroll) return;
    if (event.cancelable) {
      event.preventDefault();
    }
  };
  renderOverlay = () => {
    const { zIndex, lockScroll } = this.props;
    const style = {
      zIndex: zIndex !== undefined ? +zIndex : undefined,
      touchAction: lockScroll && "none",
      ...this.props.stye,
    };
    return (
      <div
        ref={(e) => (this.nodeRef = e)}
        className={cz("common-overlay", this.props.className)}
        style={style}
        onClick={this.props.onClick}
      >
        {this.props.children}
      </div>
    );
  };
  render() {
    return (
      <CSSTransition
        in={this.props.show}
        timeout={getCssVarNumber("--popup-duration")}
        classNames={"popup-fade"}
        unmountOnExit={true}
        mountOnEnter={true}
      >
        {this.renderOverlay()}
      </CSSTransition>
    );
  }
}

Overlay.propTypes = {
  show: PropTypes.bool,
  className: PropTypes.string,
  onClick: PropTypes.func.isRequired,
  zIndex: PropTypes.number,
  lockScroll: PropTypes.bool,
};

Overlay.defaultProps = {
  show: false,
  className: "",
  onClick: () => {},
  lockScroll: true,
};

export default Overlay;
