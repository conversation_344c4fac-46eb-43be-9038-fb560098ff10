import React from "react";
import cz from "classnames";

class Switch extends React.Component {
  state = { isChecked: false };
  componentDidMount() {
    this.setState({
      isChecked: this.props.checked,
    });
  }
  componentDidUpdate(prevProps) {
    if (prevProps.checked !== this.props.checked) {
      this.setState({
        isChecked: this.props.checked,
      });
    }
  }
  onInputChange = (e) => {
    const value = e.target.checked;
    this.setState({
      isChecked: value,
    });
    if (this.props.onChange) {
      this.props.onChange(value);
    }
  };
  render() {
    return (
      <label className="switch">
        <input
          type="checkbox"
          checked={this.state.isChecked}
          onChange={this.onInputChange}
          className="switch-checkbox"
        />
        <div className={cz("switch-box", { "switch-box-checked": this.state.isChecked })} />
      </label>
    );
  }
}

Switch.defaultProps = {
  checked: false,
};

export default Switch;
