.switch {
  position: relative;
  z-index: 1;
  display: block;
  width: 150px;
  height: 66px;
  margin: 0 20px;

  .switch-checkbox {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
    appearance: none;
    border: 0;
    opacity: 0;
  }

  .switch-box {
    position: relative;
    z-index: 0;
    width: 150px;
    height: 66px;
    padding: 0;
    margin: 0;
    appearance: none;
    border: 0;
    border-radius: 40px;
    transition: all 0.3s;
  }

  .switch-box::after {
    position: absolute;
    top: 9px;
    left: 9px;
    width: 48px;
    height: 48px;
    content: "";
    background-color: #fff;
    border-radius: 50%;
    transition: all 0.3s;
  }

  .switch-box-checked {
    // box-shadow: 0 5px 20px 0 rgba(0, 0, 0, 0.49);
    // background-color: rgba(20, 20, 38, 0.7);
  }

  .switch-box-checked::after {
    transform: translateX(84px);
    // box-shadow: 0 2px 5px 0 rgba(122, 133, 168, 0.51);
    // background: linear-gradient(to bottom, #c3c3fc, #9ca2ce 6%, #484874);
  }
}
