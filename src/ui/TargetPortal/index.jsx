import { createPortal } from "react-polyfill-portal";
import PropTypes from "prop-types";

function getTargetElement(target) {
  if (!target) return document.body;
  if (typeof target === "string") return document.querySelector(target);
  if (target instanceof HTMLElement) return target;
  return null;
}
function TargetPortal(props) {
  const element = getTargetElement(props.target);
  return element ? createPortal(props.children, element) : null;
}

TargetPortal.propTypes = {
  target: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(HTMLElement)]),
  children: PropTypes.node.isRequired,
};

export default TargetPortal;
