import React from "react";
import PropTypes from "prop-types";

import { getScrollParent } from "@/utils/scroll";
import TouchHandler from "@/utils/touchHandler";

let totalLockCount = 0;
const BODY_LOCK_CLASS = "overflow-hidden";

class LockScroll extends React.Component {
  constructor(props) {
    super(props);
    this.touch = new TouchHandler();
  }

  componentDidMount() {
    if (this.props.shouldLock) {
      this.lock();
    }
  }

  componentDidUpdate(prevProps) {
    if (this.props.shouldLock !== prevProps.shouldLock) {
      if (this.props.shouldLock) {
        this.lock();
      } else {
        this.unlock();
      }
    }
  }

  componentWillUnmount() {
    this.unlock();
  }

  lock = () => {
    document.addEventListener("touchstart", this.touch.start);
    document.addEventListener("touchmove", this.onTouchMove, { passive: false });

    if (!totalLockCount) {
      document.body.classList.add(BODY_LOCK_CLASS);
    }
    totalLockCount++;
  };

  unlock = () => {
    if (totalLockCount) {
      document.removeEventListener("touchstart", this.touch.start);
      document.removeEventListener("touchmove", this.onTouchMove);

      totalLockCount--;
      if (!totalLockCount) {
        document.body.classList.remove(BODY_LOCK_CLASS);
      }
    }
  };

  onTouchMove = (event) => {
    this.touch.move(event);
    const direction = this.touch.deltaY > 0 ? "10" : "01";
    const el = getScrollParent(event.target);
    if (!el) return;

    const { scrollHeight, offsetHeight, scrollTop } = el;

    let status = "11";
    if (scrollTop === 0) {
      status = offsetHeight >= scrollHeight ? "00" : "01";
    } else if (scrollTop + offsetHeight >= scrollHeight) {
      status = "10";
    }

    if (status !== "11" && this.touch.isVertical() && !(parseInt(status, 2) & parseInt(direction, 2))) {
      if (event.cancelable) {
        event.preventDefault();
      }
    }
  };

  render() {
    return null;
  }
}

LockScroll.propTypes = {
  shouldLock: PropTypes.bool,
};

LockScroll.defaultProps = {
  shouldLock: false,
};

export default LockScroll;
