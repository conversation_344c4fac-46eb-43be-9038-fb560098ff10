import React from "react";
import cz from "classnames";
import PropTypes from "prop-types";

import { getScrollTop, scrollTopTo } from "@/utils/scroll";

import "./BackTop.scss";

class BackTop extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
    this.scrollParent = window;
  }

  componentDidMount() {
    this.scrollParent = this.getTarget();
    this.bindScroll();
    this.onScroll();
  }

  componentWillUnmount() {
    this.unbindScroll();
  }

  getTarget = () => {
    const { target } = this.props;
    if (typeof target === "string") {
      return document.querySelector(target) || window;
    }
    return target || window;
  };

  bindScroll = () => {
    if (this.scrollParent) {
      this.scrollParent.addEventListener("scroll", this.onScroll, { passive: true });
    }
  };

  unbindScroll = () => {
    if (this.scrollParent) {
      this.scrollParent.removeEventListener("scroll", this.onScroll);
    }
  };

  onScroll = () => {
    const { offset } = this.props;
    const scrollTop = getScrollTop(this.scrollParent);

    this.setState({ visible: scrollTop >= offset });
  };

  scrollToTop = () => {
    const { immediate, duration } = this.props;
    scrollTopTo(this.scrollParent, 0, immediate ? 0 : duration);
  };

  render() {
    return (
      <div
        className={cz("back-top", this.props.className)}
        style={{
          display: this.state.visible ? undefined : "none",
          ...this.props.style,
        }}
        onClick={this.scrollToTop}
      >
        {this.props.children || "Back top"}
      </div>
    );
  }
}

BackTop.propTypes = {
  className: PropTypes.string,
  target: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(HTMLElement)]), // 滚动容器,传入选择器或 DOM 元素
  offset: PropTypes.number, // 滚动偏移量，超过此值时显示按钮
  immediate: PropTypes.bool, // 是否立即滚动到顶部
  duration: PropTypes.string, // 动画时间ms
};

BackTop.defaultProps = {
  className: "",
  offset: 200,
  immediate: false,
  duration: 300,
};

export default BackTop;
