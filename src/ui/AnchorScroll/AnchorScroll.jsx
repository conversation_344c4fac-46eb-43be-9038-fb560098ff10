import React, { Component } from "react";
import { debounce } from "lodash";
import PropTypes from "prop-types";

import { getScrollParent, getScrollTop, scrollTopTo } from "@/utils/scroll";

class AnchorScroll extends Component {
  constructor(props) {
    super(props);

    this.state = {
      activeIndex: props.initActiveIndex,
    };

    this.isClick = false;
    this.scrollBox = null;
    this.timer = null;
  }

  componentDidMount() {
    const { setRef, initActiveIndex } = this.props;
    this.initScrollBox();

    if (!this.scrollBox) return;

    this.scrollBox.addEventListener("scroll", this.onScroll);

    if (initActiveIndex !== null) {
      this.onChangeTabs(initActiveIndex);
    }

    setRef?.(this);
  }

  componentWillUnmount() {
    if (this.scrollBox) {
      this.scrollBox.removeEventListener("scroll", this.onScroll);
    }
    window.clearTimeout(this.timer);
    this.scrollFun?.cancel();
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
    }
  }

  initScrollBox = () => {
    const { scroller } = this.props;

    if (typeof scroller === "string") {
      this.scrollBox = document.querySelector(scroller);
    } else if (scroller instanceof HTMLElement) {
      this.scrollBox = scroller;
    }

    // fallback
    if (!this.scrollBox) {
      const firstItem = document.querySelector(`[id^="anchor-scroll-item-"]`);
      this.scrollBox = getScrollParent(firstItem);
    }

    if (!this.scrollBox || this.scrollBox === document) {
      this.scrollBox = window;
    }
  };

  getScrollTop = () => {
    return getScrollTop(this.scrollBox);
  };

  onChangeTabs = (index) => {
    const { onChange, offsetTop } = this.props;

    const anchorElement = document.getElementById(`anchor-scroll-item-${index}`);
    if (!anchorElement) return;

    this.setState({ activeIndex: index });
    onChange?.(index);

    this.isClick = true;

    const scrollOffset = anchorElement.getBoundingClientRect().top + this.getScrollTop() - offsetTop;
    scrollTopTo(this.scrollBox, scrollOffset, 300);
  };

  getScrollItemInfo = () => {
    return React.Children.toArray(this.props.children).reduce((acc, child, index) => {
      if (child?.type?.defaultProps?.name === "AnchorScrollItem") {
        const element = document.getElementById(`anchor-scroll-item-${index}`);
        if (element) {
          acc.push({
            key: index,
            top: element.getBoundingClientRect().top + this.getScrollTop(),
          });
        }
      }
      return acc;
    }, []);
  };

  scrollFun = debounce(() => {
    const scrollItemInfo = this.getScrollItemInfo();
    const visibleIndexes = [];
    const scrollTop = this.getScrollTop();
    const offsetTop = this.props.offsetTop || 0;

    scrollItemInfo.forEach((item) => {
      if (scrollTop >= item.top - offsetTop) {
        visibleIndexes.unshift(item.key);
      }
    });

    if (visibleIndexes.length > 0) {
      const newIndex = visibleIndexes[0];
      if (newIndex !== this.state.activeIndex) {
        this.setState({ activeIndex: newIndex });
        this.props.onChange?.(newIndex);
      }
    } else if (scrollTop <= this.props.topThreshold) {
      this.setState({ activeIndex: 0 });
      this.props.onChange?.(0);
    }

    this.isClick = false;
  }, 30);

  onScroll = () => {
    if (this.rafId) return;

    window.clearTimeout(this.timer);
    this.timer = window.setTimeout(() => {
      this.isClick = false;
    }, 66);

    if (this.isClick) return;

    this.rafId = window.requestAnimationFrame(() => {
      if (!this.isClick) {
        this.scrollFun();
      }
      this.rafId = null;
    });
  };

  render() {
    const { children, className = "" } = this.props;

    return (
      <div className={`anchor-scroll-wrap ${className}`}>
        <div className="anchor-scroll-content">
          {React.Children.map(children, (child, index) => {
            if (child?.type?.defaultProps?.name === "AnchorScrollItem") {
              return React.cloneElement(child, {
                id: `anchor-scroll-item-${index}`,
                key: `anchor-item-${index}`,
                index,
              });
            }
            return null;
          })}
        </div>
      </div>
    );
  }
}

AnchorScroll.propTypes = {
  offsetTop: PropTypes.number,
  scroller: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(HTMLElement)]),
  onChange: PropTypes.func,
  setRef: PropTypes.func,
  initActiveIndex: PropTypes.number,
  topThreshold: PropTypes.number,
  className: PropTypes.string,
  children: PropTypes.node,
};

AnchorScroll.defaultProps = {
  offsetTop: 0,
  topThreshold: 150,
};

export default AnchorScroll;
