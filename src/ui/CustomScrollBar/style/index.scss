.custom-scrollbar-wrap {
  position: relative;
  height: 100%;
  overflow: hidden;

  .custom-scrollbar-scroll {
    height: 100%;
    overflow: scroll;
  }

  .custom-scrollbar-content {
    min-width: 100%;
    min-height: 100%;
  }

  .custom-scrollbar-bar {
    position: absolute;
    z-index: 2;
    border-radius: 3px;
    transition: opacity 0.3s ease-out;

    &.custom-scrollbar-vertical {
      top: 0;
      right: 5px;
      width: 6px;
      height: 100%;

      &.custom-scrollbar-thumb {
        width: 100%;
      }
    }

    &.custom-scrollbar-horizontal {
      bottom: 5px;
      left: 0;
      width: 100%;
      height: 6px;

      &.custom-scrollbar-thumb {
        height: 100%;
      }
    }

    .custom-scrollbar-thumb {
      background-color: var(--custom-scrollbar-bg-color);
      border-radius: inherit;
      transition: background-color 0.3s;
    }
  }
}
