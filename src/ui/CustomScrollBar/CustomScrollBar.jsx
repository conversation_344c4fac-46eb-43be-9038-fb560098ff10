import React from "react";
import PropTypes from "prop-types";

class CustomScrollBar extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      verticalThumbHeight: 0,
      verticalThumbPosition: 0,
      horizontalThumbWidth: 0,
      horizontalThumbPosition: 0,
      isScrolling: true,
      showVerticalBar: false,
      showHorizontalBar: false,
    };

    this.scrollWrapRef = null;
    this.verticalBarRef = null;
    this.horizontalBarRef = null;
    this.scrollTimer = null;
  }

  componentDidMount() {
    this.updateScrollBar();

    this.scrollWrapRef.addEventListener("scroll", this.handleScroll);
    window.addEventListener("resize", this.updateScrollBar);

    this.resizeObserver = new ResizeObserver(this.updateScrollBar);
    if (this.scrollContentRef) {
      this.resizeObserver.observe(this.scrollContentRef);
    }
  }

  componentWillUnmount() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    if (this.scrollWrapRef) {
      this.scrollWrapRef.removeEventListener("scroll", this.handleScroll);
    }
    window.removeEventListener("resize", this.updateScrollBar);
  }

  updateScrollBar = () => {
    const scrollWrap = this.scrollWrapRef;

    if (scrollWrap) {
      const { clientHeight, scrollHeight, clientWidth, scrollWidth } = scrollWrap;

      const verticalThumbHeight =
        this.props.vertical && scrollHeight > clientHeight
          ? Math.max((clientHeight / scrollHeight) * clientHeight, 20)
          : 0;

      const horizontalThumbWidth =
        this.props.horizontal && scrollWidth > clientWidth
          ? Math.max((clientWidth / scrollWidth) * clientWidth, 20)
          : 0;

      this.setState({
        verticalThumbHeight,
        horizontalThumbWidth,
        showVerticalBar: this.props.vertical && scrollHeight > clientHeight,
        showHorizontalBar: this.props.horizontal && scrollWidth > clientWidth,
      });
    }
  };

  handleScroll = () => {
    const scrollWrap = this.scrollWrapRef;

    if (scrollWrap) {
      const { clientHeight, scrollHeight, scrollTop, clientWidth, scrollWidth, scrollLeft } = scrollWrap;

      const verticalThumbPosition =
        this.props.vertical && scrollHeight > clientHeight
          ? (scrollTop / (scrollHeight - clientHeight)) * (clientHeight - this.state.verticalThumbHeight)
          : 0;

      const horizontalThumbPosition =
        this.props.horizontal && scrollWidth > clientWidth
          ? (scrollLeft / (scrollWidth - clientWidth)) * (clientWidth - this.state.horizontalThumbWidth)
          : 0;

      this.setState({
        verticalThumbPosition,
        horizontalThumbPosition,
        isScrolling: true,
      });

      if (!this.props.alwaysVisible) {
        if (this.scrollTimer) clearTimeout(this.scrollTimer);
        this.scrollTimer = setTimeout(() => this.setState({ isScrolling: false }), 800);
      }

      if (this.props.onScroll) {
        this.props.onScroll(scrollTop, scrollLeft);
      }
    }
  };

  render() {
    const {
      verticalThumbHeight,
      verticalThumbPosition,
      horizontalThumbWidth,
      horizontalThumbPosition,
      isScrolling,
      showVerticalBar,
      showHorizontalBar,
    } = this.state;

    const { wrapClassName, scrollClassName, style, scrollBarStyle, alwaysVisible, children } = this.props;

    return (
      <div className={`custom-scrollbar-wrap ${wrapClassName}`} style={{ ...style }}>
        <div
          className={`custom-scrollbar-scroll ${scrollClassName} hide-scrollbar`}
          ref={(e) => (this.scrollWrapRef = e)}
          onScroll={this.handleScroll}
        >
          <div className="custom-scrollbar-content" ref={(e) => (this.scrollContentRef = e)}>
            {children}
          </div>
        </div>

        {showVerticalBar && (
          <div
            ref={(e) => (this.verticalBarRef = e)}
            className="custom-scrollbar-bar custom-scrollbar-vertical"
            style={{
              opacity: alwaysVisible || isScrolling ? 1 : 0,
              ...scrollBarStyle,
            }}
          >
            <div
              className="custom-scrollbar-thumb"
              style={{
                height: `${verticalThumbHeight}px`,
                transform: `translateY(${verticalThumbPosition}px)`,
              }}
            />
          </div>
        )}

        {showHorizontalBar && (
          <div
            ref={(e) => (this.horizontalBarRef = e)}
            className="custom-scrollbar-bar custom-scrollbar-horizontal"
            style={{
              opacity: alwaysVisible || isScrolling ? 1 : 0,
              ...scrollBarStyle,
            }}
          >
            <div
              className="custom-scrollbar-thumb"
              style={{
                width: `${horizontalThumbWidth}px`,
                transform: `translateX(${horizontalThumbPosition}px)`,
              }}
            />
          </div>
        )}
      </div>
    );
  }
}

CustomScrollBar.propTypes = {
  wrapClassName: PropTypes.string,
  scrollClassName: PropTypes.string,
  vertical: PropTypes.bool,
  horizontal: PropTypes.bool,
  style: PropTypes.object,
  scrollBarStyle: PropTypes.object,
  onScroll: PropTypes.func,
  alwaysVisible: PropTypes.bool,
};

CustomScrollBar.defaultProps = {
  wrapClassName: "",
  scrollClassName: "",
  vertical: true,
  horizontal: true,
  style: {},
  scrollBarStyle: {},
  onScroll: null,
  alwaysVisible: true,
};

export default CustomScrollBar;
