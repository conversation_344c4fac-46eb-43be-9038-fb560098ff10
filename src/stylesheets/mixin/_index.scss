@mixin iphonex {
  // iPhone 12 and iPhone 12 Pro
  @media only screen and (device-width >= 390px) and (device-width <= 844px) and (-webkit-device-pixel-ratio: 3) {
    @content;
  }
  // iPhone 12 Pro Max,
  @media only screen and (device-width >= 428px) and (device-width <= 926px) and (-webkit-device-pixel-ratio: 3) {
    @content;
  }
  // Apple iPhone X, XS, 11 Pro
  @media only screen and (device-width >= 375px) and (device-width <= 812px) and (-webkit-device-pixel-ratio: 3) {
    @content;
  }
  // Apple iPhone 11 Pro Max, XS Max
  @media only screen and (device-width >= 414px) and (device-width <= 896px) and (-webkit-device-pixel-ratio: 3) {
    @content;
  }
  // Apple iPhone 11, XR
  @media only screen and (device-width >= 414px) and (device-width <= 896px) and (-webkit-device-pixel-ratio: 2) {
    @content;
  }
}

@mixin iphonex-app {
  #root.app {
    // iPhone 12 and iPhone 12 Pro
    @media only screen and (device-width >= 390px) and (device-width <= 844px) and (-webkit-device-pixel-ratio: 3) {
      --safe-area-inset-bottom: 37px !important;
      @content;
    }
    // iPhone 12 Pro Max,
    @media only screen and (device-width >= 428px) and (device-width <= 926px) and (-webkit-device-pixel-ratio: 3) {
      --safe-area-inset-bottom: 37px !important;
      @content;
    }
    // Apple iPhone X, XS, 11 Pro
    @media only screen and (device-width >= 375px) and (device-width <= 812px) and (-webkit-device-pixel-ratio: 3) {
      --safe-area-inset-bottom: 37px !important;
      @content;
    }
    // Apple iPhone 11 Pro Max, XS Max
    @media only screen and (device-width >= 414px) and (device-width <= 896px) and (-webkit-device-pixel-ratio: 3) {
      --safe-area-inset-bottom: 37px !important;
      @content;
    }
    // Apple iPhone 11, XR
    @media only screen and (device-width >= 414px) and (device-width <= 896px) and (-webkit-device-pixel-ratio: 2) {
      --safe-area-inset-bottom: 37px !important;
      @content;
    }
  }
}

@mixin optional-at-root($sel) {
  @at-root #{if(not &, $sel, selector-append(&, $sel))} {
    @content;
  }
}

@mixin placeholder {
  @include optional-at-root("::-webkit-input-placeholder") {
    @content;
  }

  @include optional-at-root(":-moz-placeholder") {
    @content;
  }

  @include optional-at-root("::-moz-placeholder") {
    @content;
  }

  @include optional-at-root(":-ms-input-placeholder") {
    @content;
  }
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin trim($numLines: null) {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  @if $numLines != null {
    @supports (-webkit-line-clamp: 2) {
      display: -webkit-box;
      -webkit-line-clamp: $numLines;
      -webkit-box-orient: vertical;
      white-space: normal;
    }
  }
}

@mixin v-scroll() {
  &::-webkit-scrollbar {
    width: 4px;
    border-radius: 20px;
  }

  &::-webkit-scrollbar-thumb {
    min-height: 50px;
    background: $main-color;
    border-radius: 20px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 20px;
  }
}

@mixin jackpot-font() {
  font-size: 48px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  color: #47586e;
  text-align: center;
}

@mixin game-list() {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px 12px;
}

@mixin text-gradient() {
  background: linear-gradient(90deg, #fee93c 0%, #faa83c 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@mixin border-text-gradient() {
  @include text-gradient;
  position: relative;

  &::after {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    content: "";
    background: linear-gradient(180deg, #fff5c6 0%, #d1ae4c 100%);
    transform: scaleY(0.5);
  }
}

@function ensure-gradient($color) {
  @if str-index($color, "linear-gradient") {
    @return $color;
  } @else {
    @return linear-gradient(0deg, #{$color}, #{$color});
  }
}

@mixin gradient-border($gradient, $inner-color, $border-width: 2px, $radius: 8px) {
  background: ensure-gradient($inner-color), $gradient;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  border: $border-width solid transparent;
  border-radius: $radius;
  clip-path: inset(0 round #{$radius});
}

@mixin triangle($direction, $size, $color) {
  width: 0;
  height: 0;
  border-style: solid;

  @if $direction == up {
    border-color: transparent transparent $color;
    border-width: 0 $size $size;
  } @else if $direction == down {
    border-color: $color transparent transparent transparent;
    border-width: $size $size 0 $size;
  } @else if $direction == left {
    border-color: transparent $color transparent transparent;
    border-width: $size $size $size 0;
  } @else if $direction == right {
    border-color: transparent transparent transparent $color;
    border-width: $size 0 $size $size;
  } @else {
    @error "Unknown direction `#{$direction}` — use up/down/left/right.";
  }
}

@mixin btn-login {
  width: 152px;
  height: 63px;
  font-size: 24px;
  font-weight: 600;
  line-height: 63px;
  color: #fff;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.48px;
  text-shadow: 0 2px 0 #0556c6;
  background: url(~@/assets/images/home/<USER>/100% 100%;
}

@mixin btn-register {
  width: 207px;
  height: 65px;
  margin-left: -8px;
  font-size: 24px;
  font-weight: 600;
  line-height: 63px;
  color: var(--text-color-accent);
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.48px;
  text-shadow: 0 2px 0 #322727;
  background: url(~@/assets/images/home/<USER>/100% 100%;
}
