.form-page-container {
  .form-logo {
    display: block;
    height: 84px;
  }

  .form-title {
    position: relative;
    margin-bottom: 120px;
    text-align: center;

    h6 {
      font-size: 32px;
      font-weight: 600;
      line-height: normal;
      color: var(--text-color-primary);
    }

    p {
      margin-top: 10px;
      font-size: 26px;
      line-height: normal;
      color: #fff;
    }
  }

  .already-account {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    margin-top: 48px;
    font-size: 24px;
    font-weight: 500;
    line-height: normal;
    color: var(--text-color-primary);
    white-space: pre-wrap;

    .login-now {
      margin-left: 12px;
      color: var(--text-color-highlight);
      text-decoration: underline;
    }
  }

  .register-terms {
    margin-top: 34px;
    font-size: 24px;
    font-weight: 400;
    line-height: 131%;
    color: #000;
    text-align: left;

    .checkbox-item {
      align-items: flex-start;
    }

    .term-link {
      margin-left: 10px;
      color: var(--text-color-third);
    }

    .sm-checkbox-item {
      align-items: flex-start;
    }
  }

  .form-banner {
    flex-shrink: 0;
    width: 100%;
    margin-bottom: 23px;

    .swiper {
      padding: 0;
    }

    img {
      display: block;
      width: 100%;
    }
  }

  .form-actions {
    min-height: 260px;
  }
}

.form-wrap {
  position: relative;
  z-index: 5;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  min-height: calc(100 * var(--vh, 1vh));
  padding: 30px 44px 50px;

  .free-trial {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    margin-top: 20px;

    .trial-title {
      @include flex-center;
      position: relative;
      gap: 15px;

      &::before,
      &::after {
        width: 80px;
        height: 2px;
        content: "";
        background: #e4eaff;
      }
    }

    .icon-trial {
      flex-shrink: 0;
      width: 78px;
      height: 78px;
      object-fit: contain;
    }
  }

  .form-group:not(:last-child) {
    margin-bottom: 56px;

    &.form_error {
      margin-bottom: 26px;
    }
  }

  .panel-default {
    position: relative;
    display: none;
    width: 100%;
    padding: 0 10px;
    margin-top: 5px;
    text-align: left;

    &.on {
      display: block;
    }

    p {
      position: relative;
      font-size: 20px;
      font-weight: 500;
      line-height: normal;
      color: var(--text-color-error);
    }
  }

  .method-select {
    display: flex;
    flex-shrink: 0;
    justify-content: center;
    width: 100%;
    margin-bottom: 34px;

    .select-wrap {
      @include flex-center;
      gap: 30px;
      width: 100%;

      .method-toggle {
        @include flex-center;
        position: relative;
        width: 200px;
        padding: 0 0 10px;
        font-size: 24px;
        font-weight: 500;
        line-height: normal;
        color: var(--text-color-primary);
        text-align: center;
      }

      .method-toggle.on {
        color: var(--text-color-accent);
        border-bottom: 3px solid var(--border-color-accent);
      }

      .method-icon {
        width: 40px;
        height: 40px;
      }
    }
  }

  .recover-password {
    margin-top: 34px;
    margin-bottom: 0;
  }

  .btn-group {
    width: 100%;
    margin-top: 34px;
  }

  .submit-btn {
    @include flex-center;
    width: 100%;
    height: 72px;
    font-size: 32px;
    font-weight: 500;
    line-height: normal;
    color: var(--text-color-primary);
    text-align: center;
    background: var(--bg-color-button-primary);
    border: none;
    border-radius: 4px;
  }

  .submit-btn:disabled {
    opacity: 0.5;
  }

  .outter-form {
    .form-group,
    .fixed-list {
      position: relative;
      z-index: 1;
    }

    .get-code {
      @include flex-center;
      position: absolute;
      top: 50%;
      right: 16px;
      font-size: 24px;
      font-weight: 500;
      line-height: normal;
      color: var(--text-color-primary);
      text-align: center;
      border: none;
      transform: translateY(-50%);
    }

    .form-label {
      position: relative;
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 24px;
      line-height: normal;
      color: #fff;
    }

    .input-icon {
      @include flex-center;
      position: absolute;
      top: 50%;
      left: 16px;
      width: 32px;
      height: 32px;
      color: #fff;
      transform: translateY(-50%);

      .am-icon,
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    input {
      width: 100%;
      height: 64px;
      padding: 0 16px 0 64px;
      font-size: 24px;
      font-weight: 500;
      line-height: normal;
      color: var(--text-color-primary);
      background: var(--bg-color-input);
      border: 1px solid var(--border-color-input);
      border-radius: 12px;
    }

    input:focus {
      border: 1px solid var(--border-color-input-focus);
      // background: rgba(156, 203, 255, 0.2);
      // & ~ .input-placeholder {
      //   font-size: 20px;
      //   line-height: 24px;
      //   transform: translateY(-18px);
      // }
      // & ~ .right-icon {
      //   color: #e06700;
      // }
    }

    input::placeholder {
      color: var(--text-color-primary);
    }

    .input-placeholder {
      position: absolute;
      top: 42px;
      left: 32px;
      font-size: 24px;
      font-weight: 400;
      line-height: 29px;
      color: rgb(0 0 0 / 50%);
      transform-origin: 0 0;
      transition: 150ms ease-in-out;
      will-change: transform;
    }

    .form-group.form_error {
      input {
        border: 1px solid var(--border-color-input-error);
      }
      // .input-icon {
      //   .am-icon {
      //     fill: url(#icon_gradient);
      //   }
      // }
      .panel-default {
        display: block;
      }
    }

    .right-icon,
    .clear-field {
      position: absolute;
      top: 50%;
      right: 16px;
      margin-left: 20px;
      color: var(--text-color-primary);
      transform: translateY(-50%);

      .am-icon {
        display: block;
        width: 32px;
        height: 32px;
      }
    }

    .captcha-pic {
      position: absolute;
      top: 50%;
      right: 16px;
      width: auto;
      height: 60px;
      transform: translateY(-50%);
    }

    .refresh-captcha {
      position: absolute;
      top: 50%;
      right: 30px;
      width: 30px;
      height: 30px;
      margin-top: -15px;

      &.loading {
        animation: rotate-full 0.3s linear infinite;
      }
    }
  }

  .label-phone-code {
    position: absolute;
    top: 62px;
    left: 160px;
    font-size: 24px;
    font-weight: 400;
    line-height: 29px;
    color: #000;
  }

  .remember-password {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 12px;
    font-size: 21.923px;
    line-height: normal;
    color: #71b394;

    .no-code {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }
  }

  .btn-service {
    font-size: 21.923px;
    line-height: 26px;
    color: #f9fd4e;
    text-align: center;
  }

  .form-checkbox-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 32px;
    font-size: 24px;
    font-weight: 500;
    line-height: normal;
    color: var(--text-color-primary);

    .forget-password {
      color: var(--text-color-highlight);
      text-decoration: underline;
    }
  }

  .input-required {
    position: absolute;
    top: 50%;
    left: 62px;
    z-index: 10;
    width: 11px;
    height: 26px;
    font-size: 24px;
    line-height: normal;
    color: #ea4e3d;
    transform: translateY(-50%);
  }
}

.country-code-input {
  position: absolute;
  top: 0;
  left: 0;
  justify-content: flex-start;
  width: 183px;
  height: 100%;
  padding: 0 16px;
  font-size: 24px;
  color: var(--text-color-primary);
  background: var(--bg-color-secondary);
  border: 1px solid var(--border-color-surface);
  border-radius: 12px;

  img {
    order: 1;
    width: 32px;
    height: 32px;
  }

  .country-code {
    order: 2;
    min-width: 50px;
    margin: 0 15px;
    font-size: 24px !important;
    line-height: normal;
    text-align: center;
  }

  i {
    top: 5px;
    order: 3;
    border-top: 8px solid #fff;
  }

  & ~ input.form-mobileNum {
    width: calc(100% - 203px);
    height: 68px;
    padding: 0 30px;
    margin-left: auto;

    & + .input-icon {
      left: 190px;
      display: none;
    }

    & ~ .input-placeholder {
      left: 160px;
      font-size: 20px;
      line-height: 24px;
      transform: translateY(-18px);
    }

    & ~ .label-phone-code {
      display: block;
    }
  }
}

// countryCode popup
// .s-country-list-container {
//   .am-list-header {
//     color: #fff;
//   }
//   .header-wrap {
//     .close-btn {
//       color: #fff;
//     }
//   }
//   .popup-list {
//     background: rgba(26, 27, 39, 0.75);
//     box-shadow: 0px 22px 30px -14px rgba(0, 0, 0, 0.2), 0px 48px 76px 6px rgba(0, 0, 0, 0.14),
//       0px 18px 92px 16px rgba(0, 0, 0, 0.12);
//     backdrop-filter: blur(10px);
//   }
//   .am-list-body {
//     background: none;
//     border-top: 1px solid #fff;
//   }
//   .am-list-item {
//     background: none;
//     .am-list-line {
//       &::after {
//         border-bottom: 1px solid rgba(#fff, 0.5) !important;
//       }
//       .am-list-content {
//         color: #fff;
//       }
//     }
//   }
// }

// firebase style
.social-media-login {
  width: 100%;
  margin-top: 34px;
  text-align: center;

  .tips {
    @include flex-center;
    position: relative;
    gap: 20px;
    width: 100%;

    span {
      font-size: 24px;
      font-weight: normal;
      color: var(--text-color-primary);
      text-transform: capitalize;
    }

    &::before,
    &::after {
      width: 80px;
      height: 2px;
      content: "";
      background: var(--border-color-surface);
    }
  }

  .item-group {
    @include flex-center;
    margin-top: 12px;
  }

  .item {
    @include flex-center;

    & + .item {
      margin-left: 30px;
    }

    .am-icon,
    img {
      display: block;
      width: 70px;
      height: 70px;
      object-fit: contain;
    }
  }
}

// game select input
.game-select-wrap {
  position: relative;
  flex-shrink: 0;

  .game-search-box {
    position: relative;
    width: 100%;
    padding: 0 20px;
    margin-bottom: 10px;

    .search-input {
      width: 100%;
      height: 80px;
      padding: 0 28px 0 82px;
      font-size: 24px;
      font-weight: 500;
      line-height: normal;
      color: #fff;
      background: #750000;
      border: none;
      border-radius: 24px;

      &::placeholder {
        color: rgb(255 255 255 / 30%);
      }
    }

    .search-icon {
      position: absolute;
      top: 27px;
      left: 58px;
      width: 26px;
      height: 26px;
      opacity: 0.5;
    }
  }

  .close-select {
    position: absolute;
    top: 23px;
    right: 18px;
    z-index: 2;
    width: 20px;
    height: 20px;
  }

  .game-selected,
  .selected-name {
    display: flex;
    align-items: center;
  }

  .selected-icon {
    width: 28px;
    height: 28px;
    margin-right: 10px;
  }

  .simple-select {
    width: 100%;
    height: 100%;

    .select-wrap {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 100%;
      padding: 0 16px;
      font-size: 22px;
      font-weight: 700;
      line-height: normal;
      color: #363636;
      background: #eee;
      border-radius: 10px;

      &.on {
      }
    }

    .option-wrap {
      @include v-scroll;
      top: calc(100% + 10px);
      left: 0;
      width: 100%;
      max-height: 400px;
      overflow: hidden auto;
      overscroll-behavior: none;
      background: #eee;
      border-radius: 10px;
    }

    .option-bg {
      display: flex;
      flex-direction: column;
      gap: 8px;
      width: 100%;
    }
  }
}
