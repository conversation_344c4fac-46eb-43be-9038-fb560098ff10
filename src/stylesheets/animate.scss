@keyframes headShake {
  0% {
    transform: translateX(0);
  }

  6.5% {
    transform: translateX(-6px) rotateY(-9deg);
  }

  18.5% {
    transform: translateX(5px) rotateY(7deg);
  }

  31.5% {
    transform: translateX(-3px) rotateY(-5deg);
  }

  43.5% {
    transform: translateX(2px) rotateY(3deg);
  }

  50% {
    transform: translateX(0);
  }
}

.headShake {
  animation-name: headShake;
  animation-duration: 1s;
  animation-timing-function: ease-in-out;
}

.shine::after {
  position: absolute;
  top: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  pointer-events: none;
  content: "";
  background: linear-gradient(
    to right,
    rgb(255 255 255 / 0%) 0%,
    rgb(255 255 255 / 80%) 50%,
    rgb(128 186 232 / 0%) 99%,
    rgb(125 185 232 / 0%) 100%
  );
  transform: translateX(100%);
  animation: shine-slide 4s infinite;
}

@keyframes animate-shine {
  80% {
    left: -60%;
    opacity: 0;
  }

  100% {
    left: 130%;
    opacity: 1;
  }
}

@keyframes infinite-tada {
  78% {
    transform: scale3d(1, 1, 1);
  }

  80%,
  82% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }

  86%,
  90%,
  94%,
  98% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  88%,
  92%,
  96% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  100% {
    transform: scale3d(1, 1, 1);
  }
}

@keyframes shine-slide {
  0% {
    transform: translateX(-100%);
  }

  25% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(100%);
  }
}

@keyframes cf_cr {
  0% {
    transform: translateZ(0);
  }

  50% {
    transform: translate3d(10px, -10px, 0);
  }

  100% {
    transform: translateZ(0);
  }
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

@keyframes rolling {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(1turn);
  }

  100% {
    transform: rotate(2turn);
  }
}

@keyframes bg-float {
  0% {
    transform: translateX(120px) translateZ(0);
  }

  20% {
    transform: translateX(60px) translateZ(0);
  }

  40% {
    transform: translateX(120px) translateZ(0);
  }

  60% {
    transform: translateX(180px) translateZ(0);
  }

  80% {
    transform: translateX(60px) translateZ(0);
  }

  100% {
    transform: translateX(0) translateZ(0);
  }
}

.menu-fade-slide-down-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.menu-fade-slide-down-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition:
    opacity 260ms cubic-bezier(0.4, 0, 0.2, 1),
    transform 170ms cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-fade-slide-down-exit {
  opacity: 1;
  transform: translateY(0);
}

.menu-fade-slide-down-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition:
    opacity 260ms cubic-bezier(0.4, 0, 0.2, 1),
    transform 170ms cubic-bezier(0.4, 0, 0.2, 1) 80ms;
}

.menu-fade-slide-up-enter {
  opacity: 0;
  transform: translateY(10px);
}

.menu-fade-slide-up-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition:
    opacity 260ms cubic-bezier(0.4, 0, 0.2, 1),
    transform 170ms cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-fade-slide-up-exit {
  opacity: 1;
  transform: translateY(0);
}

.menu-fade-slide-up-exit-active {
  opacity: 0;
  transform: translateY(10px);
  transition:
    opacity 260ms cubic-bezier(0.4, 0, 0.2, 1),
    transform 170ms cubic-bezier(0.4, 0, 0.2, 1) 80ms;
}

// 进入动画
.page-enter {
  transform: translateX(100%);
}

.page-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-in-out;
}

// 离开动画
.page-exit {
  transform: translateX(0);
}

.page-exit-active {
  transform: translateX(-100%);
  transition: transform 300ms ease-in-out;
}

// 返回时的进入动画
.page-return-enter {
  transform: translateX(-100%);
}

.page-return-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-in-out;
}

// 返回时的离开动画
.page-return-exit {
  transform: translateX(0);
}

.page-return-exit-active {
  transform: translateX(100%);
  transition: transform 300ms ease-in-out;
}

/* Slide Left */
.slide-left-enter {
  opacity: 0;
  transform: translateX(100%);
}

.slide-left-enter-active {
  opacity: 1;
  transform: translateX(0%);
  transition: all 300ms ease;
}

.slide-left-exit {
  opacity: 1;
  transform: translateX(0%);
}

.slide-left-exit-active {
  opacity: 0;
  transform: translateX(-100%);
  transition: all 300ms ease;
}

/* Slide Right */
.slide-right-enter {
  opacity: 0;
  transform: translateX(-100%);
}

.slide-right-enter-active {
  opacity: 1;
  transform: translateX(0%);
  transition: all 300ms ease;
}

.slide-right-exit {
  opacity: 1;
  transform: translateX(0%);
}

.slide-right-exit-active {
  opacity: 0;
  transform: translateX(100%);
  transition: all 300ms ease;
}
