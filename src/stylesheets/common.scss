@import "./animate";
@import "./root";
@import "./form";

$main-font:
  Inter,
  SF Pro Text,
  SF Pro Icons,
  Helvetica Neue,
  Helvetica,
  -apple-system,
  BlinkMacSystemFont,
  Segoe UI,
  Roboto,
  PingFang SC,
  Hiragino Sans GB,
  Microsoft YaHei,
  Arial,
  sans-serif;

a,
abbr,
acronym,
address,
applet,
article,
aside,
audio,
b,
big,
blockquote,
body,
canvas,
caption,
center,
cite,
code,
dd,
del,
details,
dfn,
div,
dl,
dt,
em,
embed,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
html,
i,
iframe,
img,
ins,
kbd,
label,
legend,
li,
mark,
menu,
nav,
object,
ol,
output,
p,
pre,
q,
ruby,
s,
samp,
section,
small,
span,
strike,
strong,
sub,
summary,
sup,
table,
tbody,
td,
tfoot,
th,
thead,
time,
tr,
tt,
u,
ul,
var,
video {
  padding: 0;
  margin: 0;

  /* font-size: 100%; */

  /* font: inherit; */
  vertical-align: baseline;
  border: 0;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

html {
  body {
    font-family: $main-font;
    -webkit-overflow-scrolling: touch;

    button,
    input,
    optgroup,
    select,
    textarea {
      font-family: $main-font;
    }

    input {
      @include placeholder {
        font-family: $main-font;
      }
    }
  }
}

html,
body {
  width: 100%;
  height: 100%;

  &.overflow-hidden {
    overflow: hidden;
  }
}

html[data-contain="1"] {
  width: var(--body-max-width);
  margin: 0 auto;
  font-size: calc(var(--body-max-width) / (750 / 100)) !important;
  background: #e3e3e3;

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgb(0 0 0 / 20%);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgb(0 0 0 / 30%);
  }
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote::after,
blockquote::before,
q::after,
q::before {
  content: "";
  content: none;
}

table {
  border-spacing: 0;
  border-collapse: collapse;
}

.pointer {
  cursor: pointer;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

#mc-animate-container {
  transition:
    transform 0.3s,
    opacity 0.8s;
}

.outter-app {
  background: var(--bg-color-primary);
}

.am-toast.am-toast-mask {
  z-index: 100000000000;
}

.number-shell {
  font-family: "SF Pro Display", "SF Pro Icons", "AOS Icons", "Helvetica Neue For Number", "Roboto Mono", "PingFang SC",
    Arial, sans-serif;
}

.monospace-shell {
  font-family: "Helvetica Neue", "Roboto Mono", Avenir, sans-serif;
}

.hide-scrollbar {
  -ms-overflow-style: none;

  /* IE and Edge */
  scrollbar-width: none;

  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;

  /* Chrome, Safari and Opera */
}

._container_box {
  width: 100%;
  height: 100%;

  .lazy-load-image-background {
    width: 100%;
  }

  .img-loading {
    display: block;
    width: 100%;
  }
}

// padding footer
.avoid-footer {
  padding-bottom: var(--footer-height);
}

.app-main {
  position: relative;
  min-height: calc(100 * var(--vh, 1vh));
  color: var(--text-color-primary);
  background: var(--bg-color-primary);
}

// popup background
.common-popup-wrap.popup-standard {
  .common-popup-content {
    width: 716px;
    padding: 17.045px;
    background: rgb(138 197 218 / 50%);
    border: 1.705px solid #fff;
    border-radius: 17.045px;
    box-shadow: 0 0 31.222px 0 #8ac5da;
  }

  .popup-standard-bg {
    overflow: hidden;
    background: #fff;
    border: 1.705px solid #b1cdd8;
    border-radius: 17.045px;
  }

  .popup-standard-header {
    @include flex-center;
    width: 100%;
    height: 85px;
    font-size: 34px;
    font-weight: 800;
    line-height: normal;
    color: #fff;
    text-align: center;
    text-transform: uppercase;
  }

  .popup-close {
    @include flex-center;
    top: 34px;
    right: 34px;
    width: 24px;
    height: 24px;

    .am-icon {
      stroke: #fff;
    }
  }
}

// pagination
.pagination-container.common-pagination {
  .page-nav {
    width: 34px;
    height: 52px;

    .am-icon {
      display: block;
      width: 100%;
      height: 100%;
      fill: none;
    }
  }
}

// 活动入口
#root .promo-entry {
}

// DownloadImage
.download-image-modal1,
.download-image-modal2 {
  --download-image-banner1-bg: transparent;

  .am-modal-content {
    background-color: transparent;
  }
}
