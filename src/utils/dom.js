import { scrollTopTo } from "./scroll";

// 获取元素位置
export function getBoundingClientRect(domElement) {
  if (domElement) {
    const rect = domElement.getBoundingClientRect();
    return {
      top: rect.top,
      bottom: rect.bottom,
      left: rect.left,
      right: rect.right,
      width: rect.width || rect.right - rect.left,
      height: rect.height || rect.bottom - rect.top,
    };
  }
  return {
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    width: 0,
    height: 0,
  };
}

// 获得容器可见高度，实现better scroll必须的做的前置工作.
export const getScreenHeight = (element) => {
  if (document.querySelector(element)) {
    return document.documentElement.clientHeight - getBoundingClientRect(document.querySelector(element)).top;
  }
  return 0;
};

// 判断元素是否在可视区域
export const isElementInViewport = (domElement) => {
  const rect = getBoundingClientRect(domElement);

  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
};

// 以下API基于：https://developer.mozilla.org/en-US/docs/Web/API/Element/classList
// 支持大部分移动浏览器.

// 快速切换元素的CLASS
export const toggleClass = (domElement, className) => {
  const classes = domElement.classList;
  return classes.toggle(className);
};

// 切换元素的ID
export const toggleId = (domElement, idString) => {
  const id = domElement.getAttribute("id");
  if (id && id === idString) {
    domElement.setAttribute("id", "");
  } else {
    domElement.setAttribute("id", idString);
  }
};

// 删除元素ID
export const removeId = (domElement) => {
  domElement.removeAttribute("id");
};

// 检查元素是否包含指定的CLASS
export const hasClass = (domElement, className) => {
  const classes = domElement.classList;
  return classes.contains(className);
};

// 获取URL字符串的location部分.
export const getLocation = (href) => {
  var location = document.createElement("a");
  location.href = href;
  // IE doesn't populate all link properties when setting .href with a relative URL,
  // however .href will return an absolute URL which then can be used on itself
  // to populate these additional fields.
  // if (location.host === "") {
  //   location.href = location.href;
  // }
  return location;
};

export const remToPx = (rem) => {
  return rem * parseFloat(getComputedStyle(document.documentElement).fontSize);
};

export const getCssVar = (name) => {
  const val = getComputedStyle(document.documentElement).getPropertyValue(name);

  if (val.endsWith("rem")) {
    return parseFloat(val);
  }
  if (val) {
    return val.replaceAll(" ", "");
  }
  return 0;
};

export const getCssVarNumber = (varName) => {
  const value = getComputedStyle(document.documentElement).getPropertyValue(varName).trim();
  const number = parseFloat(value);
  return number;
};

export const parseCssValue = (value, base) => {
  if (typeof value === "number") return value;
  if (typeof value !== "string") return 0;

  if (value.endsWith("px")) return parseFloat(value);
  if (value.endsWith("rem")) {
    const remSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
    return parseFloat(value) * remSize;
  }
  if (value.endsWith("%")) return (parseFloat(value) / 100) * base;
  if (value.endsWith("vh")) return (parseFloat(value) / 100) * window.innerHeight;
  if (value.endsWith("vw")) return (parseFloat(value) / 100) * window.innerWidth;
  return parseFloat(value) || 0;
};

export const homeScrollTop = () => {
  const menuEl = document.getElementById("home-content");
  const headerEl = document.querySelector(".header-content");
  if (menuEl?.offsetTop) {
    const menuOffsetTop = menuEl.offsetTop - headerEl.clientHeight - headerEl.getBoundingClientRect()?.top;
    scrollTopTo(window, menuOffsetTop, 300);
  }
};
