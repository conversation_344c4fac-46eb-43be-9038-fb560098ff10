import CurrencyFormatter from "currencyformatter.js";

const parameters = {
  currency: "CNY", // If currency is not supplied, defaults to USD
  symbol: "￥", // Overrides the currency's default symbol
  locale: "zh_<PERSON>_CN", // Overrides the currency's default locale (see supported locales)
  decimal: ".", // Overrides the locale's decimal character
  group: ",", // Overrides the locale's group character (thousand separator)
  pattern: "#,##0.00", // Overrides the locale's default display pattern

  // The pattern follows standard unicode currency pattern notation.
  // comma = group separator, dot = decimal separator, exclamation = currency symbol
};

export default function (money, options) {
  return CurrencyFormatter.format(money, Object.assign(parameters, options));
}
