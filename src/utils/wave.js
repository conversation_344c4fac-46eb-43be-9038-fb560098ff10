class Wave {
  constructor(canvas, color) {
    this.t = 1000;
    this.w = canvas.offsetWidth;
    this.h = canvas.offsetHeight;
    this.k = 10;
    this.step = 1;
    this.color = color;
    this.speed = 0.05;
    this.a = 30;
    this.f = 10;
    this.d = 20;
    this.canvas = canvas;
    this.ctx = canvas.getContext("2d");
    this.init();
  }
  caculate(x) {
    this.amplitude = this.a * Math.sin(((this.t / this.k) * this.k) / 5);
    this.frequency = this.f * (1 / this.w);
    this.displacement = this.d;
    return this.amplitude * Math.sin(this.frequency * x + this.t) + this.displacement;
  }
  reset() {
    this.canvas.width = this.w;
    this.canvas.height = this.h;
  }
  loop() {
    const This = this;
    function drawloop() {
      window.requestAnimationFrame(drawloop);
      This.t += This.speed;
      This.ctx.clearRect(0, 0, This.w, This.h);
      This.ctx.save();
      This.ctx.beginPath();
      This.ctx.translate(0, This.h / 4);
      This.ctx.lineWidth = 1;
      This.ctx.moveTo(0, 0);
      for (let i = This.step; i < This.w; i += This.step) {
        This.ctx.lineTo(i, This.caculate(i));
      }
      This.ctx.lineTo(This.w, This.caculate(This.w));
      This.ctx.lineTo(This.w, This.h);
      This.ctx.lineTo(0, This.h);
      This.ctx.fillStyle = This.color;
      This.ctx.fill();
      This.ctx.closePath();
      This.ctx.restore();
    }
    drawloop();
  }
  init() {
    this.reset();
    this.loop();
  }
}

export default Wave;
