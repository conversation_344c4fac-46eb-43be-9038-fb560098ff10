import { noop } from "lodash";

import { isPromise } from "./basic";

export function callInterceptor(interceptor, options) {
  const { args = [], done = noop, cancel = noop, error = noop } = options;

  if (typeof interceptor === "function") {
    const result = interceptor.apply(null, args);

    if (isPromise(result)) {
      result
        .then(function (value) {
          if (value) {
            done();
          } else {
            cancel();
          }
        })
        .catch(error);
    } else {
      if (result) {
        done();
      } else {
        cancel();
      }
    }
  } else {
    done();
  }
}
