import React from "react";

let globalTemplateId = 0;

/**
 * 替换模板字符串中的占位符 `{n}` 为 React 元素
 *
 * @param {string} template - 模板字符串
 * @param {Object<number, ReactElement>} replacements - 替换用的 React 元素
 * @returns {Array<ReactElement|string>} - 渲染用的内容数组
 */
function formatTemplate(template, replacements) {
  const regex = /{(\d+)}/g;
  const result = [];

  let lastIndex = 0;
  let match;
  let localKey = 0;

  // 自动生成唯一 key 前缀
  const keyPrefix = `tpl-${globalTemplateId++}`;

  while ((match = regex.exec(template)) !== null) {
    const index = parseInt(match[1], 10);
    const matchStart = match.index;

    if (matchStart > lastIndex) {
      result.push(template.slice(lastIndex, matchStart));
    }

    if (replacements.hasOwnProperty(index)) {
      const original = replacements[index];
      const element =
        original.key != null ? original : React.cloneElement(original, { key: `${keyPrefix}-${localKey++}` });
      result.push(element);
    } else {
      result.push(match[0]);
    }

    lastIndex = regex.lastIndex;
  }

  if (lastIndex < template.length) {
    result.push(template.slice(lastIndex));
  }

  return result;
}

export default formatTemplate;
