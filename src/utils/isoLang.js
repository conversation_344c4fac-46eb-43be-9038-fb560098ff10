import deploy from "@/config/deploy.config";

export const isoLangMap = {
  CN: { code: "zh-cn", langRegionCode: "zh_CN" },
  EN: { code: "en", langRegionCode: "en_US" },
  JA: { code: "ja", langRegionCode: "ja_JP" },
  TH: { code: "th", langRegionCode: "th_TH" },
  VI: { code: "vi", langRegionCode: "vi_VN" },
  MY: { code: "my", langRegionCode: "my_MM" },
  ES: { code: "es", langRegionCode: "es_ES" },
  HI: { code: "hi", langRegionCode: "hi_IN" },
  ID: { code: "id", langRegionCode: "id_ID" },
  KM: { code: "km", langRegionCode: "km_KH" },
  KO: { code: "ko", langRegionCode: "ko_KR" },
  KR: { code: "ko", langRegionCode: "ko_KR" },
  MS: { code: "ms", langRegionCode: "ms_MY" },
  PT: { code: "pt", langRegionCode: "pt_PT" },
  TA: { code: "ta", langRegionCode: "ta_IN" },
  TW: { code: "zh-tw", langRegionCode: "zh_TW" },
  UR: { code: "ur", langRegionCode: "ur_PK" },
  TY: { code: "tl-ph", langRegionCode: "tl_PH" },
  TR: { code: "tr", langRegionCode: "tr_TR" },
  AR: { code: "ar", langRegionCode: "ar_SA" },
  "PT-BR": { code: "pt-br", langRegionCode: "pt_BR" },
  BN: { code: "bn", langRegionCode: "bn_BD" },
  UZ: { code: "uz", langRegionCode: "uz_UZ" },
  RU: { code: "ru", langRegionCode: "ru_RU" },
  FA: { code: "fa", langRegionCode: "fa_IR" },
  LO: { code: "lo", langRegionCode: "lo_LA" },
};

export const loadDayjsLang = () => {
  deploy.supportLanguages.map((item) => {
    const dayjsLang = isoLangMap[item]?.code;
    if (dayjsLang) {
      import(`dayjs/locale/${dayjsLang}`);
    }
    return null;
  });
};
