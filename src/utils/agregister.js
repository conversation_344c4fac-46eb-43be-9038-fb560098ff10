export function tieRod(rebate, series) {
  const tie = [];
  rebate = rebate ? rebate : [];
  series = series ? series : [];
  for (let i = 0; i < series.length; i++) {
    for (let j = 0; j < rebate.length; j++) {
      if (rebate[j].gameType === series[i].type) {
        let group = {};
        const max = rebate[j].highestRebate > series[i].rebateValue ? series[i].rebateValue : rebate[j].highestRebate;
        group.gameType = series[i].type;
        group.step = rebate[j].rebateInterval * 10000;
        group.max = max * 10000 - rebate[j].rebateDifference * 10000;
        group.min = rebate[j].minSeries * 10000;
        const minGap = (group.max - group.min) % group.step;
        group.min += minGap;
        tie.push(group);
      }
    }
  }
  return tie;
}

export function qutaTieRod(grebate, gvquta) {
  const configs = {};
  for (let i = 0; i < gvquta.length; i++) {
    for (let j = 0; j < grebate.length; j++) {
      if (gvquta[i].type === grebate[j].gameType) {
        configs[gvquta[i].type] =
          grebate[j].maxSeries > gvquta[i].rebateValue ? gvquta[i].rebateValue : grebate[j].maxSeries;
      }
    }
  }
  return configs;
}
export function neatenData(pushPlan) {
  const configs = [];
  for (let i = 0; i < pushPlan.basicSeries.length; i++) {
    if (!pushPlan.onSeries[pushPlan.basicSeries[i].gameType]) {
      const subConfig = {
        prizeModeId: !pushPlan.basicSeries[i].gameType.split("_")[1]
          ? null
          : pushPlan.basicSeries[i].gameType.split("_")[1],
        rebate: pushPlan.configs[pushPlan.basicSeries[i].gameType]
          ? pushPlan.configs[pushPlan.basicSeries[i].gameType]
          : pushPlan.basicSeries[i].max / 10000,
        gameCode: pushPlan.basicSeries[i].gameType.split("_")[0],
      };
      configs.push(subConfig);
    }
  }
  return configs;
}
export function neatendownline(pushPlan) {
  const configs = [];
  for (let i = 0; i < pushPlan.basicSeries.length; i++) {
    if (!pushPlan.onSeries[pushPlan.basicSeries[i].gameType]) {
      const subConfig = {
        rebateValue: pushPlan.configs[pushPlan.basicSeries[i].gameType]
          ? pushPlan.configs[pushPlan.basicSeries[i].gameType]
          : pushPlan.basicSeries[i].max / 10000,
        type: pushPlan.basicSeries[i].gameType,
      };
      configs.push(subConfig);
    }
  }
  return configs;
}

export function contrastRebate(series, pushInfo) {
  let bol = false,
    status = true;
  for (let v in series) {
    for (let i = 0; i < series[v].length; i++) {
      for (let j = 0; j < pushInfo.configs.length; j++) {
        if (series[v][i].type === pushInfo.configs[i].type) {
          bol = true;
          if (series[v][i].rebateValue < pushInfo.configs[i].rebateValue) status = false;
        }
      }
    }
  }

  return bol && status ? true : false;
}
