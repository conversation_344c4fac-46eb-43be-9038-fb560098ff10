class TouchHandler {
  constructor() {
    this.startX = 0; //手指触摸开始的 X 坐标
    this.startY = 0; //手指触摸开始的 Y 坐标
    this.deltaX = 0; //当前手指和起点 X 坐标的偏移量
    this.deltaY = 0; //当前手指和起点 Y 坐标的偏移量
    this.offsetX = 0; //deltaX 的绝对值
    this.offsetY = 0; //deltaY 的绝对值
    this.direction = ""; //当前滑动方向（'horizontal' or 'vertical' or ''）
    this.isTap = true; //是否轻点

    this.start = (event) => {
      this.reset();
      const touch = event.touches[0];
      this.startX = touch.clientX;
      this.startY = touch.clientY;
      this.isTap = true;
    };

    this.move = (event) => {
      const touch = event.touches[0];
      this.deltaX = (touch.clientX < 0 ? 0 : touch.clientX) - this.startX;
      this.deltaY = touch.clientY - this.startY;
      this.offsetX = Math.abs(this.deltaX);
      this.offsetY = Math.abs(this.deltaY);

      if (!this.direction) {
        this.direction = this.getDirection(this.offsetX, this.offsetY);
      }

      if (this.isTap && (this.offsetX > 3 || this.offsetY > 3)) {
        this.isTap = false;
      }
    };
  }

  reset = () => {
    this.deltaX = 0;
    this.deltaY = 0;
    this.offsetX = 0;
    this.offsetY = 0;
    this.direction = "";
    this.isTap = true;
  };

  isVertical = () => this.direction === "vertical";

  isHorizontal = () => this.direction === "horizontal";

  getDirection = (x, y) => {
    const MIN_DISTANCE = 10;
    if (x > y && x > MIN_DISTANCE) {
      return "horizontal";
    }
    if (y > x && y > MIN_DISTANCE) {
      return "vertical";
    }
    return "";
  };
}

export default TouchHandler;
