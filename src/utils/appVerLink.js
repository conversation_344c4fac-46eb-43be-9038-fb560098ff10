import { get } from "./storage";

export const handleWindowPage = (url) => {
  let u = navigator.userAgent;
  let isiOS = !!u.match(/iOS.+AppShellVer/);
  //是否是IOS设备并且是APP
  if (isiOS) {
    if (
      window.webkit &&
      window.webkit.messageHandlers &&
      window.webkit.messageHandlers.openSafari &&
      window.webkit.messageHandlers.openSafari.postMessage
    ) {
      window.webkit.messageHandlers.openSafari.postMessage({
        url: url,
        type: 1,
      });
    } else {
      window.open(url, "_blank");
    }
  } else {
    if (window.Android && window.Android.openAndroid) {
      window.Android.openAndroid(url);
    } else {
      window.open(url, "_blank");
    }
  }
};

export function getMCExternalLink(url) {
  const { token, merchantCode } = get("MC_SESSION_INFO") || {};
  return `${url}?token=${token}&merchant=${merchantCode}`;
}
