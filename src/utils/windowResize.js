import { debounce } from "lodash";
import { navigatorUtil } from "tcg-mobile-common";

const DESIGN_RATIO = 9 / 16;

const { isMobile } = navigatorUtil;

function updateLobbyMaxWidth() {
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;

  const maxWidthBasedOnHeight = windowHeight * DESIGN_RATIO;

  const finalMaxWidth = Math.min(windowWidth, maxWidthBasedOnHeight);

  document.documentElement.style.setProperty("--body-max-width", `${finalMaxWidth}px`);
}

function updateContainMode() {
  let html = document.querySelector("html");
  let body = document.querySelector("body");
  if (!isMobile() || window.innerWidth > 768) {
    html.style.height = "100vh";
    html.style.overflow = "hidden";
    body.style.transform = "translate(0)";
    document.documentElement.setAttribute("data-contain", "1");
    updateLobbyMaxWidth();
  } else {
    html.style.height = "";
    html.style.overflow = "";
    body.style.transform = "";
    document.documentElement.removeAttribute("data-contain");
    document.documentElement.style.removeProperty("--body-max-width");
  }
}

const debouncedUpdateContainMode = debounce(updateContainMode, 100);

function initLobbyResizeListener() {
  updateContainMode();
  window.addEventListener("resize", debouncedUpdateContainMode);
}

function removeLobbyResizeListener() {
  window.removeEventListener("resize", debouncedUpdateContainMode);
}

export { initLobbyResizeListener, removeLobbyResizeListener };
