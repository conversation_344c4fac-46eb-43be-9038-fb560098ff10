export function tran(v) {
  const d = {};
  for (let i = 0; i < v.length; i++) {
    d[v[i].customer_name] = {
      name: v[i].customer_name,
      status: false,
    };
  }
  return d;
}
export function find(v, t) {
  const d = {};
  for (let k in v) {
    if (k.match(t)) {
      d[k] = v[k];
    }
  }
  return d;
}
export function pitch(v, s) {
  for (let k in v) {
    if (s.indexOf(k) > -1) v[k].status = true;
    else v[k].status = false;
  }
  return v;
}
export function pitchAll(v, s) {
  for (let k in v) {
    if (s.indexOf(k) < 0) s.push(k);
    v[k].status = true;
  }
  return { show: v, select: s };
}
export function cleanAll(v, s) {
  for (let k in v) {
    if (s.indexOf(k) > -1) s.splice(s.indexOf(k), 1);
    v[k].status = false;
  }
  return { show: v, select: s };
}
