const overflowScrollReg = /scroll|auto/i;

function isElement(node) {
  return node && node.tagName !== "HTML" && node.tagName !== "BODY" && node.nodeType === 1;
}

export function getScrollParent(el, root = typeof window !== "undefined" ? window : null) {
  if (typeof window === "undefined") return root;

  let node = el;

  while (node && node !== root && isElement(node)) {
    const overflowY = window.getComputedStyle(node).overflowY;
    if (overflowScrollReg.test(overflowY)) {
      return node;
    }
    node = node.parentNode;
  }

  return root;
}

// 判断页面是否滚动到底部的函数
export const isAtBottom = () => {
  // 当前视窗的高度
  const windowHeight = document.documentElement.clientHeight;

  // 整个文档的高度
  const documentHeight = Math.max(
    document.body.scrollHeight,
    document.documentElement.scrollHeight,
    document.body.offsetHeight,
    document.documentElement.offsetHeight,
    document.body.clientHeight,
    document.documentElement.clientHeight
  );

  // 当前滚动位置
  const scrollTop = document.documentElement.scrollTop;

  // 判断是否滚动到底部
  return scrollTop + windowHeight >= documentHeight - 1;
};

export function getScrollTop(el) {
  const top = "scrollTop" in el ? el.scrollTop : el.pageYOffset;

  // iOS scroll bounce cause minus scrollTop
  return Math.max(top, 0);
}

export function setScrollTop(el, value) {
  if ("scrollTop" in el) {
    el.scrollTop = value;
  } else {
    el.scrollTo(el.scrollX, value);
  }
}

export function scrollTopTo(scroller, to, duration, callback) {
  let current = getScrollTop(scroller);
  const isDown = current < to;
  const frames = duration === 0 ? 1 : Math.round(duration / 16);
  const step = (to - current) / frames;

  function animate() {
    current += step;
    if ((isDown && current > to) || (!isDown && current < to)) {
      current = to;
    }

    setScrollTop(scroller, current);

    if ((isDown && current < to) || (!isDown && current > to)) {
      window.requestAnimationFrame(animate);
    } else if (callback) {
      callback();
    }
  }

  animate();
}

let rafId = 0;

export function scrollLeftTo(scroller, to, duration) {
  window.cancelAnimationFrame(rafId);

  let count = 0;
  const from = scroller.scrollLeft;
  const frames = duration === 0 ? 1 : Math.round(duration / 16);

  function animate() {
    scroller.scrollLeft += (to - from) / frames;
    if (++count < frames) {
      rafId = window.requestAnimationFrame(animate);
    }
  }

  animate();
}
