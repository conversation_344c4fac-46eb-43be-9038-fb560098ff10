import { navigatorUtil } from "tcg-mobile-common";

export function iOSVersion() {
  return navigatorUtil.iOSVersion();
}

export function isIos() {
  return navigatorUtil.isIos;
}

export function isAndroid() {
  return /android/.test(navigator.userAgent.toLowerCase());
}

export function isApp() {
  return navigatorUtil.isApp;
}

export function isPWA() {
  return navigatorUtil.isPWA;
}

/**
 * 是否使用iOS PWA, 並且版本小於 12.2
 */
export function isOldIOSPWA() {
  const [major, minor] = iOSVersion();
  if (isApp() || !isPWA()) {
    return false;
  }
  if (major !== undefined) {
    return (major === 12 && minor < 2) || major <= 11;
  }
  return false;
}

export function isNotSupportPWAIos() {
  const [major, minor] = iOSVersion();
  if (major !== undefined) {
    return (major === 11 && minor < 3) || major <= 11;
  }
  return false;
}

export function isMobile() {
  return navigatorUtil.isMobile();
}

export function isSafari() {
  return navigatorUtil.isSafari();
}

export function getDeviceLang(support = [], defaultLang) {
  if (support.includes(defaultLang)) {
    return defaultLang;
  }
  let sysLang = navigator.language.toUpperCase();
  if (sysLang.includes("ZH")) {
    sysLang = "CN";
  }
  const find = support.find((v) => sysLang.includes(v));
  return find || defaultLang || support[0];
}

export function inputScrollIntoView($input) {
  if (isAndroid()) {
    setTimeout(() => {
      $input.scrollIntoView({ block: "center" });
    }, 500);
  }
}

export function isAndroidApp() {
  return isAndroid() && isApp();
}

export function latestAppVersion(base) {
  try {
    const [, version] = navigator.userAgent.match(/App\S*?Ver:(\d+\.\d+\.\d+)/);
    const [main, sub, minor] = version.split(".");
    const [baseMain, baseSub, baseMinor] = base.split(".");
    if (+main > +baseMain) {
      return true;
    } else if (+main < +baseMain) {
      return false;
    }
    if (+sub > +baseSub) {
      return true;
    } else if (+sub < +baseSub) {
      return false;
    }
    return +minor >= +baseMinor;
  } catch (e) {
    return true;
  }
}
