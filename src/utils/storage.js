import { get as _get } from "lodash";

export function isObject(x) {
  return x != null && typeof x === "object";
}

export const get = (keyName) => {
  const val = sessionStorage.getItem(keyName);
  if (val) {
    return JSON.parse(val);
  }
  return undefined;
};

export const localGet = (keyName) => {
  const val = localStorage.getItem(keyName);
  if (val) {
    return JSON.parse(val);
  }
  return undefined;
};

export const localSet = (keyName, val) => {
  let parsedVal;
  if (isObject(val)) {
    parsedVal = JSON.stringify(val);
  } else {
    parsedVal = val;
  }
  window.localStorage.setItem(keyName, parsedVal);
};

export const set = (keyName, val) => {
  let parsedVal;
  if (isObject(val)) {
    parsedVal = JSON.stringify(val);
  } else {
    parsedVal = val;
  }
  window.sessionStorage.setItem(keyName, parsedVal);
};

export const each = (fn) => {
  for (let i = window.sessionStorage.length - 1; i >= 0; i--) {
    const key = window.sessionStorage.key(i);
    fn(get(key), key);
  }
};
export const remove = (keyName) => window.sessionStorage.removeItem(keyName);
export const clearALL = () => window.sessionStorage.clear();
export const removes = (keys) => {
  keys.forEach((key) => {
    remove(key);
  });
};

export const isTokenExists = () => get("MC_SESSION_INFO");
export const isMemberInfoExists = () => get("memberInfo");
export const getToken = () => {
  if (isTokenExists()) {
    return get("MC_SESSION_INFO").token;
  }
  return "";
};

export const transToMap = (routes, defaultIfNotFound) => {
  const map = routes.reduce((acc, curr) => {
    acc[curr.path] = curr;
    return acc;
  }, {});
  return {
    getIn(arr) {
      return _get(map, arr.join("."), defaultIfNotFound);
    },
  };
};

export const removeStartWith = (pattern = []) => {
  let list = [];
  if (typeof pattern === "string") {
    list.push(pattern);
  } else {
    list = [...pattern];
  }
  list = list.map((v) => v.toUpperCase());

  for (let i = window.sessionStorage.length - 1; i >= 0; i--) {
    const key = window.sessionStorage.key(i);
    const match = list.some((s) => key.toUpperCase().startsWith(s));
    if (match) {
      window.sessionStorage.removeItem(key);
    }
  }
};
