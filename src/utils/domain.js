export const getAffCode = () => {
  const hostname = window.location.hostname;
  const str = hostname.split(".");
  const affCode = str[str.length - 2];
  const data = {};
  if (affCode === "com" || affCode === "org" || affCode === "net" || affCode === "cn") {
    data.code = str[str.length - 4];
    data.domain = str[str.length - 3];
  } else {
    if (str.length - 2 <= 0) {
      data.code = "www";
    } else {
      data.code = str[str.length - 3];
    }
    data.domain = affCode;
  }
  return data;
};
