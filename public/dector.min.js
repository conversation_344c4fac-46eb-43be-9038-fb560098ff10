!(function(e, n) {
  "object" == typeof exports && "object" == typeof module
    ? (module.exports = n())
    : "function" == typeof define && define.amd
      ? define(n)
      : "object" == typeof exports
        ? (exports.detector = n())
        : (e.detector = n());
})(this, function() {
  return (function(e) {
    function n(o) {
      if (r[o]) return r[o].exports;
      var i = (r[o] = { exports: {}, id: o, loaded: !1 });
      return e[o].call(i.exports, i, i.exports, n), (i.loaded = !0), i.exports;
    }
    var r = {};
    return (n.m = e), (n.c = r), (n.p = ""), n(0);
  })([
    function(e, n, r) {
      e.exports = r(2);
    },
    function(e, n) {
      "use strict";
      function r(e, n) {
        if (!(e instanceof n))
          throw new TypeError("Cannot call a class as a function");
      }
      function o(e) {
        return function(n) {
          return Object.prototype.toString.call(n) === "[object " + e + "]";
        };
      }
      function i(e, n) {
        for (var r = 0, o = e.length; o > r && n.call(e, e[r], r) !== !1; r++);
      }
      function t(e, n, r) {
        var o = f(n) ? n.call(null, r) : n;
        if (!o) return null;
        var i = { name: e, version: b, codename: "" };
        if (o === !0) return i;
        if (d(o)) {
          if (-1 !== r.indexOf(o)) return i;
        } else {
          if (l(o))
            return o.hasOwnProperty("version") && (i.version = o.version), i;
          if (c(o)) {
            var t = o.exec(r);
            if (t)
              return (
                t.length >= 2 && t[1]
                  ? (i.version = t[1].replace(/_/g, "."))
                  : (i.version = b),
                i
              );
          }
        }
      }
      function a(e, n, r, o) {
        var a = u;
        i(n, function(n) {
          var r = t(n[0], n[1], e);
          return r ? ((a = r), !1) : void 0;
        }),
          r.call(o, a.name, a.version);
      }
      var s = (function() {
          function e(e, n) {
            for (var r = 0; r < n.length; r++) {
              var o = n[r];
              (o.enumerable = o.enumerable || !1),
                (o.configurable = !0),
                "value" in o && (o.writable = !0),
                Object.defineProperty(e, o.key, o);
            }
          }
          return function(n, r, o) {
            return r && e(n.prototype, r), o && e(n, o), n;
          };
        })(),
        b = "-1",
        u = { name: "na", version: b },
        d = o("String"),
        c = o("RegExp"),
        l = o("Object"),
        f = o("Function"),
        p = (function() {
          function e(n) {
            r(this, e), (this._rules = n);
          }
          return (
            s(e, [
              {
                key: "parse",
                value: function(e) {
                  e = (e || "").toLowerCase();
                  var n = {};
                  a(
                    e,
                    this._rules.device,
                    function(e, r) {
                      var o = parseFloat(r);
                      (n.device = { name: e, version: o, fullVersion: r }),
                        (n.device[e] = o);
                    },
                    n
                  ),
                    a(
                      e,
                      this._rules.os,
                      function(e, r) {
                        var o = parseFloat(r);
                        (n.os = { name: e, version: o, fullVersion: r }),
                          (n.os[e] = o);
                      },
                      n
                    );
                  var r = this.IEMode(e);
                  return (
                    a(
                      e,
                      this._rules.engine,
                      function(e, o) {
                        var i = o;
                        r &&
                          ((o = r.engineVersion || r.engineMode),
                          (i = r.engineMode));
                        var t = parseFloat(o);
                        (n.engine = {
                          name: e,
                          version: t,
                          fullVersion: o,
                          mode: parseFloat(i),
                          fullMode: i,
                          compatible: r ? r.compatible : !1
                        }),
                          (n.engine[e] = t);
                      },
                      n
                    ),
                    a(
                      e,
                      this._rules.browser,
                      function(e, o) {
                        var i = o;
                        r &&
                          ("ie" === e && (o = r.browserVersion),
                          (i = r.browserMode));
                        var t = parseFloat(o);
                        (n.browser = {
                          name: e,
                          version: t,
                          fullVersion: o,
                          mode: parseFloat(i),
                          fullMode: i,
                          compatible: r ? r.compatible : !1
                        }),
                          (n.browser[e] = t);
                      },
                      n
                    ),
                    n
                  );
                }
              },
              {
                key: "IEMode",
                value: function(e) {
                  if (!this._rules.re_msie.test(e)) return null;
                  var n = void 0,
                    r = void 0,
                    o = void 0,
                    i = void 0,
                    t = void 0;
                  if (
                    -1 !== e.indexOf("trident/") &&
                    ((n = /\btrident\/([0-9.]+)/.exec(e)), n && n.length >= 2)
                  ) {
                    o = n[1];
                    var a = n[1].split(".");
                    (a[0] = parseInt(a[0], 10) + 4), (t = a.join("."));
                  }
                  (n = this._rules.re_msie.exec(e)), (i = n[1]);
                  var s = n[1].split(".");
                  return (
                    "undefined" == typeof t && (t = i),
                    (s[0] = parseInt(s[0], 10) - 4),
                    (r = s.join(".")),
                    "undefined" == typeof o && (o = r),
                    {
                      browserVersion: t,
                      browserMode: i,
                      engineVersion: o,
                      engineMode: r,
                      compatible: o !== r
                    }
                  );
                }
              }
            ]),
            e
          );
        })();
      e.exports = p;
    },
    function(e, n, r) {
      "use strict";
      function o(e) {
        if (!a.re_msie.test(e)) return null;
        var n = void 0,
          r = void 0,
          o = void 0,
          i = void 0,
          t = void 0;
        if (
          -1 !== e.indexOf("trident/") &&
          ((n = /\btrident\/([0-9.]+)/.exec(e)), n && n.length >= 2)
        ) {
          o = n[1];
          var s = n[1].split(".");
          (s[0] = parseInt(s[0], 10) + 4), (t = s.join("."));
        }
        (n = a.re_msie.exec(e)), (i = n[1]);
        var b = n[1].split(".");
        return (
          "undefined" == typeof t && (t = i),
          (b[0] = parseInt(b[0], 10) - 4),
          (r = b.join(".")),
          "undefined" == typeof o && (o = r),
          {
            browserVersion: t,
            browserMode: i,
            engineVersion: o,
            engineMode: r,
            compatible: o !== r
          }
        );
      }
      function i(e) {
        var n = c.parse(e),
          r = o(e);
        if (r) {
          var i = n.engine.name,
            t = r.engineVersion || r.engineMode,
            a = parseFloat(t),
            s = r.engineMode;
          (n.engine = {
            name: i,
            version: a,
            fullVersion: t,
            mode: parseFloat(s),
            fullMode: s,
            compatible: r ? r.compatible : !1
          }),
            (n.engine[n.engine.name] = a);
          var b = n.browser.name,
            u = n.browser.fullVersion;
          "ie" === b && (u = r.browserVersion);
          var d = r.browserMode,
            l = parseFloat(u);
          (n.browser = {
            name: b,
            version: l,
            fullVersion: u,
            mode: parseFloat(d),
            fullMode: d,
            compatible: r ? r.compatible : !1
          }),
            (n.browser[b] = l);
        }
        return n;
      }
      var t = r(1),
        a = r(3),
        s = navigator.userAgent || "",
        b = navigator.appVersion || "",
        u = navigator.vendor || "",
        d = s + " " + b + " " + u,
        c = new t(a),
        l = i(d);
      (l.parse = i), (e.exports = l);
    },
    function(e, n) {
      (function(n) {
        "use strict";
        function r(e) {
          if (i)
            try {
              var n = i.twGetRunPath.toLowerCase(),
                r = i.twGetSecurityID(o),
                t = i.twGetVersion(r);
              if (n && -1 === n.indexOf(e)) return !1;
              if (t) return { version: t };
            } catch (a) {}
        }
        var o = "undefined" == typeof window ? n : window,
          i = o.external,
          t = /\b(?:msie |ie |trident\/[0-9].*rv[ :])([0-9.]+)/,
          a = /\bbb10\b.+?\bversion\/([\d.]+)/,
          s = /\bblackberry\b.+\bversion\/([\d.]+)/,
          b = /\bblackberry\d+\/([\d.]+)/,
          u = "-1",
          d = [
            [
              "nokia",
              function(e) {
                return -1 !== e.indexOf("nokia ")
                  ? /\bnokia ([0-9]+)?/
                  : /\bnokia([a-z0-9]+)?/;
              }
            ],
            [
              "samsung",
              function(e) {
                return -1 !== e.indexOf("samsung")
                  ? /\bsamsung(?:[ \-](?:sgh|gt|sm))?-([a-z0-9]+)/
                  : /\b(?:sgh|sch|gt|sm)-([a-z0-9]+)/;
              }
            ],
            [
              "wp",
              function(e) {
                return (
                  -1 !== e.indexOf("windows phone ") ||
                  -1 !== e.indexOf("xblwp") ||
                  -1 !== e.indexOf("zunewp") ||
                  -1 !== e.indexOf("windows ce")
                );
              }
            ],
            ["pc", "windows"],
            ["ipad", "ipad"],
            ["ipod", "ipod"],
            ["iphone", /\biphone\b|\biph(\d)/],
            ["mac", "macintosh"],
            ["mi", /\bmi[ \-]?([a-z0-9 ]+(?= build|\)))/],
            ["hongmi", /\bhm[ \-]?([a-z0-9]+)/],
            ["aliyun", /\baliyunos\b(?:[\-](\d+))?/],
            [
              "meizu",
              function(e) {
                return e.indexOf("meizu") >= 0
                  ? /\bmeizu[\/ ]([a-z0-9]+)\b/
                  : /\bm([0-9cx]{1,4})\b/;
              }
            ],
            ["nexus", /\bnexus ([0-9s.]+)/],
            [
              "huawei",
              function(e) {
                var n = /\bmediapad (.+?)(?= build\/huaweimediapad\b)/;
                return -1 !== e.indexOf("huawei-huawei")
                  ? /\bhuawei\-huawei\-([a-z0-9\-]+)/
                  : n.test(e) ? n : /\bhuawei[ _\-]?([a-z0-9]+)/;
              }
            ],
            [
              "lenovo",
              function(e) {
                return -1 !== e.indexOf("lenovo-lenovo")
                  ? /\blenovo\-lenovo[ \-]([a-z0-9]+)/
                  : /\blenovo[ \-]?([a-z0-9]+)/;
              }
            ],
            [
              "zte",
              function(e) {
                return /\bzte\-[tu]/.test(e)
                  ? /\bzte-[tu][ _\-]?([a-su-z0-9\+]+)/
                  : /\bzte[ _\-]?([a-su-z0-9\+]+)/;
              }
            ],
            ["vivo", /\bvivo(?: ([a-z0-9]+))?/],
            [
              "htc",
              function(e) {
                return /\bhtc[a-z0-9 _\-]+(?= build\b)/.test(e)
                  ? /\bhtc[ _\-]?([a-z0-9 ]+(?= build))/
                  : /\bhtc[ _\-]?([a-z0-9 ]+)/;
              }
            ],
            ["oppo", /\boppo[_]([a-z0-9]+)/],
            ["konka", /\bkonka[_\-]([a-z0-9]+)/],
            ["sonyericsson", /\bmt([a-z0-9]+)/],
            ["coolpad", /\bcoolpad[_ ]?([a-z0-9]+)/],
            ["lg", /\blg[\-]([a-z0-9]+)/],
            ["android", /\bandroid\b|\badr\b/],
            [
              "blackberry",
              function(e) {
                return e.indexOf("blackberry") >= 0
                  ? /\bblackberry\s?(\d+)/
                  : "bb10";
              }
            ]
          ],
          c = [
            [
              "wp",
              function(e) {
                return -1 !== e.indexOf("windows phone ")
                  ? /\bwindows phone (?:os )?([0-9.]+)/
                  : -1 !== e.indexOf("xblwp")
                    ? /\bxblwp([0-9.]+)/
                    : -1 !== e.indexOf("zunewp")
                      ? /\bzunewp([0-9.]+)/
                      : "windows phone";
              }
            ],
            ["windows", /\bwindows nt ([0-9.]+)/],
            ["macosx", /\bmac os x ([0-9._]+)/],
            [
              "ios",
              function(e) {
                return /\bcpu(?: iphone)? os /.test(e)
                  ? /\bcpu(?: iphone)? os ([0-9._]+)/
                  : -1 !== e.indexOf("iph os ")
                    ? /\biph os ([0-9_]+)/
                    : /\bios\b/;
              }
            ],
            ["yunos", /\baliyunos ([0-9.]+)/],
            [
              "android",
              function(e) {
                return e.indexOf("android") >= 0
                  ? /\bandroid[ \/-]?([0-9.x]+)?/
                  : e.indexOf("adr") >= 0
                    ? e.indexOf("mqqbrowser") >= 0
                      ? /\badr[ ]\(linux; u; ([0-9.]+)?/
                      : /\badr(?:[ ]([0-9.]+))?/
                    : "android";
              }
            ],
            ["chromeos", /\bcros i686 ([0-9.]+)/],
            ["linux", "linux"],
            ["windowsce", /\bwindows ce(?: ([0-9.]+))?/],
            ["symbian", /\bsymbian(?:os)?\/([0-9.]+)/],
            [
              "blackberry",
              function(e) {
                var n = e.match(a) || e.match(s) || e.match(b);
                return n ? { version: n[1] } : "blackberry";
              }
            ]
          ],
          l = [
            ["edgehtml", /edge\/([0-9.]+)/],
            ["trident", t],
            [
              "blink",
              function() {
                return (
                  "chrome" in o && "CSS" in o && /\bapplewebkit[\/]?([0-9.+]+)/
                );
              }
            ],
            ["webkit", /\bapplewebkit[\/]?([0-9.+]+)/],
            [
              "gecko",
              function(e) {
                var n = e.match(/\brv:([\d\w.]+).*\bgecko\/(\d+)/);
                return n ? { version: n[1] + "." + n[2] } : void 0;
              }
            ],
            ["presto", /\bpresto\/([0-9.]+)/],
            ["androidwebkit", /\bandroidwebkit\/([0-9.]+)/],
            ["coolpadwebkit", /\bcoolpadwebkit\/([0-9.]+)/],
            ["u2", /\bu2\/([0-9.]+)/],
            ["u3", /\bu3\/([0-9.]+)/]
          ],
          f = [
            ["edge", /edge\/([0-9.]+)/],
            [
              "sogou",
              function(e) {
                return e.indexOf("sogoumobilebrowser") >= 0
                  ? /sogoumobilebrowser\/([0-9.]+)/
                  : e.indexOf("sogoumse") >= 0 ? !0 : / se ([0-9.x]+)/;
              }
            ],
            [
              "theworld",
              function() {
                var e = r("theworld");
                return "undefined" != typeof e ? e : /theworld(?: ([\d.])+)?/;
              }
            ],
            [
              "360",
              function(e) {
                var n = r("360se");
                return "undefined" != typeof n
                  ? n
                  : -1 !== e.indexOf("360 aphone browser")
                    ? /\b360 aphone browser \(([^\)]+)\)/
                    : /\b360(?:se|ee|chrome|browser)\b/;
              }
            ],
            [
              "maxthon",
              function() {
                try {
                  if (i && (i.mxVersion || i.max_version))
                    return { version: i.mxVersion || i.max_version };
                } catch (e) {}
                return /\b(?:maxthon|mxbrowser)(?:[ \/]([0-9.]+))?/;
              }
            ],
            ["micromessenger", /\bmicromessenger\/([\d.]+)/],
            ["qq", /\bm?qqbrowser\/([0-9.]+)/],
            ["green", "greenbrowser"],
            ["tt", /\btencenttraveler ([0-9.]+)/],
            [
              "liebao",
              function(e) {
                if (e.indexOf("liebaofast") >= 0)
                  return /\bliebaofast\/([0-9.]+)/;
                if (-1 === e.indexOf("lbbrowser")) return !1;
                var n = void 0;
                try {
                  i && i.LiebaoGetVersion && (n = i.LiebaoGetVersion());
                } catch (r) {}
                return { version: n || u };
              }
            ],
            ["tao", /\btaobrowser\/([0-9.]+)/],
            ["coolnovo", /\bcoolnovo\/([0-9.]+)/],
            ["saayaa", "saayaa"],
            ["baidu", /\b(?:ba?idubrowser|baiduhd)[ \/]([0-9.x]+)/],
            ["ie", t],
            ["mi", /\bmiuibrowser\/([0-9.]+)/],
            [
              "opera",
              function(e) {
                var n = /\bopera.+version\/([0-9.ab]+)/,
                  r = /\bopr\/([0-9.]+)/;
                return n.test(e) ? n : r;
              }
            ],
            ["oupeng", /\boupeng\/([0-9.]+)/],
            ["yandex", /yabrowser\/([0-9.]+)/],
            [
              "ali-ap",
              function(e) {
                return e.indexOf("aliapp") > 0
                  ? /\baliapp\(ap\/([0-9.]+)\)/
                  : /\balipayclient\/([0-9.]+)\b/;
              }
            ],
            ["ali-ap-pd", /\baliapp\(ap-pd\/([0-9.]+)\)/],
            ["ali-am", /\baliapp\(am\/([0-9.]+)\)/],
            ["ali-tb", /\baliapp\(tb\/([0-9.]+)\)/],
            ["ali-tb-pd", /\baliapp\(tb-pd\/([0-9.]+)\)/],
            ["ali-tm", /\baliapp\(tm\/([0-9.]+)\)/],
            ["ali-tm-pd", /\baliapp\(tm-pd\/([0-9.]+)\)/],
            [
              "uc",
              function(e) {
                return e.indexOf("ucbrowser/") >= 0
                  ? /\bucbrowser\/([0-9.]+)/
                  : e.indexOf("ubrowser/") >= 0
                    ? /\bubrowser\/([0-9.]+)/
                    : /\buc\/[0-9]/.test(e)
                      ? /\buc\/([0-9.]+)/
                      : e.indexOf("ucweb") >= 0
                        ? /\bucweb([0-9.]+)?/
                        : /\b(?:ucbrowser|uc)\b/;
              }
            ],
            ["chrome", / (?:chrome|crios|crmo)\/([0-9.]+)/],
            [
              "android",
              function(e) {
                return -1 !== e.indexOf("android")
                  ? /\bversion\/([0-9.]+(?: beta)?)/
                  : void 0;
              }
            ],
            [
              "blackberry",
              function(e) {
                var n = e.match(a) || e.match(s) || e.match(b);
                return n ? { version: n[1] } : "blackberry";
              }
            ],
            [
              "safari",
              /\bversion\/([0-9.]+(?: beta)?)(?: mobile(?:\/[a-z0-9]+)?)? safari\//
            ],
            ["webview", /\bcpu(?: iphone)? os (?:[0-9._]+).+\bapplewebkit\b/],
            ["firefox", /\bfirefox\/([0-9.ab]+)/],
            ["nokia", /\bnokiabrowser\/([0-9.]+)/]
          ];
        e.exports = { device: d, os: c, browser: f, engine: l, re_msie: t };
      }.call(
        n,
        (function() {
          return this;
        })()
      ));
    }
  ]);
});
