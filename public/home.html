<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <title>Redirecting</title>
</head>
<body>
<script>
  var isMobile = ('DeviceOrientationEvent' in window || 'orientation' in window);
  if (/Windows NT|Macintosh|Mac OS X|Linux/i.test(navigator.userAgent)) isMobile = false;
  if (/Mobile|Android|webOS|iPhone|iPad|iPod|BlackBerry|Windows Phone/i.test(navigator.userAgent)) isMobile = true;
  if (isMobile) {
    location.replace(location.protocol + "//" + window.location.host + "/m/index.html");
  } else {
    location.replace(location.protocol + "//" + window.location.host + "/index.html");
  }
</script>
</body>
</html>
