<!doctype html>
<html lang="%LANGUAGE%" content="notranslate">
  <head>
    <meta charset="%CHARSET%" />
    <meta name="google" content="notranslate" />
    <meta name="theme-color" content="#1E002F" />
    <meta
      name="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />

    <!-- Primary Meta Tags Start -->
    <title>royalmanila.com</title>
    <meta name="title" content="royalmanila.com" />
    <meta name="description" content="" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="royalmanila.com" />
    <meta property="og:description" content="" />
    <meta property="og:image" content="%PUBLIC_URL%/meta-image.png" />

    <!-- Twitter -->
    <meta property="twitter:title" content="royalmanila.com" />
    <meta property="twitter:description" content="" />
    <meta property="twitter:image" content="%PUBLIC_URL%/meta-image.png" />
    <!-- Primary Meta Tags End -->

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
      rel="stylesheet"
    />

    <!--
      manifest.json provides metadata used when your web app is added to the
      homescreen on Android. See https://developers.google.com/web/fundamentals/engage-and-retain/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json?v=$BUILD" />
    <meta http-equiv="x-dns-prefetch-control" content="on" />

    <link rel="shortcut icon" href="%PUBLIC_URL%/favicon.png" />

    <!--
      Notice the use of %PUBLIC_URL% in the tag above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.
      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <script>
      // prettier-ignore
      !function(e,i){var t=i.documentElement,d=e.devicePixelRatio||1;setTimeout(()=>{if(d>=2){var e=i.createElement("body"),a=i.createElement("div");a.style.border=".5px solid transparent",e.appendChild(a),t.appendChild(e),1===a.offsetHeight&&t.classList.add("hairlines"),t.removeChild(e)}})}(window,document);
    </script>
    <script>
      // prettier-ignore
      !function(){var t="pwa_history_bug",a=function t(){if(/iP(hone|od|ad)/.test(window.navigator.platform)||/iP(hone|od|ad)/.test(window.navigator.userAgent))try{var a=window.navigator.appVersion.match(/OS (d+)_(d+)_?(d+)?/);return[parseInt(a[1],10),parseInt(a[2],10),parseInt(a[3]||0,10)]}catch(n){}return[]}(),n=a&&a[0]&&a[0]<15;"standalone"in window.navigator&&window.navigator.standalone&&n&&!sessionStorage.getItem(t)&&(sessionStorage.setItem(t,"1"),window.history.pushState({},"",location.href))}();
    </script>

    <%= htmlWebpackPlugin.files.webpackManifest %>
  </head>

  <body>
    <div class="loading-img-container"></div>
    <div id="root"></div>

    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start`.
      To create a production bundle, use `npm run build`.
    -->
    <script>
      // prettier-ignore
      !function(){if(!CSS.supports("height","1dvh")){var e=.01*window.innerHeight,t=document.documentElement,n=e+"px";t.style.getPropertyValue("--vh")!==n&&t.style.setProperty("--vh",n)}}();
    </script>

    <script src="%PUBLIC_URL%/vendor.encrypt.v2.dll.js"></script>

    <script>
      document.write(
        "<script type='text/javascript' src='/mobile/mc/loadMemberCenter.js?v=" + new Date().getTime() + "'><\/script>"
      );
    </script>
  </body>
</html>
