{"name": "roytlbf6-mobile", "version": "1.0.0", "license": "UNLICENSED", "devDependencies": {"@babel/core": "^7.17.5", "@babel/eslint-parser": "^7.17.0", "@babel/plugin-proposal-class-properties": "^7.3.4", "@babel/plugin-proposal-decorators": "^7.3.0", "@babel/plugin-proposal-object-rest-spread": "^7.3.4", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.2.0", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@babel/preset-react": "^7.0.0", "@babel/runtime-corejs3": "^7.17.7", "@commitlint/cli": "^16", "@commitlint/config-conventional": "^16", "autoprefixer": "^9", "babel-eslint": "^7.2.3", "babel-jest": "20.0.3", "babel-loader": "^8.0.5", "babel-plugin-import": "^1.11.0", "browserslist": "^4.20.0", "case-sensitive-paths-webpack-plugin": "1.1.4", "copy-and-watch": "^0.1.2", "core-js": "3.33.2", "css-loader": "0.28.1", "dotenv": "4.0.0", "es6-promise": "^4.1.1", "eslint": "^7", "eslint-config-prettier": "^8", "eslint-config-react-app": "^6.0.0", "eslint-loader": "^4", "eslint-plugin-flowtype": "^2.34.1", "eslint-plugin-import": "^2.16.0", "eslint-plugin-jsx-a11y": "^6", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-react": "^7.4.0", "eslint-plugin-react-hooks": "^4.0.8", "eslint-plugin-simple-import-sort": "^10", "eslint-plugin-unused-imports": "^1", "extract-text-webpack-plugin": "^3.0.2", "file-loader": "0.11.1", "googleapis": "^49.0.0", "html-webpack-exclude-assets-plugin": "^0.0.7", "html-webpack-plugin": "^3.2.0", "husky": "^4", "image-webpack-loader": "^6.0.0", "imagemin": "^7.0.1", "imagemin-jpegtran": "^6.0.0", "imagemin-pngquant": "^8.0.0", "inline-manifest-webpack-plugin": "3.0.1", "is-webview": "^1.0.1", "jest": "20.0.3", "less-watch-compiler": "^1.10.2", "lint-staged": "^10.0.7", "node-sass": "npm:sass@1.49.9", "npm-run-all": "4.0.2", "object-assign": "4.1.1", "postcss": "^8.5.1", "postcss-flexbugs-fixes": "3.0.0", "postcss-html": "^1.8.0", "postcss-loader": "^2.0.8", "postcss-pxtorem": "^5", "prettier": "3.4.2", "promise": "7.1.1", "prop-types": "^15.7.2", "react-dev-utils": "^3.0.0", "react-error-overlay": "^1.0.7", "sass-loader": "^7", "sass-resources-loader": "^2.0.3", "string-replace-loader": "^2.2.0", "style-loader": "0.17.0", "stylelint": "^16.14.1", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-standard-scss": "^14.0.0", "stylelint-order": "^6.0.4", "svg-sprite-loader": "0.3.1", "sw-precache-webpack-plugin": "^0.11.5", "uglifyjs-webpack-plugin": "1.3.0", "url-loader": "0.5.8", "webpack": "^3", "webpack-bundle-analyzer": "^2.13.1", "webpack-dev-server": "^2", "webpack-manifest-plugin": "1.1.0", "whatwg-fetch": "2.0.3", "workbox-webpack-plugin": "^4.3.1"}, "dependencies": {"@juggle/resize-observer": "^3.2.0", "antd-mobile": "^1.7.1", "axios": "^0.17.1", "classnames": "^2.2.6", "cross-env": "^7.0.3", "currencyformatter.js": "^1.0.5", "dayjs": "^1.11.9", "fastclick": "^1.0.6", "immutable": "^3.8.2", "is-webview": "^1.0.1", "js-base64": "^2.4.3", "js-cookie": "^3.0.5", "lodash": "^4.17.4", "mobx": "3.1.10", "mobx-react": "4.2.1", "mobx-react-devtools": "^4.2.13", "mobx-state-tree": "0.6.2", "pubsub-js": "1.8.0", "qrcode.react": "^0.7.2", "qs": "^6.5.1", "rc-form": "1.4.8", "react": "15.5.4", "react-addons-css-transition-group": "^15.6.2", "react-async-component": "^2.0.0", "react-copy-to-clipboard": "^5.0.1", "react-dom": "15.5.4", "react-draggable": "4.4.5", "react-iframe": "1.0.7", "react-imageloader": "^3.0.0", "react-loadable": "^5.4.0", "react-loading": "^2.0.3", "react-polyfill-portal": "^1.1.0", "react-router": "4.2.0", "react-router-cache-route": "^1.12.11", "react-router-dom": "4.2.2", "react-switch": "^2.3.0", "react-transition-group": "^2.2.1", "recharts": "^1.0.0-beta.5", "rmc-date-picker": "^6.0.5", "tcg-mobile-common": "^1.417.4"}, "scripts": {"start-js": "node scripts/start.js", "build-js": "node scripts/build.js", "start": "npm-run-all -p i18n start-js", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider npm-run-all build:dist", "build-local": "cross-env NODE_OPTIONS=--openssl-legacy-provider node scripts/build.js", "test": "node scripts/test.js --env=jsdom", "lint": "eslint src", "build-css": "less-watch-compiler --run-once --main-file=index.less src/stylesheets/theme src/stylesheets/theme", "watch-css": "npm run build-css && less-watch-compiler --main-file=index.less src/stylesheets/theme src/stylesheets/theme", "precommit": "lint-staged", "i18n": "node i18n/js/sync", "format": "prettier --write \"src/**/*.{js,jsx,json}\" --loglevel warn", "build:dist": "node scripts/build.js"}, "lint-staged": {"{src,public}/**/*.{css,scss,less,html}": ["stylelint --cache --color --fix --allow-empty-input", "prettier --write --ignore-unknown"], "*.{js,jsx}": ["prettier --write --ignore-unknown", "eslint --fix"], "!(*.js|*.jsx|*.css|*.scss|*.less|*.html)": ["prettier --write --ignore-unknown"]}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx}"], "setupFiles": ["<rootDir>/config/polyfills.js"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.js?(x)", "<rootDir>/src/**/?(*.)(spec|test).js?(x)"], "testEnvironment": "node", "testURL": "http://localhost", "transform": {"^.+\\.(js|jsx)$": "<rootDir>/node_modules/babel-jest", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx)$"], "moduleNameMapper": {"^react-native$": "react-native-web"}}, "proxy": "http://www.sit-dfstar.com/", "homepage": "/m", "language": "EN", "supportLanguages": ["EN", "TY"], "charset": "utf-8", "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "browserslist": ["> 0.2%, last 1 year, not dead, not op_mini all", "not edge >= 12", "not ie <= 11", "ios_saf >= 12", "Android >= 51", "safari >= 11"], "resolutions": {"browserslist": "^4.20.0", "ip": "1.1.5"}}