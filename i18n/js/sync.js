const fs = require("fs");
const readline = require("readline");
const { google } = require("googleapis");
const pkg = require("../../package.json");

// If modifying these scopes, delete token.json.
const SCOPES = ["https://www.googleapis.com/auth/spreadsheets.readonly"];
// The file token.json stores the user's access and refresh tokens, and is
// created automatically when the authorization flow completes for the first
// time.
const TOKEN_PATH = "./i18n/js/token.json";

// Load client secrets from a local file.
fs.readFile("./i18n/js/credentials.json", (err, content) => {
  if (err) return console.log("Error loading client secret file:", err);
  // Authorize a client with credentials, then call the Google Sheets API.
  authorize(JSON.parse(content), listMajors);
});

/**
 * Create an OAuth2 client with the given credentials, and then execute the
 * given callback function.
 * @param {Object} credentials The authorization client credentials.
 * @param {function} callback The callback to call with the authorized client.
 */
function authorize(credentials, callback) {
  const { client_secret, client_id, redirect_uris } = credentials.installed;
  const oAuth2Client = new google.auth.OAuth2(client_id, client_secret, redirect_uris[0]);

  // Check if we have previously stored a token.
  fs.readFile(TOKEN_PATH, (err, token) => {
    if (err) return getNewToken(oAuth2Client, callback);
    oAuth2Client.setCredentials(JSON.parse(token));
    callback(oAuth2Client);
  });
}

/**
 * Get and store new token after prompting for user authorization, and then
 * execute the given callback with the authorized OAuth2 client.
 * @param {google.auth.OAuth2} oAuth2Client The OAuth2 client to get token for.
 * @param {getEventsCallback} callback The callback for the authorized client.
 */
function getNewToken(oAuth2Client, callback) {
  const authUrl = oAuth2Client.generateAuthUrl({
    access_type: "offline",
    scope: SCOPES,
  });
  console.log("Authorize this app by visiting this url:", authUrl);
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });
  rl.question("Enter the code from that page here: ", (code) => {
    rl.close();
    oAuth2Client.getToken(code, (err, token) => {
      if (err) return console.error("Error while trying to retrieve access token", err);
      oAuth2Client.setCredentials(token);
      // Store the token to disk for later program executions
      fs.writeFile(TOKEN_PATH, JSON.stringify(token), (err) => {
        if (err) console.error(err);
        console.log("Token stored to", TOKEN_PATH);
      });
      callback(oAuth2Client);
    });
  });
}

const spreadsheetId = "1SmGcDrVthCeWi76typ61SxPB0fW8Qw7UYPeiGyNtQAs";

/**
 * Prints the names and majors of students in a sample spreadsheet:
 * @see https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit
 * @param {google.auth.OAuth2} auth The authenticated Google OAuth client.
 */
function listMajors(auth) {
  const sheets = google.sheets({ version: "v4", auth });
  sheets.spreadsheets.values.get(
    {
      spreadsheetId: spreadsheetId,
      range: "shell!A:V",
    },
    (err, res) => {
      if (err) return console.log("The API returned an error: " + err);
      const rows = res.data.values;
      if (rows.length) {
        const rest = rows.slice(1);
        const langs = rows[0].slice(1);
        langs.forEach((el, idx) => {
          const validHeader = el && /^[A-Za-z]{2}$/i.test(el);
          if (validHeader) {
            createI18nFile(el, rest, idx + 1);
          }
        });
      } else {
        console.log("No data found.");
      }
    }
  );
}

function createI18nFile(file, rest, index) {
  const content = rest.map((row) => {
    let key = row[0] || "";
    key = key.trim();
    if (!key && key.trim().length === 0) {
      return "";
    }
    if (key.trim().startsWith("//")) {
      return `  ${key.trim()}`;
    }
    let result = (row[index] || "").replace(/\\"/g, "&&&").replace(/"/g, '\\"').replace(/&&&/g, '\\"');
    return `  "${key}": "${result ? result : key}",`;
  });
  const json = `/* eslint-disable max-len, prettier/prettier */
const RESOURCES = {
${content.join("\n")}
};
export default RESOURCES;
`;

  fs.mkdir("./src/language/i18n", { recursive: true }, (err) => {
    if (err) throw err;
    if (pkg.supportLanguages.includes(file)) {
      fs.writeFile(`./src/language/i18n/${file}.js`, json, (err) => {
        if (err) throw err;
      });
    }
  });
}
